version: '3.8'

services:
  zookeeper:
    image: 'bitnami/zookeeper:3.8'
    container_name: ecp-zookeeper
    ports:
      - '2181:2181'
    environment:
      - ALLOW_ANONYMOUS_LOGIN=yes
      - TZ=Asia/Shanghai
    healthcheck:
      test: ["CMD", "zkServer.sh", "status"]
      interval: 30s
      timeout: 10s
      retries: 3

  kafka:
    image: 'bitnami/kafka:3.4'
    container_name: ecp-kafka
    depends_on:
      - zookeeper
    volumes:
      - './ecp-kafka/certs:/bitnami/kafka/config/certs:rw'
    ports:
      - '9092:9092'  # PLAINTEXT
      - '9093:9093'  # SSL
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_CFG_ZOOKEEPER_CONNECT=zookeeper:2181
      - ALLOW_PLAINTEXT_LISTENER=yes
      
      # 基本监听器配置
      - KAFKA_CFG_LISTENERS=PLAINTEXT://0.0.0.0:9092,SSL://0.0.0.0:9093
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://10.173.41.173:9092,SSL://10.173.41.173:9093
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,SSL:SSL
      
      # Inter-broker 通信使用 PLAINTEXT
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=PLAINTEXT
      
      # SSL 配置
      - KAFKA_CFG_SSL_KEYSTORE_LOCATION=/bitnami/kafka/config/certs/kafka.server.keystore.jks
      - KAFKA_CFG_SSL_KEYSTORE_PASSWORD=changeit
      - KAFKA_CFG_SSL_KEY_PASSWORD=changeit
      - KAFKA_CFG_SSL_TRUSTSTORE_LOCATION=/bitnami/kafka/config/certs/kafka.server.truststore.jks
      - KAFKA_CFG_SSL_TRUSTSTORE_PASSWORD=changeit
      - KAFKA_CFG_SSL_CLIENT_AUTH=required
      - KAFKA_CFG_SSL_ENDPOINT_IDENTIFICATION_ALGORITHM=
      
      # 性能优化
      - KAFKA_CFG_NUM_NETWORK_THREADS=3
      - KAFKA_CFG_NUM_IO_THREADS=8
      - KAFKA_CFG_SOCKET_SEND_BUFFER_BYTES=102400
      - KAFKA_CFG_SOCKET_RECEIVE_BUFFER_BYTES=102400
      - KAFKA_CFG_SOCKET_REQUEST_MAX_BYTES=104857600
      - KAFKA_CFG_NUM_PARTITIONS=1
      - KAFKA_CFG_NUM_RECOVERY_THREADS_PER_DATA_DIR=1
      - KAFKA_CFG_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - KAFKA_CFG_TRANSACTION_STATE_LOG_REPLICATION_FACTOR=1
      - KAFKA_CFG_TRANSACTION_STATE_LOG_MIN_ISR=1
      - KAFKA_CFG_LOG_FLUSH_INTERVAL_MESSAGES=10000
      - KAFKA_CFG_LOG_FLUSH_INTERVAL_MS=1000
      - KAFKA_CFG_LOG_RETENTION_HOURS=168
      - KAFKA_CFG_LOG_RETENTION_BYTES=**********
      - KAFKA_CFG_LOG_SEGMENT_BYTES=**********
      - KAFKA_CFG_LOG_RETENTION_CHECK_INTERVAL_MS=300000
      - KAFKA_CFG_LOG_CLEANUP_POLICY=delete
      
      # JVM 优化
      - KAFKA_HEAP_OPTS=-Xms512m -Xmx1536m
      - KAFKA_JVM_PERFORMANCE_OPTS=-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions
      
      - TZ=Asia/Shanghai
    
    healthcheck:
      test: ["CMD", "kafka-topics.sh", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
