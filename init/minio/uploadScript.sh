#!/usr/bin/env bash
set -euo pipefail
set -x
# 脚本执行前提，在执行环境安装minio client,
# 提前在宿主机中指定位置配置好files+assetMapping.csv+uploadScript,并挂载到容器指定路径
# === 配置参数 ===============================================================
MINIO_SERVER="http://ecp-minio:9000"
ACCESS_KEY="admin"
SECRET_KEY="capitalization@DTT#ecp"
# 容器内目录（与宿主机共享，通过挂载映射）
CONTAINER_DATA_DIR="/usr/local/init"          # 容器内数据目录
# 文件路径（直接使用挂载后的路径）
LOCAL_DIR="$CONTAINER_DATA_DIR/s3files"
MAPPING_CSV="$CONTAINER_DATA_DIR/assetMapping.csv"
# === 初始化 =================================================================
declare -A bucket_map path_map
# 安装 minio client
apt-get update && apt-get install -y wget
wget https://dl.min.io/client/mc/release/linux-amd64/mc -O /usr/local/bin/mc
chmod +x /usr/local/bin/mc

# 验证安装是否成功 csvkit
echo "Checking mc installation..."
if command -v mc &> /dev/null; then
    echo "mc installed successfully!"
else
    echo "Failed to install mc. Exiting..."
    exit 1
fi

# 安装 csvkit
echo "Installing csvkit..."
pip install csvkit

# 初始化mc client
if ! mc alias list | grep -q "$MINIO_SERVER"; then
    mc alias set minio-server "$MINIO_SERVER" "$ACCESS_KEY" "$SECRET_KEY"
fi
# === 初始化一组预置的aksk供应用访问
mc admin user add minio-server 'Lvt2LqwKul2edlWunXnY' 'G5EPnYR44Sv0qepMJfVYlGXv2gp1wVS9ACGBUdE8'
# === 初始化 minIO bucket
mc mb --ignore-existing minio-server/s3-atb-ecp-image-test &&
mc anonymous set public minio-server/s3-atb-ecp-image-test &&
mc mb --ignore-existing minio-server/s3-atb-ecp-file-test &&
mc anonymous set public minio-server/s3-atb-ecp-file-test &&
mc mb --ignore-existing minio-server/s3-atb-ecp-video-test &&
mc anonymous set public minio-server/s3-atb-ecp-video-test &&
# 初始化需要的文件夹
init_dirs() {
    echo "🛠️  检查目录挂载..."
    # 只需检查目录是否存在（挂载是否成功）
    if [[ ! -d "$CONTAINER_DATA_DIR" ]]; then
        mkdir CONTAINER_DATA_DIR
        mkdir LOCAL_DIR
    fi

    # 检查映射文件是否存在
    if [[ ! -f "$MAPPING_CSV" ]]; then
        echo "错误：映射文件不存在: $MAPPING_CSV"
        exit 1
    fi
}

# 验证安装是否成功 csvkit
echo "Checking csvkit installation..."
if command -v csvcut &> /dev/null; then
    echo "csvkit installed successfully!"
else
    echo "Failed to install csvkit. Exiting..."
    exit 1
fi


# === 加载CSV映射 ===========================================================
csv_data=$(csvcut --encoding=iso-8859-1 -c filename,bucket_name,minio_path "$MAPPING_CSV" | tail -n +2)

while IFS=, read -r filename bucket minio_path; do
    # 清理数据
    filename=$(echo "$filename" | tr -d '"' | xargs)
    bucket=$(echo "$bucket" | tr -d '"' | xargs)
    minio_path=$(echo "$minio_path" | tr -d '"' | xargs)

    if [[ -n "$filename" && -n "$bucket" && -n "$minio_path" ]]; then
        bucket_map["$filename"]="$bucket"
        path_map["$filename"]="$minio_path"
    fi
done <<< "$csv_data"

# === 打印最终映射表 ========================================================
echo "=== 最终映射表 ==="
printf "%-30s %-20s %s\n" "文件名" "Bucket" "MinIO路径"
for filename in "${!bucket_map[@]}"; do
    printf "%-30s %-20s %s\n" "$filename" "${bucket_map[$filename]}" "${path_map[$filename]}"
done
echo "=== 共 ${#bucket_map[@]} 条有效映射 ==="

# === 文件上传 ==============================================================
echo "🔍 扫描本地文件: $LOCAL_DIR"
find "$LOCAL_DIR" -type f | while read -r file; do
    filename="${file##*/}"
    echo "处理文件: $filename (完整路径: $file)"

    if [[ -z "${bucket_map[$filename]:-}" ]]; then
        echo "⚠️  未找到映射: $filename"
        continue
    else
        echo "⚠️  映射: ${bucket_map[$filename]}"
    fi
    target="minio-server/${bucket_map[$filename]}/${path_map[$filename]#/}"
    echo "⚠️  remotePath: $target"

    if mc cp "$file" "$target"; then
        echo "✅ 成功: $filename → $target"

    else
        echo "❌ 失败: $filename (返回码: $?)"

    fi
done

# === 结果统计 ==============================================================
echo "=== 执行结果 ==="
