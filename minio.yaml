version: '3.8'

services:
  minio:
    image: minio/minio:RELEASE.2024-05-10T01-41-38Z   # 固定版本
    container_name: ecp-minio
    restart: always
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      TZ: Asia/Shanghai
    command: server /data --console-address ":9001"
    volumes:
      - ${ROOT_ENV_DIR}/ecp-minio/data:/data
      - ${ROOT_ENV_DIR}/ecp-minio/conf:/root/.minio

    # 内存限制 - 1024MB (MinIO主要依赖磁盘)
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.1'

    healthcheck:
      test: ["CMD-SHELL", "exec 3<>/dev/tcp/localhost/9000 || exit 1"]
      interval: 20s
      timeout: 5s
      retries: 5
      start_period: 20s

  minio-init:
    image: python:3.11-slim
    container_name: ecp-minio-init
    depends_on:
      minio:
        condition: service_healthy
    volumes:
      - ${ROOT_ENV_DIR}/init/minio:/usr/local/init
    entrypoint: ["/usr/local/init/uploadScript.sh"]