#!/bin/bash

echo "=== Simple Kafka Test ==="

# Test 1: Create a topic via PLAINTEXT
echo "1. Creating test topic via PLAINTEXT..."
timeout 10 docker exec ecp-kafka kafka-topics.sh --bootstrap-server localhost:9092 --create --topic test-ssl --partitions 1 --replication-factor 1 --if-not-exists
echo "Topic creation result: $?"

echo ""

# Test 2: List topics via PLAINTEXT
echo "2. Listing topics via PLAINTEXT..."
timeout 10 docker exec ecp-kafka kafka-topics.sh --bootstrap-server localhost:9092 --list
echo "List topics result: $?"

echo ""

# Test 3: Send a message via PLAINTEXT
echo "3. Sending message via PLAINTEXT..."
echo "Hello Kafka!" | timeout 10 docker exec -i ecp-kafka kafka-console-producer.sh --bootstrap-server localhost:9092 --topic test-ssl
echo "Send message result: $?"

echo ""

# Test 4: Consume message via PLAINTEXT
echo "4. Consuming message via PLAINTEXT..."
timeout 5 docker exec ecp-kafka kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic test-ssl --from-beginning --max-messages 1
echo "Consume message result: $?"

echo ""
echo "=== Test completed ==="
