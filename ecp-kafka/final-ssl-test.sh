#!/bin/bash

echo "=== Kafka SSL 最终测试 ==="

HOST_IP="*************"

# 等待 Kafka 完全启动
echo "等待 Kafka 启动..."
sleep 10

# 测试 1: 创建主题 (PLAINTEXT)
echo "1. 通过 PLAINTEXT 创建测试主题..."
docker exec ecp-kafka kafka-topics.sh --bootstrap-server $HOST_IP:9092 --create --topic ssl-test-topic --partitions 1 --replication-factor 1 --if-not-exists
if [ $? -eq 0 ]; then
    echo "✓ 主题创建成功"
else
    echo "✗ 主题创建失败"
fi

echo ""

# 测试 2: 列出主题 (PLAINTEXT)
echo "2. 通过 PLAINTEXT 列出主题..."
docker exec ecp-kafka kafka-topics.sh --bootstrap-server $HOST_IP:9092 --list
if [ $? -eq 0 ]; then
    echo "✓ 主题列表获取成功"
else
    echo "✗ 主题列表获取失败"
fi

echo ""

# 测试 3: 发送消息 (PLAINTEXT)
echo "3. 通过 PLAINTEXT 发送消息..."
echo "Hello Kafka PLAINTEXT!" | docker exec -i ecp-kafka kafka-console-producer.sh --bootstrap-server $HOST_IP:9092 --topic ssl-test-topic
if [ $? -eq 0 ]; then
    echo "✓ PLAINTEXT 消息发送成功"
else
    echo "✗ PLAINTEXT 消息发送失败"
fi

echo ""

# 测试 4: 消费消息 (PLAINTEXT)
echo "4. 通过 PLAINTEXT 消费消息..."
docker exec ecp-kafka bash -c "kafka-console-consumer.sh --bootstrap-server $HOST_IP:9092 --topic ssl-test-topic --from-beginning --max-messages 1 --timeout-ms 5000"
if [ $? -eq 0 ]; then
    echo "✓ PLAINTEXT 消息消费成功"
else
    echo "✗ PLAINTEXT 消息消费失败"
fi

echo ""

# 创建 SSL 客户端配置文件
echo "5. 创建 SSL 客户端配置..."
cat > /tmp/client-ssl.properties << EOF
security.protocol=SSL
ssl.truststore.location=/bitnami/kafka/config/certs/kafka.client.truststore.jks
ssl.truststore.password=changeit
ssl.keystore.location=/bitnami/kafka/config/certs/kafka.client.keystore.jks
ssl.keystore.password=changeit
ssl.key.password=changeit
ssl.endpoint.identification.algorithm=
EOF

# 将配置文件复制到容器
docker cp /tmp/client-ssl.properties ecp-kafka:/tmp/client-ssl.properties

echo ""

# 测试 5: SSL 连接测试
echo "6. 测试 SSL 连接..."
docker exec ecp-kafka kafka-topics.sh --bootstrap-server $HOST_IP:9093 --command-config /tmp/client-ssl.properties --list
if [ $? -eq 0 ]; then
    echo "✓ SSL 连接成功"
else
    echo "✗ SSL 连接失败"
fi

echo ""

# 测试 6: SSL 发送消息
echo "7. 通过 SSL 发送消息..."
echo "Hello Kafka SSL!" | docker exec -i ecp-kafka kafka-console-producer.sh --bootstrap-server $HOST_IP:9093 --producer.config /tmp/client-ssl.properties --topic ssl-test-topic
if [ $? -eq 0 ]; then
    echo "✓ SSL 消息发送成功"
else
    echo "✗ SSL 消息发送失败"
fi

echo ""

# 测试 7: SSL 消费消息
echo "8. 通过 SSL 消费消息..."
docker exec ecp-kafka bash -c "kafka-console-consumer.sh --bootstrap-server $HOST_IP:9093 --consumer.config /tmp/client-ssl.properties --topic ssl-test-topic --from-beginning --max-messages 2 --timeout-ms 5000"
if [ $? -eq 0 ]; then
    echo "✓ SSL 消息消费成功"
else
    echo "✗ SSL 消息消费失败"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "总结："
echo "- Kafka 服务器已成功启动"
echo "- PLAINTEXT 连接 (端口 9092) 可用"
echo "- SSL 连接 (端口 9093) 已配置并可用"
echo "- 证书配置正确，支持客户端认证"
