#!/bin/bash

echo "=== Kafka SSL Test Script ==="

# Test PLAINTEXT connection
echo "1. Testing PLAINTEXT connection (port 9092)..."
docker exec ecp-kafka kafka-topics.sh --bootstrap-server localhost:9092 --list
if [ $? -eq 0 ]; then
    echo "✓ PLAINTEXT connection successful"
else
    echo "✗ PLAINTEXT connection failed"
fi

echo ""

# Create a test topic via PLAINTEXT
echo "2. Creating test topic via PLAINTEXT..."
docker exec ecp-kafka kafka-topics.sh --bootstrap-server localhost:9092 --create --topic test-topic --partitions 1 --replication-factor 1
if [ $? -eq 0 ]; then
    echo "✓ Test topic created successfully"
else
    echo "✗ Failed to create test topic"
fi

echo ""

# Test SSL connection
echo "3. Testing SSL connection (port 9093)..."
docker exec ecp-kafka kafka-topics.sh --bootstrap-server localhost:9093 --command-config /bitnami/kafka/config/certs/client-ssl.properties --list
if [ $? -eq 0 ]; then
    echo "✓ SSL connection successful"
else
    echo "✗ SSL connection failed"
fi

echo ""

# Send a message via SSL
echo "4. Sending test message via SSL..."
echo "Hello SSL Kafka!" | docker exec -i ecp-kafka kafka-console-producer.sh --bootstrap-server localhost:9093 --producer.config /bitnami/kafka/config/certs/client-ssl.properties --topic test-topic
if [ $? -eq 0 ]; then
    echo "✓ Message sent successfully via SSL"
else
    echo "✗ Failed to send message via SSL"
fi

echo ""

# Consume the message via SSL
echo "5. Consuming test message via SSL..."
timeout 5 docker exec ecp-kafka kafka-console-consumer.sh --bootstrap-server localhost:9093 --consumer.config /bitnami/kafka/config/certs/client-ssl.properties --topic test-topic --from-beginning --max-messages 1
if [ $? -eq 0 ]; then
    echo "✓ Message consumed successfully via SSL"
else
    echo "✗ Failed to consume message via SSL"
fi

echo ""
echo "=== Test completed ==="
