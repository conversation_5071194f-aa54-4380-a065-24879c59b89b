#!/bin/bash

echo "=== Final Kafka SSL Test ==="

# Test using external tools
echo "1. Testing with kafkacat (if available)..."
if command -v kafkacat &> /dev/null; then
    echo "Testing PLAINTEXT connection..."
    echo "test message" | kafkacat -P -b localhost:9092 -t test-topic
    echo "PLAINTEXT test result: $?"
    
    echo "Testing SSL connection..."
    echo "test ssl message" | kafkacat -P -b localhost:9093 -t test-topic \
        -X security.protocol=SSL \
        -X ssl.ca.location=ecp-kafka/certs/ca.crt \
        -X ssl.certificate.location=ecp-kafka/certs/kafka.client.crt \
        -X ssl.key.location=ecp-kafka/certs/kafka.client.key
    echo "SSL test result: $?"
else
    echo "kafkacat not available, skipping external test"
fi

echo ""

# Test by creating a simple producer/consumer inside container
echo "2. Testing with simple producer/consumer..."

# Create a test topic first
echo "Creating test topic..."
docker exec ecp-kafka bash -c "
    export KAFKA_OPTS='-Djava.net.preferIPv4Stack=true'
    kafka-topics.sh --bootstrap-server *************:9092 --create --topic final-test --partitions 1 --replication-factor 1 --if-not-exists
"
echo "Topic creation result: $?"

echo ""

# Send a message
echo "Sending message..."
echo "Hello from final test!" | docker exec -i ecp-kafka bash -c "
    export KAFKA_OPTS='-Djava.net.preferIPv4Stack=true'
    kafka-console-producer.sh --bootstrap-server *************:9092 --topic final-test
"
echo "Send message result: $?"

echo ""

# Consume the message
echo "Consuming message..."
docker exec ecp-kafka bash -c "
    export KAFKA_OPTS='-Djava.net.preferIPv4Stack=true'
    timeout 5 kafka-console-consumer.sh --bootstrap-server *************:9092 --topic final-test --from-beginning --max-messages 1
"
echo "Consume message result: $?"

echo ""
echo "=== Test completed ==="
