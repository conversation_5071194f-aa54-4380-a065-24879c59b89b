#!/bin/bash

# Kafka SSL Setup Script
# This script creates Java keystores and truststores from PEM certificates

CERT_DIR="./certs"
PASSWORD="changeit"

echo "Setting up Kafka SSL certificates..."

# Create server keystore
echo "Creating server keystore..."
openssl pkcs12 -export \
    -in ${CERT_DIR}/kafka.server.crt \
    -inkey ${CERT_DIR}/kafka.server.key \
    -out ${CERT_DIR}/kafka.server.p12 \
    -name kafka-server \
    -password pass:${PASSWORD}

keytool -importkeystore \
    -deststorepass ${PASSWORD} \
    -destkeypass ${PASSWORD} \
    -destkeystore ${CERT_DIR}/kafka.server.keystore.jks \
    -srckeystore ${CERT_DIR}/kafka.server.p12 \
    -srcstoretype PKCS12 \
    -srcstorepass ${PASSWORD} \
    -alias kafka-server

# Create client keystore
echo "Creating client keystore..."
openssl pkcs12 -export \
    -in ${CERT_DIR}/kafka.client.crt \
    -inkey ${CERT_DIR}/kafka.client.key \
    -out ${CERT_DIR}/kafka.client.p12 \
    -name kafka-client \
    -password pass:${PASSWORD}

keytool -importkeystore \
    -deststorepass ${PASSWORD} \
    -destkeypass ${PASSWORD} \
    -destkeystore ${CERT_DIR}/kafka.client.keystore.jks \
    -srckeystore ${CERT_DIR}/kafka.client.p12 \
    -srcstoretype PKCS12 \
    -srcstorepass ${PASSWORD} \
    -alias kafka-client

# Create server truststore
echo "Creating server truststore..."
keytool -import \
    -file ${CERT_DIR}/ca.crt \
    -alias ca-cert \
    -keystore ${CERT_DIR}/kafka.server.truststore.jks \
    -storepass ${PASSWORD} \
    -noprompt

# Create client truststore
echo "Creating client truststore..."
keytool -import \
    -file ${CERT_DIR}/ca.crt \
    -alias ca-cert \
    -keystore ${CERT_DIR}/kafka.client.truststore.jks \
    -storepass ${PASSWORD} \
    -noprompt

echo "SSL setup completed!"
echo "Generated files:"
echo "  - kafka.server.keystore.jks"
echo "  - kafka.server.truststore.jks"
echo "  - kafka.client.keystore.jks"
echo "  - kafka.client.truststore.jks"
