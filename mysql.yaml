version: '3.8'
services:
  mysql: # 服务名称
    image: mysql:8.4.5 # 或其它mysql版本
    container_name: ecp-mysql # 容器名称
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD} # root用户密码
      - TZ=Asia/Shanghai # 设置容器时区
      # MySQL内存优化配置
      - MYSQL_INNODB_BUFFER_POOL_SIZE=1536M  # InnoDB缓冲池大小
      - MYSQL_INNODB_LOG_BUFFER_SIZE=64M     # 日志缓冲区大小
      - MYSQL_MAX_CONNECTIONS=200            # 最大连接数
      - MYSQL_QUERY_CACHE_SIZE=128M          # 查询缓存大小
    volumes:
      - ${ROOT_ENV_DIR}/ecp-mysql/log:/var/log/mysql # 映射日志目录，宿主机:容器
      - ${ROOT_ENV_DIR}/ecp-mysql/data:/var/lib/mysql # 映射数据目录，宿主机:容器
      - ${ROOT_ENV_DIR}/ecp-mysql/conf.d:/etc/mysql/conf.d # 映射配置目录，宿主机:容器
      - ${ROOT_ENV_DIR}/init/mysql:/docker-entrypoint-initdb.d # 初始化数据库脚本
      - /etc/localtime:/etc/localtime:ro # 让容器的时钟与宿主机时钟同步，避免时间的问题，ro是read only的意思，就是只读。
    ports:
      - "3306:3306" # 指定宿主机端口与容器端口映射关系，宿主机:容器
    restart: always # 容器随docker启动自启

    # 内存限制 - 2.5GB
    deploy:
      resources:
        limits:
          memory: 2048M
          cpus: '1.0'
        reservations:
          memory: 1024M
          cpus: '0.5'

    healthcheck:
      test: ["CMD-SHELL", "mysqladmin ping -h 127.0.0.1 -p${MYSQL_ROOT_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5
