create table ecp_system.infra_file_content
(
    id           bigint auto_increment comment '编号'
        primary key,
    config_id    bigint                                not null comment '配置编号',
    path         varchar(512)                          not null comment '文件路径',
    content      mediumblob                            not null comment '文件内容',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision     bigint      default 1                 not null comment '版本号'
)
    comment '文件表' collate = utf8mb4_unicode_ci;

create table ecp_system.member_level
(
    id               bigint auto_increment comment '编号'
        primary key,
    name             varchar(30)  default ''                not null comment '等级名称',
    level            int          default 0                 not null comment '等级',
    experience       int          default 0                 not null comment '升级经验',
    discount_percent tinyint      default 100               not null comment '享受折扣',
    icon             varchar(255) default ''                not null comment '等级图标',
    background_url   varchar(255) default ''                not null comment '等级背景图',
    status           tinyint      default 0                 not null comment '状态',
    creator          varchar(64)  default ''                null comment '创建者',
    create_time      datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updater          varchar(64)  default ''                null comment '更新者',
    update_time      datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted          bit          default b'0'              not null comment '是否删除',
    tenant_id        bigint       default 0                 not null comment '租户编号'
)
    comment '会员等级' collate = utf8mb4_unicode_ci;

create table ecp_system.member_level_record
(
    id               bigint auto_increment comment '编号'
        primary key,
    user_id          bigint       default 0                 not null comment '用户编号',
    level_id         bigint       default 0                 not null comment '等级编号',
    level            int          default 0                 not null comment '会员等级',
    discount_percent tinyint      default 100               not null comment '享受折扣',
    experience       int          default 0                 not null comment '升级经验',
    user_experience  int          default 0                 not null comment '会员此时的经验',
    remark           varchar(255) default ''                not null comment '备注',
    description      varchar(255) default ''                not null comment '描述',
    creator          varchar(64)  default ''                null comment '创建者',
    create_time      datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updater          varchar(64)  default ''                null comment '更新者',
    update_time      datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted          bit          default b'0'              not null comment '是否删除',
    tenant_id        bigint       default 0                 not null comment '租户编号'
)
    comment '会员等级记录' collate = utf8mb4_unicode_ci;

create index idx_user_id
    on ecp_system.member_level_record (user_id)
    comment '会员等级记录-用户编号';

create table ecp_system.member_point_config
(
    id                      bigint auto_increment comment '自增主键'
        primary key,
    trade_deduct_enable     bit                                                              not null comment '是否开启积分抵扣',
    trade_deduct_unit_price int                                                              not null comment '积分抵扣(单位：分)',
    trade_deduct_max_price  int                                                              null comment '积分抵扣最大值',
    trade_give_point        bigint                                                           null comment '1 元赠送多少分',
    creator                 varchar(64) collate utf8mb4_unicode_ci default ''                null comment '创建者',
    create_time             datetime                               default CURRENT_TIMESTAMP not null comment '创建时间',
    updater                 varchar(64) collate utf8mb4_unicode_ci default ''                null comment '更新者',
    update_time             datetime                               default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                 bit                                    default b'0'              not null comment '是否删除',
    tenant_id               bigint                                 default 0                 not null comment '租户编号'
)
    comment '会员积分配置表';

create table ecp_system.member_point_record
(
    id          bigint auto_increment comment '自增主键'
        primary key,
    user_id     bigint                                                           not null comment '用户编号',
    biz_id      varchar(255)                                                     not null comment '业务编码',
    biz_type    tinyint                                                          not null comment '业务类型',
    title       varchar(255)                                                     not null comment '积分标题',
    description varchar(5000)                                                    null comment '积分描述',
    point       int                                                              not null comment '积分',
    total_point int                                                              not null comment '变动后的积分',
    creator     varchar(64) collate utf8mb4_unicode_ci default ''                null comment '创建者',
    create_time datetime                               default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     varchar(64) collate utf8mb4_unicode_ci default ''                null comment '更新者',
    update_time datetime                               default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted     bit                                    default b'0'              not null comment '是否删除',
    tenant_id   bigint                                 default 0                 not null comment '租户编号'
)
    comment '用户积分记录';

create index index_title
    on ecp_system.member_point_record (title);

create index index_userId
    on ecp_system.member_point_record (user_id);

create table ecp_system.member_sign_in_config
(
    id          int auto_increment comment '编号'
        primary key,
    day         int                                                              not null comment '第几天',
    point       int                                                              not null comment '奖励积分',
    status      tinyint                                                          not null comment '状态',
    creator     varchar(64) collate utf8mb4_general_ci default ''                null comment '创建者',
    create_time datetime                               default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     varchar(64) collate utf8mb4_general_ci default ''                null comment '更新者',
    update_time datetime                               default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted     bit                                    default b'0'              not null comment '是否删除',
    tenant_id   bigint                                 default 0                 not null comment '租户编号'
)
    comment '签到规则';

create table ecp_system.member_sign_in_record
(
    id          bigint auto_increment comment '签到自增id'
        primary key,
    user_id     int           null comment '签到用户',
    day         int           null comment '第几天签到',
    point       int           null comment '签到的分数',
    create_time datetime      null comment '签到时间',
    update_time datetime      null on update CURRENT_TIMESTAMP comment '变更时间',
    tenant_id   varchar(255)  null comment '租户id',
    deleted     int default 0 null comment '是否删除',
    creator     varchar(255)  null comment '创建人',
    updater     varchar(255)  null comment '更新人'
)
    comment '签到记录';

create table ecp_system.member_tag
(
    id          bigint auto_increment comment '编号'
        primary key,
    name        varchar(30) default ''                not null comment '标签名称',
    creator     varchar(64) default ''                null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     varchar(64) default ''                null comment '更新者',
    update_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted     bit         default b'0'              not null comment '是否删除',
    tenant_id   bigint      default 0                 not null comment '租户编号'
)
    comment '会员标签' collate = utf8mb4_unicode_ci;

create table ecp_system.member_user
(
    id          bigint auto_increment comment '编号'
        primary key,
    mobile      varchar(11)                                                      not null comment '手机号',
    password    varchar(100)                           default ''                not null comment '密码',
    status      tinyint                                                          not null comment '状态',
    register_ip varchar(32)                                                      not null comment '注册 IP',
    login_ip    varchar(50) collate utf8mb4_unicode_ci default ''                null comment '最后登录IP',
    login_date  datetime                                                         null comment '最后登录时间',
    nickname    varchar(30)                            default ''                not null comment '用户昵称',
    avatar      varchar(255)                           default ''                not null comment '头像',
    name        varchar(30)                            default ''                null comment '真实名字',
    sex         tinyint                                default 0                 null comment '用户性别',
    area_id     bigint                                                           null comment '所在地',
    birthday    datetime                                                         null comment '出生日期',
    mark        varchar(255)                                                     null comment '会员备注',
    point       int                                    default 0                 not null comment '积分',
    tag_ids     varchar(255)                                                     null comment '用户标签编号列表，以逗号分隔',
    level_id    bigint                                                           null comment '等级编号',
    experience  int                                    default 0                 not null comment '经验',
    group_id    bigint                                                           null comment '用户分组编号',
    creator     varchar(64)                            default ''                null comment '创建者',
    create_time datetime                               default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     varchar(64)                            default ''                null comment '更新者',
    update_time datetime                               default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted     bit                                    default b'0'              not null comment '是否删除',
    tenant_id   bigint                                 default 0                 not null comment '租户编号',
    constraint uk_mobile
        unique (mobile) comment '手机号'
)
    comment '会员用户' collate = utf8mb4_general_ci;

create table ecp_system.pay_app
(
    id                bigint auto_increment comment '应用编号'
        primary key,
    name              varchar(64)                           not null comment '应用名',
    status            tinyint                               not null comment '开启状态',
    remark            varchar(255)                          null comment '备注',
    order_notify_url  varchar(1024)                         not null comment '支付结果的回调地址',
    refund_notify_url varchar(1024)                         not null comment '退款结果的回调地址',
    creator           varchar(64) default ''                null comment '创建者',
    create_time       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater           varchar(64) default ''                null comment '更新者',
    update_time       datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted           bit         default b'0'              not null comment '是否删除',
    tenant_id         bigint      default 0                 not null comment '租户编号'
)
    comment '支付应用信息' collate = utf8mb4_unicode_ci;

create table ecp_system.pay_channel
(
    id          bigint auto_increment comment '商户编号'
        primary key,
    code        varchar(32)                           not null comment '渠道编码',
    status      tinyint                               not null comment '开启状态',
    remark      varchar(255)                          null comment '备注',
    fee_rate    double      default 0                 not null comment '渠道费率，单位：百分比',
    app_id      bigint                                not null comment '应用编号',
    config      varchar(8192)                         not null comment '支付渠道配置',
    creator     varchar(64) default ''                null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     varchar(64) default ''                null comment '更新者',
    update_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted     bit         default b'0'              not null comment '是否删除',
    tenant_id   bigint      default 0                 not null comment '租户编号'
)
    comment '支付渠道
' collate = utf8mb4_unicode_ci;

create table ecp_system.pay_demo_order
(
    id               bigint auto_increment comment '订单编号'
        primary key,
    user_id          bigint unsigned                       not null comment '用户编号',
    spu_id           bigint                                not null comment '商品编号',
    spu_name         varchar(255)                          not null comment '商品名字',
    price            int                                   not null comment '价格，单位：分',
    pay_status       bit         default b'0'              not null comment '是否已支付：[0:未支付 1:已经支付过]',
    pay_order_id     bigint                                null comment '支付订单编号',
    pay_channel_code varchar(16)                           null comment '支付成功的支付渠道',
    pay_time         datetime                              null comment '订单支付时间',
    pay_refund_id    bigint                                null comment '退款订单编号',
    refund_price     int         default 0                 not null comment '退款金额，单位：分',
    refund_time      datetime                              null comment '退款时间',
    creator          varchar(64) default ''                null comment '创建者',
    create_time      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater          varchar(64) default ''                null comment '更新者',
    update_time      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted          bit         default b'0'              not null comment '是否删除',
    tenant_id        bigint      default 0                 not null comment '租户编号'
)
    comment '示例订单
' collate = utf8mb4_bin;

create table ecp_system.pay_notify_log
(
    id           bigint auto_increment comment '日志编号'
        primary key,
    task_id      bigint                                not null comment '通知任务编号',
    notify_times tinyint                               not null comment '第几次被通知',
    response     varchar(2048)                         not null comment '请求参数',
    status       tinyint                               not null comment '通知状态',
    creator      varchar(64) default ''                null comment '创建者',
    create_time  datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater      varchar(64) default ''                null comment '更新者',
    update_time  datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted      bit         default b'0'              not null comment '是否删除',
    tenant_id    bigint      default 0                 not null comment '租户编号'
)
    comment '支付通知 App 的日志' collate = utf8mb4_unicode_ci;

create table ecp_system.pay_notify_task
(
    id                bigint auto_increment comment '任务编号'
        primary key,
    app_id            bigint                                not null comment '应用编号',
    type              tinyint                               not null comment '通知类型',
    data_id           bigint                                not null comment '数据编号',
    status            tinyint                               not null comment '通知状态',
    merchant_order_id varchar(64)                           not null comment '商户订单编号',
    next_notify_time  datetime    default CURRENT_TIMESTAMP not null comment '下一次通知时间',
    last_execute_time datetime    default CURRENT_TIMESTAMP not null comment '最后一次执行时间',
    notify_times      tinyint                               not null comment '当前通知次数',
    max_notify_times  tinyint                               not null comment '最大可通知次数',
    notify_url        varchar(1024)                         not null comment '异步通知地址',
    creator           varchar(64) default ''                null comment '创建者',
    create_time       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater           varchar(64) default ''                null comment '更新者',
    update_time       datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted           bit         default b'0'              not null comment '是否删除',
    tenant_id         bigint      default 0                 not null comment '租户编号'
)
    comment '商户支付、退款等的通知
' collate = utf8mb4_unicode_ci;

create table ecp_system.pay_order
(
    id                bigint auto_increment comment '支付订单编号'
        primary key,
    app_id            bigint                                not null comment '应用编号',
    channel_id        bigint                                null comment '渠道编号',
    channel_code      varchar(32)                           null comment '渠道编码',
    merchant_order_id varchar(64)                           not null comment '商户订单编号',
    subject           varchar(32)                           not null comment '商品标题',
    body              varchar(128)                          not null comment '商品描述',
    notify_url        varchar(1024)                         not null comment '异步通知地址',
    price             bigint                                not null comment '支付金额，单位：分',
    channel_fee_rate  double      default 0                 null comment '渠道手续费，单位：百分比',
    channel_fee_price bigint      default 0                 null comment '渠道手续金额，单位：分',
    status            tinyint                               not null comment '支付状态',
    user_ip           varchar(50)                           not null comment '用户 IP',
    expire_time       datetime    default CURRENT_TIMESTAMP not null comment '订单失效时间',
    success_time      datetime    default CURRENT_TIMESTAMP null comment '订单支付成功时间',
    extension_id      bigint                                null comment '支付成功的订单拓展单编号',
    no                varchar(64)                           null comment '支付订单号',
    refund_price      bigint                                not null comment '退款总金额，单位：分',
    channel_user_id   varchar(255)                          null comment '渠道用户编号',
    channel_order_no  varchar(64)                           null comment '渠道订单号',
    creator           varchar(64) default ''                null comment '创建者',
    create_time       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater           varchar(64) default ''                null comment '更新者',
    update_time       datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted           bit         default b'0'              not null comment '是否删除',
    tenant_id         bigint      default 0                 not null comment '租户编号'
)
    comment '支付订单
' collate = utf8mb4_unicode_ci;

create table ecp_system.pay_order_extension
(
    id                  bigint auto_increment comment '支付订单编号'
        primary key,
    no                  varchar(64)                           not null comment '支付订单号',
    order_id            bigint                                not null comment '支付订单编号',
    channel_id          bigint                                not null comment '渠道编号',
    channel_code        varchar(32)                           not null comment '渠道编码',
    user_ip             varchar(50)                           not null comment '用户 IP',
    status              tinyint                               not null comment '支付状态',
    channel_extras      varchar(256)                          null comment '支付渠道的额外参数',
    channel_error_code  varchar(128)                          null comment '渠道调用报错时，错误码',
    channel_error_msg   varchar(256)                          null comment '渠道调用报错时，错误信息',
    channel_notify_data varchar(4096)                         null comment '支付渠道异步通知的内容',
    creator             varchar(64) default ''                null comment '创建者',
    create_time         datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater             varchar(64) default ''                null comment '更新者',
    update_time         datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted             bit         default b'0'              not null comment '是否删除',
    tenant_id           bigint      default 0                 not null comment '租户编号'
)
    comment '支付订单
' collate = utf8mb4_unicode_ci;

create table ecp_system.pay_refund
(
    id                  bigint auto_increment comment '支付退款编号'
        primary key,
    no                  varchar(64)                           not null comment '退款单号',
    app_id              bigint                                not null comment '应用编号',
    channel_id          bigint                                not null comment '渠道编号',
    channel_code        varchar(32)                           not null comment '渠道编码',
    order_id            bigint                                not null comment '支付订单编号 pay_order 表id',
    order_no            varchar(64)                           not null comment '支付订单 no',
    merchant_order_id   varchar(64)                           not null comment '商户订单编号（商户系统生成）',
    merchant_refund_id  varchar(64)                           not null comment '商户退款订单号（商户系统生成）',
    notify_url          varchar(1024)                         not null comment '异步通知商户地址',
    status              tinyint                               not null comment '退款状态',
    pay_price           bigint                                not null comment '支付金额,单位分',
    refund_price        bigint                                not null comment '退款金额,单位分',
    reason              varchar(256)                          not null comment '退款原因',
    user_ip             varchar(50)                           null comment '用户 IP',
    channel_order_no    varchar(64)                           not null comment '渠道订单号，pay_order 中的 channel_order_no 对应',
    channel_refund_no   varchar(64)                           null comment '渠道退款单号，渠道返回',
    success_time        datetime                              null comment '退款成功时间',
    channel_error_code  varchar(128)                          null comment '渠道调用报错时，错误码',
    channel_error_msg   varchar(256)                          null comment '渠道调用报错时，错误信息',
    channel_notify_data varchar(4096)                         null comment '支付渠道异步通知的内容',
    creator             varchar(64) default ''                null comment '创建者',
    create_time         datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater             varchar(64) default ''                null comment '更新者',
    update_time         datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted             bit         default b'0'              not null comment '是否删除',
    tenant_id           bigint      default 0                 not null comment '租户编号'
)
    comment '退款订单' collate = utf8mb4_unicode_ci;

create table ecp_system.report_go_view_project
(
    id          bigint auto_increment comment '编号'
        primary key,
    name        varchar(255)                          not null comment '项目名称',
    pic_url     varchar(1024)                         null comment '预览图片 URL',
    content     text                                  null comment '报表内容',
    status      tinyint                               not null comment '发布状态',
    remark      varchar(255)                          null comment '项目备注',
    creator     varchar(64) default ''                null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     varchar(64) default ''                null comment '更新者',
    update_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted     bit         default b'0'              not null comment '是否删除',
    tenant_id   bigint      default 0                 not null comment '租户编号'
)
    comment 'GoView 项目表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_dept
(
    id             bigint auto_increment comment '部门id'
        primary key,
    name           varchar(30) default ''                not null comment '部门名称',
    parent_id      bigint      default 0                 not null comment '父部门id',
    sort           int         default 0                 not null comment '显示顺序',
    leader_user_id bigint                                null comment '负责人',
    phone          varchar(11)                           null comment '联系电话',
    email          varchar(50)                           null comment '邮箱',
    status         tinyint                               not null comment '部门状态（0正常 1停用）',
    created_by     varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time   datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by     varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time   datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted     bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id      bigint      default 0                 not null comment '租户编号',
    revision       bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '部门表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_dict_data
(
    id           bigint auto_increment comment '字典编码'
        primary key,
    sort         int          default 0                 not null comment '字典排序',
    label        varchar(100) default ''                not null comment '字典标签',
    value        varchar(100) default ''                not null comment '字典键值',
    dict_type    varchar(100) default ''                not null comment '字典类型',
    status       tinyint      default 0                 not null comment '状态（0正常 1停用）',
    color_type   varchar(100) default ''                null comment '颜色类型',
    css_class    varchar(100) default ''                null comment 'css 样式',
    remark       varchar(500)                           null comment '备注',
    created_by   varchar(64)  default ''                not null comment '创建人',
    created_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by   varchar(64)  default ''                null comment '更新人',
    updated_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted   bit          default b'0'              not null comment '逻辑删除',
    revision     bigint       default 1                 not null comment '版本号'
)
    comment '字典数据表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_dict_type
(
    id           bigint auto_increment comment '字典主键'
        primary key,
    name         varchar(100) default ''                not null comment '字典名称',
    type         varchar(100) default ''                not null comment '字典类型',
    status       tinyint      default 0                 not null comment '状态（0正常 1停用）',
    remark       varchar(500)                           null comment '备注',
    created_by   varchar(64)  default ''                not null comment '创建人',
    created_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by   varchar(64)  default ''                null comment '更新人',
    updated_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted   bit          default b'0'              not null comment '逻辑删除',
    deleted_time datetime                               null comment '删除时间',
    revision     bigint       default 1                 not null comment '版本号',
    constraint dict_type
        unique (type)
)
    comment '字典类型表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_error_code
(
    id               bigint auto_increment comment '错误码编号'
        primary key,
    type             tinyint      default 0                 not null comment '错误码类型',
    application_name varchar(50)                            not null comment '应用名',
    code             int          default 0                 not null comment '错误码编码',
    message          varchar(512) default ''                not null comment '错误码错误提示',
    memo             varchar(512) default ''                null comment '备注',
    created_by       varchar(64)  default ''                not null comment '鍒涘缓鑰',
    created_time     datetime     default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by       varchar(64)  default ''                null comment '鏇存柊鑰',
    updated_time     datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted       bit          default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision         bigint       default 1                 not null comment '鐗堟湰鍙'
)
    comment '错误码表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_login_log
(
    id           bigint auto_increment comment '访问ID'
        primary key,
    log_type     bigint                                not null comment '日志类型',
    trace_id     varchar(64) default ''                not null comment '链路追踪编号',
    user_id      bigint      default 0                 not null comment '用户编号',
    user_type    tinyint     default 0                 not null comment '用户类型',
    username     varchar(50) default ''                not null comment '用户账号',
    result       tinyint                               not null comment '登陆结果',
    user_ip      varchar(50)                           not null comment '用户 IP',
    user_agent   varchar(512)                          not null comment '浏览器 UA',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id    bigint      default 0                 not null comment '租户编号',
    revision     bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '系统访问记录' collate = utf8mb4_unicode_ci;

create table ecp_system.system_mail_account
(
    id           bigint auto_increment comment '主键'
        primary key,
    mail         varchar(255)                          not null comment '邮箱',
    username     varchar(255)                          not null comment '用户名',
    password     varchar(255)                          not null comment '密码',
    host         varchar(255)                          not null comment 'SMTP 服务器域名',
    port         int                                   not null comment 'SMTP 服务器端口',
    ssl_enable   bit         default b'0'              not null comment '是否开启 SSL',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision     bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '邮箱账号表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_mail_log
(
    id                bigint auto_increment comment '编号'
        primary key,
    user_id           bigint                                null comment '用户编号',
    user_type         tinyint                               null comment '用户类型',
    to_mail           varchar(255)                          not null comment '接收邮箱地址',
    account_id        bigint                                not null comment '邮箱账号编号',
    from_mail         varchar(255)                          not null comment '发送邮箱地址',
    template_id       bigint                                not null comment '模板编号',
    template_code     varchar(63)                           not null comment '模板编码',
    template_nickname varchar(255)                          null comment '模版发送人名称',
    template_title    varchar(255)                          not null comment '邮件标题',
    template_content  varchar(10240)                        not null comment '邮件内容',
    template_params   varchar(255)                          not null comment '邮件参数',
    send_status       tinyint     default 0                 not null comment '发送状态',
    send_time         datetime                              null comment '发送时间',
    send_message_id   varchar(255)                          null comment '发送返回的消息 ID',
    send_exception    varchar(4096)                         null comment '发送异常',
    created_by        varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time      datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by        varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted        bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision          bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '邮件日志表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_mail_template
(
    id           bigint auto_increment comment '编号'
        primary key,
    name         varchar(63)                           not null comment '模板名称',
    code         varchar(63)                           not null comment '模板编码',
    account_id   bigint                                not null comment '发送的邮箱账号编号',
    nickname     varchar(255)                          null comment '发送人名称',
    title        varchar(255)                          not null comment '模板标题',
    content      varchar(10240)                        not null comment '模板内容',
    params       varchar(255)                          not null comment '参数数组',
    status       tinyint                               not null comment '开启状态',
    remark       varchar(255)                          null comment '备注',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision     bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '邮件模版表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_menu
(
    id             bigint auto_increment comment '菜单ID'
        primary key,
    name           varchar(50)                            not null comment '菜单名称',
    permission     varchar(100) default ''                not null comment '权限标识',
    type           tinyint                                not null comment '菜单类型',
    sort           int          default 0                 not null comment '显示顺序',
    parent_id      bigint       default 0                 not null comment '父菜单ID',
    path           varchar(200) default ''                null comment '路由地址',
    icon           varchar(100) default '#'               null comment '菜单图标',
    component      varchar(255)                           null comment '组件路径',
    component_name varchar(255)                           null comment '组件名',
    status         tinyint      default 0                 not null comment '菜单状态',
    visible        bit          default b'1'              not null comment '是否可见',
    keep_alive     bit          default b'1'              not null comment '是否缓存',
    always_show    bit          default b'1'              not null comment '是否总是显示',
    created_by     varchar(64)  default ''                not null comment '鍒涘缓鑰',
    created_time   datetime     default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by     varchar(64)  default ''                null comment '鏇存柊鑰',
    updated_time   datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted     bit          default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision       bigint       default 1                 not null comment '鐗堟湰鍙'
)
    comment '菜单权限表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_notice
(
    id           bigint auto_increment comment '公告ID'
        primary key,
    title        varchar(50)                           not null comment '公告标题',
    content      text                                  not null comment '公告内容',
    type         tinyint                               not null comment '公告类型（1通知 2公告）',
    status       tinyint     default 0                 not null comment '公告状态（0正常 1关闭）',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id    bigint      default 0                 not null comment '租户编号',
    revision     bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '通知公告表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_notify_message
(
    id                bigint auto_increment comment '用户ID'
        primary key,
    user_id           bigint                                not null comment '用户id',
    user_type         tinyint                               not null comment '用户类型',
    template_id       bigint                                not null comment '模版编号',
    template_code     varchar(64)                           not null comment '模板编码',
    template_nickname varchar(63)                           not null comment '模版发送人名称',
    template_content  varchar(1024)                         not null comment '模版内容',
    template_type     int                                   not null comment '模版类型',
    template_params   varchar(255)                          not null comment '模版参数',
    read_status       bit                                   not null comment '是否已读',
    read_time         datetime                              null comment '阅读时间',
    created_by        varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time      datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by        varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted        bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id         bigint      default 0                 not null comment '租户编号',
    revision          bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '站内信消息表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_notify_template
(
    id           bigint auto_increment comment '主键'
        primary key,
    name         varchar(63)                           not null comment '模板名称',
    code         varchar(64)                           not null comment '模版编码',
    nickname     varchar(255)                          not null comment '发送人名称',
    content      varchar(1024)                         not null comment '模版内容',
    type         tinyint                               not null comment '类型',
    params       varchar(255)                          null comment '参数数组',
    status       tinyint                               not null comment '状态',
    remark       varchar(255)                          null comment '备注',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision     bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '站内信模板表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_oauth2_access_token
(
    id            bigint auto_increment comment '编号'
        primary key,
    user_id       bigint                                not null comment '用户编号',
    user_type     tinyint                               not null comment '用户类型',
    access_token  varchar(255)                          not null comment '访问令牌',
    refresh_token varchar(32)                           not null comment '刷新令牌',
    client_id     varchar(255)                          not null comment '客户端编号',
    scopes        varchar(255)                          null comment '授权范围',
    expires_time  datetime                              not null comment '过期时间',
    created_by    varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time  datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by    varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time  datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted    bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id     bigint      default 0                 not null comment '租户编号',
    revision      bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment 'OAuth2 访问令牌' collate = utf8mb4_unicode_ci;

create table ecp_system.system_oauth2_approve
(
    id           bigint auto_increment comment '编号'
        primary key,
    user_id      bigint                                 not null comment '用户编号',
    user_type    tinyint                                not null comment '用户类型',
    client_id    varchar(255)                           not null comment '客户端编号',
    scope        varchar(255) default ''                not null comment '授权范围',
    approved     bit          default b'0'              not null comment '是否接受',
    expires_time datetime                               not null comment '过期时间',
    created_by   varchar(64)  default ''                not null comment '鍒涘缓鑰',
    created_time datetime     default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64)  default ''                null comment '鏇存柊鑰',
    updated_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit          default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id    bigint       default 0                 not null comment '租户编号',
    revision     bigint       default 1                 not null comment '鐗堟湰鍙'
)
    comment 'OAuth2 批准表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_oauth2_code
(
    id           bigint auto_increment comment '编号'
        primary key,
    user_id      bigint                                 not null comment '用户编号',
    user_type    tinyint                                not null comment '用户类型',
    code         varchar(32)                            not null comment '授权码',
    client_id    varchar(255)                           not null comment '客户端编号',
    scopes       varchar(255) default ''                null comment '授权范围',
    expires_time datetime                               not null comment '过期时间',
    redirect_uri varchar(255)                           null comment '可重定向的 URI 地址',
    state        varchar(255) default ''                not null comment '状态',
    created_by   varchar(64)  default ''                not null comment '鍒涘缓鑰',
    created_time datetime     default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64)  default ''                null comment '鏇存柊鑰',
    updated_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit          default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id    bigint       default 0                 not null comment '租户编号',
    revision     bigint       default 1                 not null comment '鐗堟湰鍙'
)
    comment 'OAuth2 授权码表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_oauth2_refresh_token
(
    id            bigint auto_increment comment '编号'
        primary key,
    user_id       bigint                                not null comment '用户编号',
    refresh_token varchar(32)                           not null comment '刷新令牌',
    user_type     tinyint                               not null comment '用户类型',
    client_id     varchar(255)                          not null comment '客户端编号',
    scopes        varchar(255)                          null comment '授权范围',
    expires_time  datetime                              not null comment '过期时间',
    created_by    varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time  datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by    varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time  datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted    bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id     bigint      default 0                 not null comment '租户编号',
    revision      bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment 'OAuth2 刷新令牌' collate = utf8mb4_unicode_ci;

create table ecp_system.system_post
(
    id           bigint auto_increment comment '岗位ID'
        primary key,
    code         varchar(64)                           not null comment '岗位编码',
    name         varchar(50)                           not null comment '岗位名称',
    sort         int                                   not null comment '显示顺序',
    status       tinyint                               not null comment '状态（0正常 1停用）',
    remark       varchar(500)                          null comment '备注',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id    bigint      default 0                 not null comment '租户编号',
    revision     bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '岗位信息表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_role
(
    id                  bigint auto_increment comment '角色ID'
        primary key,
    name                varchar(30)                            not null comment '角色名称',
    code                varchar(100)                           not null comment '角色权限字符串',
    sort                int                                    not null comment '显示顺序',
    data_scope          tinyint      default 1                 not null comment '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
    data_scope_dept_ids varchar(500) default ''                not null comment '数据范围(指定部门数组)',
    status              tinyint                                not null comment '角色状态（0正常 1停用）',
    type                tinyint                                not null comment '角色类型',
    remark              varchar(500)                           null comment '备注',
    created_by          varchar(64)  default ''                not null comment '鍒涘缓鑰',
    created_time        datetime     default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by          varchar(64)  default ''                null comment '鏇存柊鑰',
    updated_time        datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted          bit          default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id           bigint       default 0                 not null comment '租户编号',
    revision            bigint       default 1                 not null comment '鐗堟湰鍙'
)
    comment '角色信息表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_role_menu
(
    id           bigint auto_increment comment '自增编号'
        primary key,
    role_id      bigint                                not null comment '角色ID',
    menu_id      bigint                                not null comment '菜单ID',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id    bigint      default 0                 not null comment '租户编号',
    revision     bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '角色和菜单关联表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_sms_code
(
    id           bigint auto_increment comment '编号'
        primary key,
    mobile       varchar(11)                           not null comment '手机号',
    code         varchar(6)                            not null comment '验证码',
    create_ip    varchar(15)                           not null comment '创建 IP',
    scene        tinyint                               not null comment '发送场景',
    today_index  tinyint                               not null comment '今日发送的第几条',
    used         tinyint                               not null comment '是否使用',
    used_time    datetime                              null comment '使用时间',
    used_ip      varchar(255)                          null comment '使用 IP',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id    bigint      default 0                 not null comment '租户编号',
    revision     bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '手机验证码' collate = utf8mb4_unicode_ci;

create index idx_mobile
    on ecp_system.system_sms_code (mobile)
    comment '手机号';

create table ecp_system.system_sms_log
(
    id               bigint auto_increment comment '编号'
        primary key,
    channel_id       bigint                                not null comment '短信渠道编号',
    channel_code     varchar(63)                           not null comment '短信渠道编码',
    template_id      bigint                                not null comment '模板编号',
    template_code    varchar(63)                           not null comment '模板编码',
    template_type    tinyint                               not null comment '短信类型',
    template_content varchar(255)                          not null comment '短信内容',
    template_params  varchar(255)                          not null comment '短信参数',
    api_template_id  varchar(63)                           not null comment '短信 API 的模板编号',
    mobile           varchar(11)                           not null comment '手机号',
    user_id          bigint                                null comment '用户编号',
    user_type        tinyint                               null comment '用户类型',
    send_status      tinyint     default 0                 not null comment '发送状态',
    send_time        datetime                              null comment '发送时间',
    send_code        int                                   null comment '发送结果的编码',
    send_msg         varchar(255)                          null comment '发送结果的提示',
    api_send_code    varchar(63)                           null comment '短信 API 发送结果的编码',
    api_send_msg     varchar(255)                          null comment '短信 API 发送失败的提示',
    api_request_id   varchar(255)                          null comment '短信 API 发送返回的唯一请求 ID',
    api_serial_no    varchar(255)                          null comment '短信 API 发送返回的序号',
    receive_status   tinyint     default 0                 not null comment '接收状态',
    receive_time     datetime                              null comment '接收时间',
    api_receive_code varchar(63)                           null comment 'API 接收结果的编码',
    api_receive_msg  varchar(255)                          null comment 'API 接收结果的说明',
    created_by       varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time     datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by       varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time     datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted       bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision         bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '短信日志' collate = utf8mb4_unicode_ci;

create table ecp_system.system_sms_template
(
    id              bigint auto_increment comment '编号'
        primary key,
    type            tinyint                               not null comment '短信签名',
    status          tinyint                               not null comment '开启状态',
    code            varchar(63)                           not null comment '模板编码',
    name            varchar(63)                           not null comment '模板名称',
    content         varchar(255)                          not null comment '模板内容',
    params          varchar(255)                          not null comment '参数数组',
    remark          varchar(255)                          null comment '备注',
    api_template_id varchar(63)                           not null comment '短信 API 的模板编号',
    channel_id      bigint                                not null comment '短信渠道编号',
    channel_code    varchar(63)                           not null comment '短信渠道编码',
    created_by      varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time    datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by      varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted      bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision        bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '短信模板' collate = utf8mb4_unicode_ci;

create table ecp_system.system_social_user
(
    id             bigint unsigned auto_increment comment '主键(自增策略)'
        primary key,
    type           tinyint                               not null comment '社交平台的类型',
    openid         varchar(32)                           not null comment '社交 openid',
    token          varchar(256)                          null comment '社交 token',
    raw_token_info varchar(1024)                         not null comment '原始 Token 数据，一般是 JSON 格式',
    nickname       varchar(32)                           not null comment '用户昵称',
    avatar         varchar(255)                          null comment '用户头像',
    raw_user_info  varchar(1024)                         not null comment '原始用户数据，一般是 JSON 格式',
    code           varchar(256)                          not null comment '最后一次的认证 code',
    state          varchar(256)                          null comment '最后一次的认证 state',
    created_by     varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time   datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by     varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time   datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted     bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id      bigint      default 0                 not null comment '租户编号',
    revision       bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '社交用户表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_social_user_bind
(
    id             bigint unsigned auto_increment comment '主键(自增策略)'
        primary key,
    user_id        bigint                                not null comment '用户编号',
    user_type      tinyint                               not null comment '用户类型',
    social_type    tinyint                               not null comment '社交平台的类型',
    social_user_id bigint                                not null comment '社交用户的编号',
    created_by     varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time   datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by     varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time   datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted     bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id      bigint      default 0                 not null comment '租户编号',
    revision       bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '社交绑定表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_tenant
(
    id              bigint auto_increment comment '租户编号'
        primary key,
    name            varchar(30)                            not null comment '租户名',
    contact_user_id bigint                                 null comment '联系人的用户编号',
    contact_name    varchar(30)                            not null comment '联系人',
    contact_mobile  varchar(500)                           null comment '联系手机',
    status          tinyint      default 0                 not null comment '租户状态（0正常 1停用）',
    domain          varchar(256) default ''                null comment '绑定域名',
    package_id      bigint                                 not null comment '租户套餐编号',
    expire_time     datetime                               not null comment '过期时间',
    account_count   int                                    not null comment '账号数量',
    created_by      varchar(64)  default ''                not null comment '创建者',
    created_time    datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by      varchar(64)  default ''                null comment '更新者',
    updated_time    datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted      bit          default b'0'              not null comment '是否删除',
    revision        bigint       default 1                 not null comment '鐗堟湰鍙'
)
    comment '租户表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_tenant_package
(
    id           bigint auto_increment comment '套餐编号'
        primary key,
    name         varchar(30)                            not null comment '套餐名',
    status       tinyint      default 0                 not null comment '租户状态（0正常 1停用）',
    remark       varchar(256) default ''                null comment '备注',
    menu_ids     varchar(2048)                          not null comment '关联的菜单编号',
    created_by   varchar(64)  default ''                not null comment '创建者',
    created_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by   varchar(64)  default ''                null comment '更新者',
    updated_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted   bit          default b'0'              not null comment '是否删除',
    revision     bigint       default 1                 not null comment '鐗堟湰鍙'
)
    comment '租户套餐表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_user_post
(
    id           bigint auto_increment comment 'id'
        primary key,
    user_id      bigint      default 0                 not null comment '用户ID',
    post_id      bigint      default 0                 not null comment '岗位ID',
    created_by   varchar(64) default ''                null comment '创建者',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by   varchar(64) default ''                null comment '更新者',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted   bit         default b'0'              not null comment '是否删除',
    tenant_id    bigint      default 0                 not null comment '租户编号',
    revision     bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '用户岗位表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_user_role
(
    id           bigint auto_increment comment '自增编号'
        primary key,
    user_id      bigint                                not null comment '用户ID',
    role_id      bigint                                not null comment '角色ID',
    created_by   varchar(64) default ''                null comment '创建者',
    created_time datetime    default CURRENT_TIMESTAMP null comment '创建时间',
    updated_by   varchar(64) default ''                null comment '更新者',
    updated_time datetime    default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted   bit         default b'0'              null comment '是否删除',
    tenant_id    bigint      default 0                 not null comment '租户编号',
    revision     bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment '用户和角色关联表' collate = utf8mb4_unicode_ci;

create table ecp_system.system_users
(
    id           bigint auto_increment comment '用户ID'
        primary key,
    username     varchar(30)                            not null comment '用户账号',
    password     varchar(100) default ''                not null comment '密码',
    nickname     varchar(30)                            not null comment '用户昵称',
    remark       varchar(500)                           null comment '备注',
    dept_id      bigint                                 null comment '部门ID',
    post_ids     varchar(255)                           null comment '岗位编号数组',
    email        varchar(50)  default ''                null comment '用户邮箱',
    mobile       varchar(11)  default ''                null comment '手机号码',
    sex          tinyint      default 0                 null comment '用户性别',
    avatar       varchar(512) default ''                null comment '头像地址',
    status       tinyint      default 0                 not null comment '帐号状态（0正常 1停用）',
    login_ip     varchar(50)  default ''                null comment '最后登录IP',
    login_date   datetime                               null comment '最后登录时间',
    created_by   varchar(64)  default ''                null comment '创建者',
    created_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by   varchar(64)  default ''                null comment '更新者',
    updated_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted   bit          default b'0'              not null comment '是否删除;',
    tenant_id    bigint       default 0                 not null comment '租户编号',
    revision     bigint       default 1                 not null comment '鐗堟湰鍙',
    constraint idx_username
        unique (username, updated_time, tenant_id)
)
    comment '用户信息表' collate = utf8mb4_unicode_ci;

create table ecp_subscription.t_amap_activate_record
(
    id               bigint auto_increment comment '主键'
        primary key,
    upload_file_name varchar(255) not null comment '上传文件名;上传文件名',
    upload_s3_file   varchar(255) not null comment '上传文件地址;上传文件地址',
    result_s3_file   varchar(255) not null comment '处理结果文件地址;处理结果文件地址',
    upload_user_name varchar(90)  not null comment '上传用户名;上传用户名',
    upload_time      datetime     not null comment '上传时间;上传时间',
    tenant_id        int          not null comment '租户号',
    created_by       varchar(90)  not null comment '创建人',
    created_time     datetime     not null comment '创建时间',
    updated_by       varchar(90)  null comment '更新人',
    updated_time     datetime     null comment '更新时间',
    is_deleted       int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int          not null comment '乐观锁'
)
    comment 't_amap_activate_record' charset = utf8mb3
                                     row_format = DYNAMIC;

create table ecp_subscription.t_amap_initial_data
(
    id          bigint auto_increment comment '主键'
        primary key,
    car_vin     varchar(90) not null comment '车辆VIN;车辆VIN',
    expiry_date varchar(90) null comment '服务过期时间;服务过期时间'
)
    comment 't_amap_initial_data' charset = utf8mb3
                                  row_format = DYNAMIC;

create index t_amap_initial_data_car_vin_IDX
    on ecp_subscription.t_amap_initial_data (car_vin);

create table ecp_subscription.t_amap_query_batch_records
(
    id            bigint auto_increment comment '主键'
        primary key,
    batch_no      bigint      not null comment '批次号;批次号',
    verify_result int         not null comment '校验结果;校验结果 0：不通过 1：通过',
    verify_reason varchar(90) null comment '校验结果;校验不通过的原因',
    deal_status   int         not null comment '处理状态;处理状态 0：待处理 1：处理中 2：已处理',
    operator      varchar(90) not null comment '操作人账号;操作人账号',
    tenant_id     int         not null comment '租户号',
    created_by    varchar(90) not null comment '创建人',
    created_time  datetime    not null comment '创建时间',
    updated_by    varchar(90) null comment '更新人',
    updated_time  datetime    null comment '更新时间',
    is_deleted    int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int         not null comment '乐观锁'
)
    comment 't_amap_query_batch_records';

create table ecp_subscription.t_amap_query_records
(
    id               bigint auto_increment comment '主键'
        primary key,
    car_vin          varchar(90) not null comment 'VIN;VIN',
    renew_no         bigint      not null comment '续费编号;续费编号，对于批量续费就是批次号',
    query_status     int         not null comment '处理状态;处理状态 0：查询中 1：查询完成 2：查询失败',
    operator         varchar(90) not null comment '操作人账号;操作人账号',
    service_status   int         null comment '服务状态 0：未激活 2：过期 3：使用中;服务状态 0：未激活 2：过期 3：使用中',
    amap_expiry_date datetime    null comment '续费后到期日;续费后到期日',
    ecp_expiry_date  datetime    null comment 'ecp到期日;ecp到期日',
    result_code      varchar(90) null comment '高德查询结果;高德查询结果 1成功 其他值失败',
    error_desc       varchar(90) null comment '续费失败原因;续费失败原因',
    tenant_id        int         not null comment '租户号',
    created_by       varchar(90) not null comment '创建人',
    created_time     datetime    not null comment '创建时间',
    updated_by       varchar(90) null comment '更新人',
    updated_time     datetime    null comment '更新时间',
    is_deleted       int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int         not null comment '乐观锁'
)
    comment 't_amap_query_records';

create table ecp_subscription.t_amap_renew_batch_records
(
    id                 bigint auto_increment comment '主键'
        primary key,
    batch_no           bigint       not null comment '批次处理号;批次处理号，雪花算法ID',
    upload_file        varchar(255) not null comment '上传原始文件;上传原始文件S3文件URL',
    verify_result      int          not null comment '校验结果;校验结果 0：不通过 1：通过',
    deal_status        int          not null comment '处理状态;处理状态 0：待处理 1：已处理',
    verify_result_file varchar(255) null comment '校验结果文件;对于失败的校验结果S3文件URL',
    operator           varchar(90)  not null comment '操作人账号;操作人账号',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁'
)
    comment 't_amap_renew_batch_records';

create table ecp_subscription.t_amap_renew_records
(
    id                       bigint auto_increment comment '主键'
        primary key,
    car_vin                  varchar(90)  not null comment 'VIN;VIN',
    renew_year               int          not null comment '续费时长;续费时长 1：1年 3：3年',
    renew_no                 bigint       not null comment '续费编号;续费编号，对于批量续费就是批次号',
    cus_order_id             varchar(90)  null comment '客户订单号for高德续费，雪花算法ID',
    operator                 varchar(90)  not null comment '操作人账号;操作人账号',
    data_source              int          not null comment '数据来源;数据来源 1单次续费 2批量续费',
    renew_status             int          not null comment '续费状态 0：待续费 1：续费中 2：续费成功 3：续费失败',
    renew_before_expiry_date datetime     null comment '续费前到期日;续费前到期日',
    renew_after_expiry_date  datetime     null comment '续费后到期日;续费后到期日',
    order_result_code        varchar(90)  null comment '高德续费结果 1成功 其他值失败',
    query_result_code        varchar(90)  null comment '高德查询结果 1成功 其他值失败',
    error_desc               varchar(255) null comment '续费失败原因;续费失败原因',
    tenant_id                int          not null comment '租户号',
    created_by               varchar(90)  not null comment '创建人',
    created_time             datetime     not null comment '创建时间',
    updated_by               varchar(90)  null comment '更新人',
    updated_time             datetime     null comment '更新时间',
    is_deleted               int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                 int          not null comment '乐观锁'
)
    comment 't_amap_renew_records';

create table ecp_subscription.t_amap_temp_20240826
(
    vin varchar(100) not null comment 'VIN'
)
    collate = utf8mb4_general_ci;

create table ecp_payment.t_app_info
(
    id                bigint auto_increment comment '主键'
        primary key,
    app_no            varchar(90)  not null comment '应用编号;商户编号',
    app_name          varchar(90)  not null comment '应用名称;商户名称',
    app_desc          varchar(255) null comment '应用描述信息',
    app_id            varchar(90)  null comment 'appid信息;appid，用于识别应用ID',
    app_secret        varchar(255) null comment '应用密钥信息;appsecret，用于应用签名密钥',
    pay_notify_url    varchar(255) not null comment '支付通知回调地址',
    refund_notify_url varchar(255) not null comment '退款通知回调地址',
    tenant_id         int          not null comment '租户号',
    created_by        varchar(90)  not null comment '创建人',
    created_time      datetime     not null comment '创建时间',
    updated_by        varchar(90)  null comment '更新人',
    updated_time      datetime     null comment '更新时间',
    is_deleted        int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int          not null comment '乐观锁'
)
    comment 't_app_info' charset = utf8mb3
                         row_format = DYNAMIC;

create table ecp_subscription.t_appd_initial_data
(
    id                              bigint auto_increment comment '主键'
        primary key,
    ad_id                           varchar(255) charset utf8mb4 null comment 'ad_id',
    jlr_subscription_id             varchar(255) charset utf8mb4 not null comment 'jlr_subscription_id',
    subscription_id                 varchar(255) charset utf8mb4 null comment 'subscription_id',
    subscription_request_type       varchar(255) charset utf8mb4 null comment 'subscription_request_type',
    subscription_commences_date     varchar(255) charset utf8mb4 null comment 'subscription_commences_date',
    subscription_expires_date       varchar(255) charset utf8mb4 null comment 'subscription_expires_date',
    subscription_created_date       varchar(255) charset utf8mb4 null comment 'subscription_created_date',
    subscription_provisioned_date   varchar(255) charset utf8mb4 null comment 'subscription_provisioned_date',
    subscription_activation_date    varchar(255) charset utf8mb4 null comment 'subscription_activation_date',
    subscription_deprovisioned_date varchar(255) charset utf8mb4 null comment 'subscription_deprovisioned_date',
    subscription_deactivation_date  varchar(255) charset utf8mb4 null comment 'subscription_deactivation_date',
    subscription_attribute_name     varchar(255) charset utf8mb4 null comment 'subscription_attribute_name',
    subscription_attribute_value    varchar(255) charset utf8mb4 null comment 'subscription_attribute_value',
    jlr_service_element_instance_id varchar(255) charset utf8mb4 null comment 'jlr_service_element_instance_id',
    service_element_instance_id     varchar(255) charset utf8mb4 null comment 'service_element_instance_id',
    service_element_short_code      varchar(255) charset utf8mb4 null comment 'service_element_short_code',
    service_element_name            varchar(255) charset utf8mb4 null comment 'service_element_name',
    service_element_state           varchar(255) charset utf8mb4 null comment 'service_element_state',
    id_1                            varchar(255) charset utf8mb4 null comment 'id_1',
    subscriber_id                   varchar(255) charset utf8mb4 null comment 'subscriber_id',
    subscriber_unique_id            varchar(255) charset utf8mb4 null comment 'subscriber_unique_id',
    subscriber_first_name           varchar(255) charset utf8mb4 null comment 'subscriber_first_name',
    subscriber_last_name            varchar(255) charset utf8mb4 null comment 'subscriber_last_name',
    subscriber_contact_phone        varchar(255) charset utf8mb4 null comment 'subscriber_contact_phone',
    subscriber_contact_email        varchar(255) charset utf8mb4 null comment 'subscriber_contact_email',
    subscriber_locale               varchar(255) charset utf8mb4 null comment 'subscriber_locale',
    subscriber_street               varchar(255) charset utf8mb4 null comment 'subscriber_street',
    subscriber_street_2             varchar(255) charset utf8mb4 null comment 'subscriber_street_2',
    subscriber_street_3             varchar(255) charset utf8mb4 null comment 'subscriber_street_3',
    subscriber_street_4             varchar(255) charset utf8mb4 null comment 'subscriber_street_4',
    subscriber_town                 varchar(255) charset utf8mb4 null comment 'subscriber_town',
    subscriber_county               varchar(255) charset utf8mb4 null comment 'subscriber_county',
    subscriber_postcode             varchar(255) charset utf8mb4 null comment 'subscriber_postcode',
    subscriber_country              varchar(255) charset utf8mb4 null comment 'subscriber_country',
    subscriber_country_code         varchar(255) charset utf8mb4 null comment 'subscriber_country_code',
    subscriber_address_type         varchar(255) charset utf8mb4 null comment 'subscriber_address_type',
    subscriber_attribute_name       varchar(255) charset utf8mb4 null comment 'subscriber_attribute_name',
    subscriber_attribute_value      varchar(255) charset utf8mb4 null comment 'subscriber_attribute_value',
    id_2                            varchar(255) charset utf8mb4 null comment 'id_2',
    vehicle_id                      varchar(255) charset utf8mb4 null comment 'vehicle_id',
    vin                             varchar(255) charset utf8mb4 null comment 'vin',
    make                            varchar(255) charset utf8mb4 null comment 'make',
    model                           varchar(255) charset utf8mb4 null comment 'model',
    model_code                      varchar(255) charset utf8mb4 null comment 'model_code',
    model_year                      varchar(255) charset utf8mb4 null comment 'model_year',
    country_code                    varchar(255) charset utf8mb4 null comment 'country_code',
    license_number                  varchar(255) charset utf8mb4 null comment 'license_number',
    vehicle_date_created            varchar(255) charset utf8mb4 null comment 'vehicle_date_created',
    vehicle_date_updated            varchar(255) charset utf8mb4 null comment 'vehicle_date_updated',
    vehicle_distributor_code        varchar(255) charset utf8mb4 null comment 'vehicle_distributor_code',
    vehicle_retailer_code           varchar(255) charset utf8mb4 null comment 'vehicle_retailer_code',
    vehicle_feature_code            varchar(255) charset utf8mb4 null comment 'vehicle_feature_code',
    vehicle_feature_name            varchar(255) charset utf8mb4 null comment 'vehicle_feature_name',
    vehicle_attribute_name          varchar(255) charset utf8mb4 null comment 'vehicle_attribute_name',
    vehicle_attribute_value         varchar(255) charset utf8mb4 null comment 'vehicle_attribute_value'
)
    comment 't_appd_initial_data' charset = utf8mb3;

create table ecp_subscription.t_appdcu_renew_batch_records
(
    id                 bigint auto_increment comment '主键'
        primary key,
    batch_no           bigint       not null comment '批次处理号;批次处理号，雪花算法ID',
    upload_file        varchar(255) not null comment '上传原始文件;上传原始文件S3文件URL',
    verify_result      int          not null comment '校验结果;校验结果 0：不通过 1：通过',
    deal_status        int          not null comment '处理状态;处理状态 0：待处理  1：处理中 2：已处理',
    verify_result_file varchar(255) null comment '校验结果文件;对于失败的校验结果S3文件URL',
    operator           varchar(90)  not null comment '操作人账号;操作人账号',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁'
)
    comment 't_appdcu_renew_batch_records';

create table ecp_subscription.t_appdcu_renew_records
(
    id                       bigint auto_increment comment '主键'
        primary key,
    renew_no                 bigint       not null comment '续费编号;续费编号，对于批量续费就是批次号',
    car_vin                  varchar(90)  not null comment 'VIN;VIN',
    renew_service_type       int          not null comment '续费服务类型;续费服务类型 1：APPD 2:CU',
    renew_date               datetime     not null comment '续费指定时间;续费日期 YYYY-MM-DD',
    cus_order_id             varchar(90)  not null comment '客户订单号;客户订单号for联通续费ext_book_id，雪花算法ID',
    operator                 varchar(90)  not null comment '操作人账号;操作人账号',
    data_source              int          not null comment '数据来源;数据来源 1单次续费 2批量续费',
    renew_status             int          not null comment '续费状态;续费状态 0：待续费 2：续费成功 3：续费失败',
    renew_before_expiry_date datetime     null comment '续费前到期日;续费前到期日',
    renew_after_expiry_date  datetime     null comment '续费后到期日;续费后到期日',
    order_result_code        varchar(255) null comment '续费结果;续费结果 1成功 0失败',
    error_desc               varchar(255) null comment '续费失败原因;续费失败原因:1.接口调用失败2.接口返回失败3.系统异常',
    tenant_id                int          not null comment '租户号',
    created_by               varchar(90)  not null comment '创建人',
    created_time             datetime     not null comment '创建时间',
    updated_by               varchar(90)  null comment '更新人',
    updated_time             datetime     null comment '更新时间',
    is_deleted               int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                 int          not null comment '乐观锁'
)
    comment 't_appdcu_renew_records';

create table ecp_notification.t_auto_task_condition
(
    id             bigint auto_increment comment '主键'
        primary key,
    condition_id   varchar(90) not null comment '条件ID;条件ID，雪花算法ID',
    condition_name varchar(90) not null comment '条件名称;条件名称，对应条件类型的名称',
    condition_type tinyint     not null comment '条件类型;条件类型 1：品牌 2：车辆产地 3：到期服务 4：实名状态 5：到期区间',
    condition_code varchar(90) not null comment '条件编码;品牌：JAGUAR,LANDROVER,ALL
产地：CHINA_MADE,IMPORTED,ALL
服务：REMOTE,PIVI
实名状态：RNR_TRUE,RNR_FALSE,ALL
到期时间：BEFORE_EXPIRED,AFTER_EXPIRED',
    tenant_id      int         not null comment '租户号',
    created_by     varchar(90) not null comment '创建人',
    created_time   datetime    not null comment '创建时间',
    updated_by     varchar(90) null comment '更新人',
    updated_time   datetime    null comment '更新时间',
    is_deleted     int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int         not null comment '乐观锁'
)
    comment 't_auto_task_condition' charset = utf8mb3;

create table ecp_notification.t_auto_task_trigger_map
(
    id              bigint auto_increment comment '主键'
        primary key,
    task_code       varchar(90)  not null comment '任务编码;任务编码',
    condition_id    varchar(90)  not null comment '条件ID;条件ID',
    condition_value varchar(255) null comment '条件值;指定条件的值，比如到期日期指定天数：30',
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁'
)
    comment 't_auto_task_trigger_map' charset = utf8mb3;

create table ecp_notification.t_config_modify_log
(
    id                     bigint auto_increment comment '主键'
        primary key,
    config_id              varchar(90) null comment '配置ID;配置ID',
    modify_module          varchar(90) not null comment '修改模块;修改模块',
    modify_field_count     int         null comment '修改字段数量;修改字段数量',
    modify_field_old_value text        null comment '修改前字段值;涉及修改字段旧值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleOld","rule_desc":"descOld"}',
    modify_field_new_value text        null comment '修改后字段值;涉及修改字段新值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleNew","rule_desc":"descNew"}',
    operate_time           datetime    null comment '修改时间;修改时间',
    operate_user           varchar(90) null comment '修改人账号;修改人账号',
    tenant_id              int         not null comment '租户号',
    created_by             varchar(90) not null comment '创建人',
    created_time           datetime    not null comment '创建时间',
    updated_by             varchar(90) null comment '更新人',
    updated_time           datetime    null comment '更新时间',
    is_deleted             int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int         not null comment '乐观锁'
)
    comment 't_config_modify_log';

create table ecp_consumer.t_consumer_channel
(
    id                    bigint auto_increment comment '主键'
        primary key,
    consumer_code         varchar(90)   not null comment '用户编码;用户编码',
    channel_code          varchar(90)   not null comment '渠道编码;渠道编码，路虎小程序LANDROVER；捷豹小程序JAGURA; ',
    open_id               varchar(255)  null comment '渠道open_id;渠道open_id',
    union_id              varchar(255)  null comment '渠道union_id;渠道union_id',
    app_id                varchar(255)  null comment '渠道应用ID;渠道应用ID，如微信公众号APPID小程序APPID',
    channel_phone_encrypt varchar(1000) null comment '渠道手机号;渠道手机号(密文)',
    channel_phone_mix     varchar(90)   null comment '半隐藏手机号',
    nick_name             varchar(255)  null comment '渠道昵称;渠道昵称',
    profile_photo         varchar(255)  null comment '头像URL;头像URL',
    status                int           not null comment '渠道状态;渠道状态；0:已绑定 1：已解绑',
    tenant_id             int           not null comment '租户号',
    created_by            varchar(90)   not null comment '创建人',
    created_time          datetime      not null comment '创建时间',
    updated_by            varchar(90)   null comment '更新人',
    updated_time          datetime      null comment '更新时间',
    is_deleted            int           not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int           not null comment '乐观锁'
)
    comment 't_consumer_channel' charset = utf8mb3;

create index idx_consumer
    on ecp_consumer.t_consumer_channel (consumer_code);

create table ecp_subscription.t_consumer_incontrol
(
    id            bigint auto_increment comment '主键'
        primary key,
    consumer_code varchar(90) not null comment '用户编码;用户编码',
    incontrol_id  varchar(90) not null comment 'incontrol账号;incontrol账号',
    bind_time     datetime    not null comment '绑定时间;绑定时间',
    unbind_time   datetime    null comment '解绑时间;解绑时间',
    bind_status   int         not null comment '绑定状态;绑定状态 0：已解绑 1已绑定',
    bind_channel  varchar(90) not null comment '绑定渠道，LAN:路虎，JAG：捷豹',
    tenant_id     int         not null comment '租户号',
    created_by    varchar(90) not null comment '创建人',
    created_time  datetime    not null comment '创建时间',
    updated_by    varchar(90) null comment '更新人',
    updated_time  datetime    null comment '更新时间',
    is_deleted    int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int         not null comment '乐观锁'
)
    comment 't_consumer_incontrol' charset = utf8mb3
                                   row_format = DYNAMIC;

create index idx_consumer
    on ecp_subscription.t_consumer_incontrol (consumer_code);

create index idx_icr
    on ecp_subscription.t_consumer_incontrol (incontrol_id);

create table ecp_consumer.t_consumer_info
(
    id             bigint auto_increment comment '主键'
        primary key,
    consumer_code  varchar(90)   not null comment '用户编码;用户编码，C+',
    phone_encrypt  varchar(1000) null comment '用户手机号;用户手机号，加密存储',
    phone_md5      varchar(90)   null comment 'md5手机号;MD5手机号，用于查询',
    phone_mix      varchar(90)   null comment '半隐藏手机号',
    nick_name      varchar(90)   null comment '用户昵称;用户昵称',
    city           varchar(90)   null comment '城市;所在城市',
    province       varchar(90)   null comment '省市;所在省市',
    area           varchar(90)   null comment '区域;所在区域',
    email_encrypt  varchar(90)   null comment '邮箱地址;邮箱地址',
    email_md5      varchar(255)  null comment 'MD5邮箱地址;MD5邮箱地址',
    gender         int           null comment '性别;性别，0：女 1：男 2：未知',
    native_place   varchar(2)    null comment '籍贯;籍贯',
    birthday_year  varchar(90)   null comment '生日年;生日年',
    birthday_month varchar(2)    null comment '生日月;生日月',
    bithday_day    varchar(2)    null comment '生日日;生日日',
    tenant_id      int           not null comment '租户号',
    created_by     varchar(90)   not null comment '创建人',
    created_time   datetime      not null comment '创建时间',
    updated_by     varchar(90)   null comment '更新人',
    updated_time   datetime      null comment '更新时间',
    is_deleted     int           not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int           not null comment '乐观锁',
    constraint idx_consumer
        unique (consumer_code)
)
    comment 't_consumer_info' charset = utf8mb3;

create table ecp_product.t_content_modify_log
(
    id                     bigint auto_increment comment '主键'
        primary key,
    content_id             varchar(90) null comment '内容ID',
    content_type           varchar(90) null comment '常见问题：FAQ，更多内容：information',
    modify_module          varchar(90) not null comment '修改模块;修改模块',
    modify_field_count     int         null comment '修改字段数量;修改字段数量',
    modify_field_old_value text        null comment '修改前字段值;涉及修改字段旧值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleOld","rule_desc":"descOld"}',
    modify_field_new_value text        null comment '修改后字段值;涉及修改字段新值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleNew","rule_desc":"descNew"}',
    operate_time           datetime    null comment '修改时间;修改时间',
    operate_user           varchar(90) null comment '修改人账号;修改人账号',
    tenant_id              int         not null comment '租户号',
    created_by             varchar(90) not null comment '创建人',
    created_time           datetime    not null comment '创建时间',
    updated_by             varchar(90) null comment '更新人',
    updated_time           datetime    null comment '更新时间',
    is_deleted             int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int         not null comment '乐观锁'
)
    comment 't_order_modify_detail_log';

create table ecp_product.t_content_modify_log_bak
(
    id               bigint auto_increment comment '主键'
        primary key,
    content_id       varchar(255) not null comment '内容ID;内容ID',
    content_type     varchar(255) not null comment '内容类型;内容类型，FAQ问题POLICY购买条款',
    operate_type     int          not null comment '操作类型;修改类型 1：编辑更多信息配置 2：编辑FAQ',
    modify_user_id   varchar(90)  not null comment '修改人userid;修改人ID',
    modify_user_name varchar(90)  not null comment '修改人;修改人账号',
    tenant_id        int          not null comment '租户号',
    created_by       varchar(90)  not null comment '创建人',
    created_time     datetime     not null comment '创建时间',
    updated_by       varchar(90)  null comment '更新人',
    updated_time     datetime     null comment '更新时间',
    is_deleted       int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int          not null comment '乐观锁'
)
    comment 't_content_modify_log' charset = utf8mb3
                                   row_format = DYNAMIC;

create index t_content_modify_log_content_id_IDX
    on ecp_product.t_content_modify_log_bak (content_id);

create table ecp_order.t_coupon_mock
(
    id                    bigint auto_increment comment '主键ID'
        primary key,
    customer_code         varchar(255)                         null comment '用户编码',
    coupon_code           varchar(255)                         null comment '卡券券码',
    coupon_model_name     varchar(255)                         null comment '卡券模版名称',
    coupon_model_code     varchar(255)                         null comment '卡券模版code',
    coupon_model_classify int                                  null comment '卡券模板类型：0-积分 1-兑换券 2-代金券 3-折扣券 4-满减券',
    points                int                                  null comment '积分数量',
    rule_type             int                                  null comment '满减类型：1-满金额减；2-满数量减',
    trigger_money         varchar(255)                         null comment '触发满金额减和使用现金券时的金额要求（满减券）',
    trigger_number        int                                  null comment '触发满数量减时的数量要求（满减券）',
    trigger_amount        varchar(255)                         null comment '满减券的优惠金额',
    money                 varchar(255)                         null comment '代金券的金额',
    discount_percent      double                               null comment '折扣券的折扣',
    use_rule              text                                 null comment '使用规则',
    use_explain           text                                 null comment '使用说明',
    coupon_scope          varchar(255)                         null comment '卡券适用范围',
    valid_start_time      datetime                             null comment '生效时间',
    valid_end_time        datetime                             null comment '失效时间',
    coupon_img_link       varchar(255)                         null comment '模版图片链接',
    created_time          datetime   default CURRENT_TIMESTAMP null comment '创建时间',
    updated_time          datetime   default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted            tinyint(1) default 0                 null comment '逻辑删除标志',
    tenant_id             int                                  not null comment '租户号',
    created_by            varchar(90)                          not null comment '创建人',
    updated_by            varchar(90)                          null comment '更新人',
    revision              int                                  not null comment '乐观锁'
)
    comment '优惠券模拟表';

create table ecp_product.t_coupon_modify_log
(
    id              bigint auto_increment comment '主键'
        primary key,
    rule_code       varchar(255) not null comment '规则code',
    operate_content text         not null comment '操作内容',
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁'
)
    comment '优惠券修改日志表' charset = utf8mb3;

create table ecp_notification.t_crc_messages
(
    id              bigint auto_increment comment '主键'
        primary key,
    message_id      bigint       not null comment '消息唯一id;消息ID雪花算法ID',
    crc_message_id  varchar(90)  not null comment 'CRC消息唯一ID;CRC消息唯一ID',
    jlr_id          varchar(90)  not null comment '用户JLRID;用户JLRID',
    message_type    int          not null comment '消息类型;1：文字 2：图片 3：视频 4：音频',
    message_content varchar(900) null comment '消息内容;客服留言消息，最大900字符',
    message_time    datetime     not null comment '消息发送时间;消息发送时间',
    message_status  tinyint      not null comment '消息状态;0：未读 1：已读',
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁',
    business_code   varchar(90)  null comment '业务线编码'
)
    comment 't_crc_messages';

create table ecp_consumer.t_customer_browse
(
    id               bigint auto_increment comment '主键'
        primary key,
    consumer_code    varchar(90)                        not null comment '用户编码JLRID;用户编码JLRID',
    product_spu_code varchar(90)                        not null comment '商品SPU编码;商品SPU编码',
    browse_time      datetime                           not null comment '浏览商品时间;浏览商品进入时间',
    browse_page      varchar(90)                        null comment '浏览商品页面;浏览商品页面',
    browse_path      varchar(255)                       null comment '浏览商品页面路径;浏览商品页面路径',
    browse_device    varchar(90)                        null comment '浏览设备信息;浏览设备信息',
    brand_code       varchar(90)                        null comment '品牌编码',
    business_code    varchar(90) default 'BUSINESS:001' not null comment '业务线编码：BUSINESS:001、BrandedGoods、LRE',
    tenant_id        int                                not null comment '租户号',
    created_by       varchar(90)                        not null comment '创建人',
    created_time     datetime                           not null comment '创建时间',
    updated_by       varchar(90)                        null comment '更新人',
    updated_time     datetime                           null comment '更新时间',
    is_deleted       int                                not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int                                not null comment '乐观锁'
)
    comment 't_customer_browse';

create table ecp_order.t_customer_service_order
(
    id                    bigint auto_increment comment '主键'
        primary key,
    order_id              varchar(255) not null comment '下单ID;客服下单ID，雪花算法ID',
    order_code            varchar(255) not null comment '关联订单号;关联订单号',
    recieve_phone         varchar(90)  not null comment '接收付款短信号;接收付款短信号',
    message_template_code varchar(90)  not null comment '短信模板编码;短信模板编码',
    bind_customer         tinyint      not null comment '是否绑定客户，0否 1是',
    create_operator       varchar(90)  not null comment '创建人;创建人',
    tenant_id             int          not null comment '租户号',
    created_by            varchar(90)  not null comment '创建人',
    created_time          datetime     not null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int          not null comment '乐观锁',
    constraint t_customer_service_order_order_code_IDX
        unique (order_code)
)
    comment 't_customer_service_order';

create index t_customer_service_order_create_operator_IDX
    on ecp_order.t_customer_service_order (create_operator);

create table ecp_subscription.t_dms_oss_file_records
(
    id              bigint auto_increment comment '主键'
        primary key,
    bau_job_id      bigint       not null comment '任务ID;雪花算法ID',
    job_date        datetime     not null comment 'job日期;job日期YYYY-MM-DD',
    job_param       varchar(255) null comment 'JOB指定日期;JOB指定日期',
    dms_oss_file    json         not null comment 'DMS OSS文件地址ARRAY JSON;DMS OSS文件地址',
    ecp_s3_file     json         not null comment 'ECP S3文件地址ARRAY JSON;ECP S3文件地址',
    total_vin_num   int          null comment 'VIN总数量;VIN总数量',
    success_vin_num int          null comment '处理成功VIN数量;处理成功VIN数量',
    failed_vin_num  int          null comment '处理失败VIN数量;处理失败VIN数量',
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁'
);

create table ecp_subscription.t_dms_oss_original_data
(
    id                  bigint auto_increment comment '主键',
    bau_job_id          bigint      not null comment 'BAU任务ID;BAU任务ID',
    data_id             bigint      not null comment '数据id;雪花算法ID',
    job_date            datetime    not null comment '任务日期;任务日期',
    car_vin             varchar(90) not null comment 'VIN;VIN',
    dms_invoice_date    datetime    null comment '发票日期;发票日期',
    status              int         not null comment '处理状态;数据处理状态 0：待处理 1：处理成功 2:处理失败',
    sync_status         int         not null comment '同步状态;同步过期日期状态 0：未同步 1：同步成功 2：APPD同步失败 3：CU同步失败 4：均同步失败',
    sota_result         int         null comment 'sota查询结果;sota查询结果 1：成功 2：无数据返回 3：ICCID为空',
    appd_result         int         null comment 'appd查询结果;appd查询结果 1：成功 2：系统异常 3：请求异常 4：未找到该VIN 5：到期日不存在 6：JLR Subscription ID不存在；',
    cu_result           int         null comment '联通查询结果;cu查询结果 1：成功 2：系统异常 3：请求异常 4：未找到该VIN的ICCID；',
    amap_result         int         null comment 'amap查询结果;amap查询结果 1：成功 2：系统异常 3：未找到该VIN 4：到期日不存在；',
    dp_result           int         null comment 'DP查询结果;dp查询结果 1：成功 2：系统异常 3：对应的配置信息为空',
    pivi_config_result  int         null comment 'PIVI车机匹配结果;PIVI车机匹配结果 1：PIVI 2：接口异常 3：非PIVI车机',
    vin_match_result    int         null comment 'VIN处理结果;VIN处理结果是否入库ECP    0：不需要入库  1：待入库 2：成功入库',
    special_vin_config  varchar(90) null comment '特殊车型;特殊车型',
    iccid               varchar(90) null comment 'iccid;iccid',
    jlr_subscription_id varchar(90) null comment 'APPD jlr_subscription_id;APPD jlr_subscription_id',
    amap_expire_date    datetime    null comment '高德服务过期日;高德服务过期日',
    source_type         tinyint     null comment '数据来源 1：BAU JOB   2：新手动补录',
    tenant_id           int         not null comment '租户号',
    created_by          varchar(90) not null comment '创建人',
    created_time        datetime    not null comment '创建时间',
    updated_by          varchar(90) null comment '更新人',
    updated_time        datetime    null comment '更新时间',
    is_deleted          int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision            int         not null comment '乐观锁',
    primary key (id, car_vin)
)
    comment 't_dms_oss_original_data';

create table ecp_subscription.t_dms_oss_original_data_manual
(
    id                 bigint auto_increment comment '主键'
        primary key,
    data_id            bigint       not null comment '数据id;雪花算法ID',
    car_vin            varchar(90)  not null comment 'VIN;VIN',
    dms_invoice_date   datetime     not null comment '发票日期;发票日期',
    iccid              varchar(90)  not null comment 'iccid;iccid',
    status             int          not null comment '处理状态;数据处理状态 0：初始化中 1：初始化成功 2:初始化失败',
    sync_status        int          not null comment '同步状态;同步过期日期状态 0：未同步 1：同步成功 2：APPD同步失败 3：CU同步失败 4：均同步失败',
    sota_result        int          null comment 'sota查询结果;sota查询结果 1:成功，2：失败无数据 3：失败iccid为空',
    appd_result        int          null comment 'appd查询结果;appd查询结果 1：成功  2：系统异常 3：请求异常 4：未找到VIN 5：到期日不存在 6：JLR SUBSCRIPTIONID不存在；',
    cu_result          int          null comment '联通查询结果;cu查询结果 1：成功 2：VIN不存在 3：系统异常 4：参数错误；',
    amap_result        int          null comment 'amap查询结果;amap查询结果 1：成功 2：VIN不存在 3：系统异常 4：参数错误；',
    dp_result          int          null comment 'DP查询结果;dp查询结果 0：失败 1：成功',
    pivi_config_result int          null comment 'PIVI车机匹配结果;PIVI车机匹配结果 0：非PIVI 1：PIVI',
    special_vin_config varchar(90)  null comment '特殊车型;特殊车型',
    error_message      varchar(255) null comment '失败原因;失败原因，各个系统查不到或者同步失败的原因拼接',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁'
)
    comment 't_dms_oss_original_data_manual';

create table ecp_subscription.t_dms_oss_original_data_records
(
    id               bigint auto_increment comment '主键'
        primary key,
    bau_job_id       bigint        not null comment 'BAU任务ID;BAU任务ID',
    data_id          bigint        not null comment '数据ID;数据ID',
    car_vin          varchar(90)   null comment 'VIN',
    sota_result      varchar(2000) null comment 'sota查询结果;sota查询结果',
    appd_result      varchar(2000) null comment 'appd查询结果;appd查询结果',
    cu_result        longtext      null comment 'cu查询结果;cu查询结果',
    dp_result        varchar(2000) null comment 'dp查询结果;dp查询结果',
    amap_result      varchar(2000) null comment 'amap查询结果;amap查询结果',
    appd_sync_result int           null comment 'appd过期同步结果;appd过期同步结果 0：失败 1：成功',
    cu_sync_result   int           null comment 'cu过期同步结果;cu过期同步结果 0：失败 1：成功',
    tenant_id        int           not null comment '租户号',
    created_by       varchar(90)   not null comment '创建人',
    created_time     datetime      not null comment '创建时间',
    updated_by       varchar(90)   null comment '更新人',
    updated_time     datetime      null comment '更新时间',
    is_deleted       int           not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int           not null comment '乐观锁'
)
    comment 't_dms_oss_original_data_records';

create table ecp_system.t_eshop_entrance_config
(
    id                bigint auto_increment comment '主键'
        primary key,
    entrance_name     varchar(255)  not null comment '入口名称',
    entrance_desc     varchar(255)  not null comment '入口描述',
    entrance_path     varchar(1024) null comment '入口路径',
    entrance_pic_url  varchar(1024) null comment '入口底图链接',
    entrance_position varchar(255)  not null comment '入口位置 枚举类型 (L-左侧; RT-右顶; RD-右底)',
    tenant_id         int           not null comment '租户号',
    created_by        varchar(90)   null comment '创建人',
    created_time      datetime      not null comment '创建时间',
    updated_by        varchar(90)   null comment '更新人',
    updated_time      datetime      null comment '更新时间',
    is_deleted        int           not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int           not null comment '乐观锁'
)
    comment '商城入口图文配置信息表';

create table ecp_system.t_eshop_entrance_config_modify_log
(
    id             bigint auto_increment comment '主键'
        primary key,
    config_id      varchar(90)  null comment '配置ID;配置ID',
    modify_content varchar(90)  null comment '修改操作;',
    remark         varchar(255) null comment '备注信息;备注信息',
    operate_time   datetime     null comment '修改时间;修改时间',
    operate_user   varchar(90)  null comment '修改人账号;修改人账号',
    tenant_id      int          not null comment '租户号',
    created_by     varchar(90)  null comment '创建人',
    created_time   datetime     not null comment '创建时间',
    updated_by     varchar(90)  null comment '更新人',
    updated_time   datetime     null comment '更新时间',
    is_deleted     int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int          not null comment '乐观锁'
)
    comment '商城入口配置信息变更记录表';

create table ecp_system.t_eshop_entrance_poster_config
(
    id                   bigint auto_increment comment '主键'
        primary key,
    poster_main_url      varchar(255) null comment '海报主图 URL',
    poster_long_form_url varchar(255) null comment '海报长图 URL',
    tenant_id            int          not null comment '租户号',
    created_by           varchar(90)  null comment '创建人',
    created_time         datetime     not null comment '创建时间',
    updated_by           varchar(90)  null comment '更新人',
    updated_time         datetime     null comment '更新时间',
    is_deleted           int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision             int          not null comment '乐观锁'
);

create table ecp_system.t_eshop_entrance_poster_modify_log
(
    id             bigint auto_increment comment '主键'
        primary key,
    modify_content varchar(90)  null comment '修改操作;',
    remark         varchar(255) null comment '备注信息;备注信息',
    operate_time   datetime     not null comment '修改时间;修改时间',
    operate_user   varchar(90)  null comment '修改人账号;修改人账号',
    tenant_id      int          not null comment '租户号',
    created_by     varchar(90)  null comment '创建人',
    created_time   datetime     not null comment '创建时间',
    updated_by     varchar(90)  null comment '更新人',
    updated_time   datetime     null comment '更新时间',
    is_deleted     int          not null comment '是否删除;逻辑删除字段, 0:否; 1:是',
    revision       int          not null comment '乐观锁'
);

create table ecp_subscription.t_expire_search_batch_record
(
    id            bigint auto_increment comment '主键'
        primary key,
    batch_no      varchar(90)       not null comment '批次处理号（雪花算法ID）',
    verify_result tinyint           null comment '校验结果：0-不通过 1-通过',
    verify_reason varchar(900)      null comment '校验不通过原因',
    deal_status   tinyint default 0 not null comment '处理状态：0-待处理 1-处理中 2-已处理',
    operator      varchar(90)       null comment '操作人账号',
    tenant_id     int               not null comment '租户号',
    created_by    varchar(90)       not null comment '创建人',
    created_time  datetime          not null comment '创建时间',
    updated_by    varchar(90)       null comment '更新人',
    updated_time  datetime          null comment '更新时间',
    is_deleted    int     default 0 not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int               not null comment '乐观锁'
);

create table ecp_subscription.t_expire_search_detail_record
(
    id                   bigint auto_increment comment '主键'
        primary key,
    car_vin              varchar(64)       not null comment '车辆VIN码',
    batch_no             varchar(90)       not null comment '批次处理号（雪花算法ID）',
    query_status         tinyint default 0 not null comment '处理状态：0-查询中 1-查询完成 2-查询失败',
    brand_name           varchar(255)      null comment '品牌名称',
    config_name          varchar(900)      null comment '配置名称',
    model_year           varchar(255)      null comment '型号年款',
    config_code          varchar(255)      null comment '配置编码',
    series_code          varchar(255)      null comment '系列编码',
    brand_code           varchar(255)      null comment '品牌编码',
    series_name          varchar(255)      null comment '系列名称',
    carSystemModel       varchar(64)       null comment 'PIVI车机属性（PIVI标识是）',
    has_info_entertain   char(90)          null comment '是否具备信息娱乐服务（N/Y）',
    remote_service_date  varchar(64)       null comment 'InControl远程车控服务日期',
    pivi_service_date    varchar(64)       null comment 'InControl在线服务日期',
    appd_service_date    varchar(64)       null comment '信息娱乐服务日期',
    amap_service_date    varchar(64)       null comment '实时交通信息日期',
    unicom_service_date  varchar(64)       null comment '网络流量服务日期',
    invoice_service_date varchar(64)       null comment '车辆发票日期',
    operate_time         datetime          null comment '操作时间',
    operator             varchar(90)       null comment '操作人账号',
    error_reason         varchar(255)      null comment '失败原因',
    tenant_id            int               null comment '租户ID',
    created_by           varchar(90)       not null comment '创建人',
    created_time         datetime          not null comment '创建时间',
    updated_by           varchar(90)       null comment '更新人',
    updated_time         datetime          null comment '更新时间',
    is_deleted           int     default 0 not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision             int               not null comment '乐观锁'
);

create table ecp_order.t_feedback_config
(
    id                   bigint auto_increment comment '反馈配置ID'
        primary key,
    business_code        varchar(90)  null comment '业务线',
    feedback_dimensions  varchar(32)  not null comment '评价维度：PM开头、已支付OR、订单完成、CL、订单整单取消',
    feedback_code        varchar(90)  not null comment '评价编码：生成规则：feedback_dimensions+递增三位数编号（例如：PM001）',
    enable_status        tinyint      not null comment '商品使用状态(启用状态，0=待启用 1=启用 2=停用)',
    downtime             datetime     null comment '停用时间',
    enable_time          datetime     null comment '启用时间',
    schedule_enable_time datetime     null comment '定时启用时间',
    enable_input         tinyint      not null comment '是否允许输入框：0=否；1=是',
    input_text           varchar(255) null comment '输入框提示信息',
    must_input           tinyint      not null comment '输入框是否必填：0=否；1=是',
    remark               varchar(255) null comment '备注说明',
    last_modify_user     varchar(90)  null comment '最近修改人',
    tenant_id            int          not null comment '租户ID',
    created_by           varchar(90)  not null comment '创建人',
    created_time         datetime     not null comment '创建时间',
    updated_by           varchar(90)  null comment '更新人',
    updated_time         datetime     null comment '更新时间',
    is_deleted           int          not null comment '是否删除/逻辑删除字段，0：否；1：是',
    revision             int          not null comment '版本号'
)
    comment '评价设置表';

create index idx_feedback_code
    on ecp_order.t_feedback_config (feedback_code);

create table ecp_order.t_feedback_dimensions
(
    id              bigint auto_increment comment '维度配置ID'
        primary key,
    dimensions_code varchar(90)  not null comment '维度Code，算法',
    feedback_code   varchar(90)  not null comment '评价编码：生成规则：feedback_dimensions+递增三位数编号（例如：PM001）',
    name            varchar(90)  not null comment '名称',
    type            int          not null comment '维度类型：0：星级（5分制）；1：单选题；2：多选题',
    option_json     json         null comment '选项:[{"option":"ICR APP","sort":1},{"option":"广告","sort":2}]',
    must_input      tinyint      not null comment '是否必填：0=否；1=是',
    remark          varchar(255) null comment '备注',
    sort            int          not null comment '排序字段',
    tenant_id       int          not null comment '租户ID',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除/逻辑删除字段，0：否；1：是',
    revision        int          not null comment '版本号'
)
    comment '评价维度配置表';

create index idx_feedback_code
    on ecp_order.t_feedback_dimensions (feedback_code);

create table ecp_order.t_feedback_modify_log
(
    id                     bigint auto_increment comment '主键'
        primary key,
    feedback_code          varchar(90) null comment '订单号;订单号',
    modify_module          varchar(90) not null comment '修改模块;修改模块',
    modify_field_count     int         null comment '修改字段数量;修改字段数量',
    modify_field_old_value text        null comment '修改前字段值;涉及修改字段旧值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleOld","rule_desc":"descOld"}',
    modify_field_new_value text        null comment '修改后字段值;涉及修改字段新值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleNew","rule_desc":"descNew"}',
    operate_time           datetime    null comment '修改时间;修改时间',
    operate_user           varchar(90) null comment '修改人账号;修改人账号',
    tenant_id              int         not null comment '租户号',
    created_by             varchar(90) not null comment '创建人',
    created_time           datetime    not null comment '创建时间',
    updated_by             varchar(90) null comment '更新人',
    updated_time           datetime    null comment '更新时间',
    is_deleted             int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int         not null comment '乐观锁'
)
    comment 't_order_modify_detail_log';

create table ecp_order.t_feedback_records
(
    id                    bigint auto_increment comment '记录ID'
        primary key,
    feedback_records_code varchar(90)  not null comment '评价记录Code，算法',
    feedback_dimensions   varchar(32)  not null comment '评价维度：
PM已支付；
OR订单完成；
CL订单整单取消；',
    feedback_code         varchar(90)  not null comment '评价编码：生成规则：feedback_dimensions+递增三位数编号（例如：PM001）',
    snapshot_code         varchar(90)  not null comment '快照编码：版本号，启用时自动生成：feedback_code_PT+时间戳（YYYYMMDDHHMMSS）',
    order_code            varchar(90)  not null comment '订单编码：订单唯一标识，编码规则：业务线code+时间戳（年月日时分秒）+6位随机数',
    total                 int          not null comment '评价总分',
    feedback_json         json         null comment '评价结果内容:例如：[{"name":"订单完成速度","type":0,"mustInput":0,"sort":1,"result":["5"]},{"name":"小程序界面便捷度","type":0,"mustInput":0,"sort":2,"result":["4"]},{"name":"渠道","type":2,"mustInput":0,"optionJson":[{"option":"ICR APP","sort":1},{"option":"广告","sort":2}],"sort":2,"result":["广告","ICR APP"]}] ',
    input_extra           varchar(255) null comment '输入框额外内容',
    tenant_id             int          not null comment '租户ID',
    created_by            varchar(90)  not null comment '创建人',
    created_time          datetime     not null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          not null comment '是否删除/逻辑删除字段，0：否；1：是',
    revision              int          not null comment '版本号'
)
    comment '评价记录表';

create index idx_feedback_code
    on ecp_order.t_feedback_records (feedback_code);

create index idx_order_code
    on ecp_order.t_feedback_records (order_code);

create index idx_snapshot_code
    on ecp_order.t_feedback_records (snapshot_code);

create table ecp_order.t_feedback_snapshot
(
    id                  bigint auto_increment comment '快照ID'
        primary key,
    business_code       varchar(90)  null comment '业务线',
    feedback_dimensions varchar(32)  not null comment '评价维度：PM开头、已支付OR、订单完成、CL、订单整单取消',
    feedback_code       varchar(90)  not null comment '评价编码：生成规则：feedback_dimensions+递增三位数编号（例如：PM001）',
    snapshot_code       varchar(90)  not null comment '快照编码：版本号，启用时自动生成：feedback_code_PT+时间戳（YYYYMMDDHHMMSS）',
    snapshot_json       json         null comment '快照内容 JSON格式：[{"name":"订单完成速度","type":0,"must_input":0,"sort":1,"remark":"备注"},{"name":"小程序界面便捷度","type":0,"must_input":0,"sort":2,"remark":"备注1"},{"name":"渠道","type":2,"optionJson":[{"option":"ICR APP","sort":1},{"option":"广告","sort":2}],"mustInput":0,"sort":2,"remark":"备注1"}]',
    enable_input        tinyint      not null comment '是否允许输入框：0=否；1=是',
    input_text          varchar(255) null comment '输入框提示信息',
    remark              varchar(255) null comment '备注说明',
    downtime            datetime     null comment '停用时间',
    enable_time         datetime     null comment '启用时间',
    submit_num          int          null comment '提交数量；停用的时候统计；当前正在使用的快照为null直接查询records表',
    tenant_id           int          not null comment '租户ID',
    created_by          varchar(90)  not null comment '创建人',
    created_time        datetime     not null comment '创建时间',
    updated_by          varchar(90)  null comment '更新人',
    updated_time        datetime     null comment '更新时间',
    is_deleted          int          not null comment '是否删除/逻辑删除字段，0：否；1：是',
    revision            int          not null comment '版本号'
)
    comment '评价设置快照表';

create index idx_feedback_code
    on ecp_order.t_feedback_snapshot (feedback_code);

create index idx_snapshot_code
    on ecp_order.t_feedback_snapshot (snapshot_code);

create table ecp_logistics.t_freight_rule_region
(
    id            bigint auto_increment comment '主键'
        primary key,
    country       varchar(100) null comment '国家',
    province      varchar(100) null comment '省',
    city          varchar(100) null comment '市',
    rule_code     varchar(100) null comment '运费模板规则code',
    tenant_id     int          not null comment '租户号',
    created_by    varchar(90)  not null comment '创建人',
    created_time  datetime     not null comment '创建时间',
    updated_by    varchar(90)  null comment '更新人',
    updated_time  datetime     null comment '更新时间',
    is_deleted    int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int          not null comment '乐观锁',
    template_code varchar(100) null comment '运费模板code'
)
    comment '运费规则地区表' charset = utf8mb3;

create table ecp_logistics.t_freight_template
(
    id                      bigint auto_increment comment '主键'
        primary key,
    template_name           varchar(255) not null comment '模板名称',
    template_code           varchar(50)  not null comment '模板编码',
    shipping_method         varchar(100) null comment '运送方式',
    free_shipping_type      tinyint      null comment '包邮类型',
    free_shipping_condition bigint       null comment '包邮条件(满足包邮条件的金额)',
    origin_country          varchar(100) null comment '发货地-国家',
    origin_province         varchar(100) null comment '发货地-省',
    origin_city             varchar(100) null comment '发货地-市',
    origin_area             varchar(100) null comment '发货地-区',
    tenant_id               int          not null comment '租户号',
    created_by              varchar(90)  not null comment '创建人',
    created_time            datetime     not null comment '创建时间',
    updated_by              varchar(90)  null comment '更新人',
    updated_time            datetime     null comment '更新时间',
    is_deleted              int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                int          not null comment '乐观锁',
    constraint template_code
        unique (template_code)
)
    comment '运费模板表' charset = utf8mb3;

create table ecp_logistics.t_freight_template_rule
(
    id                    bigint auto_increment comment '主键'
        primary key,
    first_weight          bigint       not null comment '首重',
    first_weight_fee      bigint       not null comment '首重运费',
    additional_weight     bigint       not null comment '续重',
    additional_weight_fee bigint       not null comment '续重运费',
    template_code         varchar(50)  null comment '运费模板编码',
    rule_type             varchar(50)  null comment '运费模板规则类型(其他地区/指定地区)',
    tenant_id             int          not null comment '租户号',
    created_by            varchar(90)  not null comment '创建人',
    created_time          datetime     not null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int          not null comment '乐观锁',
    rule_code             varchar(100) null comment '运费模板规则code'
)
    comment '运费模板规则表' charset = utf8mb3;

create table ecp_system.t_gift_address_config
(
    id           bigint auto_increment comment '主键'
        primary key,
    is_enable    int          not null comment '是否启用;是否启用 0 停用 1启用',
    tips_content varchar(255) null comment '提示文案;提示文案',
    remark       varchar(255) not null comment '备注信息;备注信息',
    tenant_id    int          not null comment '租户号',
    created_by   varchar(90)  not null comment '创建人',
    created_time datetime     not null comment '创建时间',
    updated_by   varchar(90)  null comment '更新人',
    updated_time datetime     null comment '更新时间',
    is_deleted   int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int          not null comment '乐观锁'
)
    comment 't_gift_address_config';

create table ecp_system.t_gift_address_config_modify_log
(
    id             bigint auto_increment comment '主键'
        primary key,
    config_id      varchar(90)  not null comment '配置ID;配置ID',
    modify_content varchar(90)  not null comment '修改操作;修改操作，启用，停用',
    remark         varchar(255) null comment '备注信息;备注信息',
    operate_time   datetime     null comment '修改时间;修改时间',
    operate_user   varchar(90)  null comment '修改人账号;修改人账号',
    tenant_id      int          not null comment '租户号',
    created_by     varchar(90)  not null comment '创建人',
    created_time   datetime     not null comment '创建时间',
    updated_by     varchar(90)  null comment '更新人',
    updated_time   datetime     null comment '更新时间',
    is_deleted     int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int          not null comment '乐观锁'
)
    comment 't_gift_address_config_modify_log';

create table ecp_fulfillment.t_guanyi_sync_log
(
    id              bigint auto_increment comment '主键ID'
        primary key,
    request_time    datetime      not null comment '请求时间',
    request_method  varchar(255)  not null comment '请求方法',
    request_headers text          null comment '请求头',
    request_params  text          null comment '请求参数',
    request_body    text          null comment '请求主体',
    response_body   text          null comment '响应主体',
    response_time   datetime      null comment '响应时间',
    cost_time       int           null comment '花费时间（单位：毫秒）',
    is_success      int default 0 not null comment '是否成功，0：失败；1：成功',
    tenant_id       int           not null comment '租户ID',
    created_by      varchar(90)   not null comment '创建人',
    created_time    datetime      not null comment '创建时间',
    updated_by      varchar(90)   null comment '更新人',
    updated_time    datetime      null comment '更新时间',
    is_deleted      int default 0 not null comment '是否删除/逻辑删除字段，0：否；1：是',
    revision        int default 0 not null comment '版本号'
)
    comment '管易云同步请求日志表';

create table ecp_system.t_home_config_modify_log
(
    id             bigint auto_increment comment '主键'
        primary key,
    config_id      bigint      not null comment '配置主键ID',
    modify_content varchar(90) not null comment '修改内容;修改内容',
    modify_date    varchar(90) not null comment '修改时间;修改时间',
    modify_user    varchar(90) not null comment '修改人;修改人',
    tenant_id      int         not null comment '租户号',
    created_by     varchar(90) not null comment '创建人',
    created_time   datetime    not null comment '创建时间',
    updated_by     varchar(90) null comment '更新人',
    updated_time   datetime    null comment '更新时间',
    is_deleted     int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int         not null comment '乐观锁'
)
    comment 't_home_config_modify_log' collate = utf8mb4_general_ci
                                       row_format = DYNAMIC;

create table ecp_system.t_home_config_modify_log_new
(
    id                     bigint auto_increment comment '主键'
        primary key,
    config_id              varchar(90) null comment '配置ID;配置ID',
    modify_module          varchar(90) not null comment '修改模块;修改模块',
    modify_field_count     int         null comment '修改字段数量;修改字段数量',
    modify_field_old_value text        null comment '修改前字段值;涉及修改字段旧值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleOld","rule_desc":"descOld"}',
    modify_field_new_value text        null comment '修改后字段值;涉及修改字段新值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleNew","rule_desc":"descNew"}',
    operate_time           datetime    null comment '修改时间;修改时间',
    operate_user           varchar(90) null comment '修改人账号;修改人账号',
    tenant_id              int         not null comment '租户号',
    created_by             varchar(90) not null comment '创建人',
    created_time           datetime    not null comment '创建时间',
    updated_by             varchar(90) null comment '更新人',
    updated_time           datetime    null comment '更新时间',
    is_deleted             int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int         not null comment '乐观锁'
)
    comment 't_home_config_modify_log_new';

create table ecp_subscription.t_iccid_modify_batch_records
(
    id                 bigint auto_increment comment '主键'
        primary key,
    revision           int          not null comment '乐观锁',
    batch_no           bigint       not null comment '批次号',
    upload_file        varchar(90)  not null comment '上传原始文件S3文件URL',
    verify_result      int          null comment '校验结果;校验结果;校验结果;校验结果 0：不通过 1：通过',
    deal_status        int          null comment '处理状态;处理状态 0：待处理  1：处理中 2：已处理',
    verify_result_file varchar(255) null comment '校验结果文件;对于失败的校验结果S3文件URL',
    operator           varchar(90)  not null comment '操作人账号;操作人账号',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是'
)
    comment 't_iccid_modify_batch_records';

create table ecp_subscription.t_iccid_modify_records
(
    id                  bigint auto_increment comment '主键'
        primary key,
    modify_no           bigint       not null comment '批次编号;批次编号，单条修改记录拥有唯一的雪花算法生成的id; 对于批量修改就是批次号',
    car_vin             varchar(90)  not null comment 'VIN',
    modify_before_iccid varchar(90)  not null comment '修改前联通ICCID;修改前联通ICCID',
    modify_after_iccid  varchar(255) not null comment '修改后联通ICCID;修改后联通ICCID',
    modify_status       int          not null comment '修改状态;修改状态：1：进行中 2：修改成功 3：修改失败',
    data_source         tinyint      not null comment '数据来源 1单次修改 2批量修改',
    error_desc          varchar(90)  null comment '错误信息;修改失败原因:1：ICCID在联通不存在 2.联通接口调用失败 3.系统异常',
    tenant_id           int          not null comment '租户号',
    created_by          varchar(90)  not null comment '创建人',
    created_time        datetime     not null comment '创建时间',
    updated_by          varchar(90)  null comment '更新人',
    updated_time        datetime     null comment '更新时间',
    is_deleted          int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision            int          not null comment '乐观锁'
)
    comment 't_iccid_modify_records';

create table ecp_subscription.t_import_manual_modify_log
(
    operate_time        datetime    null,
    operator            varchar(50) null,
    car_vin             varchar(50) null,
    modifyType          varchar(50) null,
    modify_before_value varchar(50) null,
    modify_after_value  varchar(50) null,
    remark              varchar(50) null,
    other               varchar(50) null
);

create table ecp_subscription.t_incontrol_customer
(
    id              bigint auto_increment comment '主键'
        primary key,
    incontrol_id    varchar(90) not null comment 'ICR账号;ICR账号',
    userid          varchar(90) null comment '用户ID;用户ID',
    firstName       varchar(90) null comment '姓;姓',
    surname         varchar(90) null comment '名;名',
    phone_encrypt   varchar(90) null comment '加密手机号;加密手机号',
    source          int         null comment '用户来源;用户来源 1主动登录 2过期服务查询',
    last_login_time datetime    null comment '上次登录时间;上次登录时间',
    tenant_id       int         not null comment '租户号',
    created_by      varchar(90) not null comment '创建人',
    created_time    datetime    not null comment '创建时间',
    updated_by      varchar(90) null comment '更新人',
    updated_time    datetime    null comment '更新时间',
    is_deleted      int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int         not null comment '乐观锁'
)
    comment 't_incontrol_customer' charset = utf8mb3;

create table ecp_subscription.t_incontrol_vehicle
(
    id               bigint auto_increment comment '主键'
        primary key,
    car_vin          varchar(90)  not null comment '车辆VIN;车辆VIN，唯一索引',
    incontrol_id     varchar(90)  null comment 'incontrol账号;incontrol账号',
    series_code      varchar(90)  not null comment '车辆型号;车辆型号',
    series_name      varchar(90)  not null comment '车辆名称;车辆名称',
    brand_code       varchar(255) null comment '品牌CODE;品牌CODE; LALANDJAG',
    brand_name       varchar(90)  null comment '品牌名;车型品牌',
    hob_en           varchar(90)  null comment 'House of Brand 英文描述描述;House of Brand 英文描述描述',
    production_en    varchar(90)  null comment '产品类型EN;产品类型EN',
    config_code      varchar(90)  null comment '配置编码;配置编码',
    config_name      varchar(255) null comment '配置名称;配置名称',
    model_year       varchar(90)  not null comment '车辆年款;车辆年款',
    car_system_model varchar(90)  null comment '车机型号;车机型号，PIVI，通过计算获得',
    incontrol_phone  varchar(90)  null comment '车主手机号',
    dms_invoice_date datetime     null comment '发票时间',
    bind_time        datetime     not null comment '绑定时间;绑定时间',
    tenant_id        int          null comment '租户号',
    created_by       varchar(90)  null comment '创建人',
    created_time     datetime     null comment '创建时间',
    updated_by       varchar(90)  null comment '更新人',
    updated_time     datetime     null comment '更新时间',
    is_deleted       int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int          null comment '乐观锁'
)
    comment 't_incontrol_vehicle' charset = utf8mb3
                                  row_format = DYNAMIC;

create index idx_icr
    on ecp_subscription.t_incontrol_vehicle (incontrol_id);

create index idx_vin
    on ecp_subscription.t_incontrol_vehicle (car_vin);

create table ecp_system.t_infra_api_access_log
(
    id               int auto_increment comment '主键',
    trace_id         varchar(90)  not null comment '链路追踪编号;链路追踪编号',
    user_id          int          not null comment '用户编号;用户编号',
    user_type        int          not null comment '用户类型;用户类型',
    application_name varchar(90)  not null comment '应用名称;应用名称',
    request_method   varchar(90)  not null comment '请求方法名;请求方法名',
    request_url      varchar(90)  not null comment '请求URL;请求URL',
    request_params   text         not null comment '请求参数;请求参数',
    user_ip          varchar(90)  not null comment '用户IP;用户IP',
    user_agent       varchar(512) not null comment '浏览器UA;浏览器UA',
    begin_time       datetime     not null comment '开始请求时间;开始请求时间',
    end_time         datetime     not null comment '结束请求时间;结束请求时间',
    duration         int          not null comment '请求时长;请求时长',
    result_code      varchar(90)  not null comment '结果码;结果码',
    result_msg       varchar(90)  not null comment '结果提示;结果提示',
    tenant_id        int          not null comment '租户号',
    created_by       varchar(90)  not null comment '创建人',
    created_time     datetime     not null comment '创建时间',
    updated_by       varchar(90)  null comment '更新人',
    updated_time     datetime     null comment '更新时间',
    is_deleted       int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int          not null comment '乐观锁',
    primary key (id, trace_id)
)
    comment 't_infra_api_access_log' collate = utf8mb4_general_ci;

create table ecp_system.t_infra_api_error_log
(
    id                           int auto_increment comment '编号'
        primary key,
    trace_id                     varchar(64)                            not null comment '链路追踪编号
     *
     * 一般来说，通过链路追踪编号，可以将访问日志，错误日志，链路追踪日志，logger 打印日志等，结合在一起，从而进行排错。',
    user_id                      int          default 0                 not null comment '用户编号',
    user_type                    tinyint      default 0                 not null comment '用户类型',
    application_name             varchar(50)                            not null comment '应用名
     *
     * 目前读取 spring.application.name',
    request_method               varchar(16)                            not null comment '请求方法名',
    request_url                  varchar(255)                           not null comment '请求地址',
    request_params               varchar(8000)                          not null comment '请求参数',
    user_ip                      varchar(50)                            not null comment '用户 IP',
    user_agent                   varchar(512)                           not null comment '浏览器 UA',
    exception_time               datetime                               not null comment '异常发生时间',
    exception_name               varchar(128) default ''                not null comment '异常名
     *
     * {@link Throwable#getClass()} 的类全名',
    exception_message            text                                   not null comment '异常导致的消息
     *
     * {@link com.dtt.framework.common.exception.ServiceException#getMessage(Throwable)}',
    exception_root_cause_message text                                   not null comment '异常导致的根消息
     *
     * {@link com.dtt.framework.common.exception.ServiceException#getRootCauseMessage(Throwable)}',
    exception_stack_trace        text                                   not null comment '异常的栈轨迹
     *
     * {@link com.dtt.framework.common.exception.ServiceException#getServiceException(Exception)}',
    exception_class_name         varchar(512)                           not null comment '异常发生的类全名
     *
     * {@link StackTraceElement#getClassName()}',
    exception_file_name          varchar(512)                           not null comment '异常发生的类文件
     *
     * {@link StackTraceElement#getFileName()}',
    exception_method_name        varchar(512)                           not null comment '异常发生的方法名
     *
     * {@link StackTraceElement#getMethodName()}',
    exception_line_number        int                                    not null comment '异常发生的方法所在行
     *
     * {@link StackTraceElement#getLineNumber()}',
    process_status               tinyint                                not null comment '处理状态',
    process_time                 datetime                               null comment '处理时间',
    process_user_id              int          default 0                 null comment '处理用户编号',
    created_by                   varchar(64)  default ''                not null comment '鍒涘缓鑰',
    created_time                 datetime     default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by                   varchar(64)  default ''                null comment '鏇存柊鑰',
    updated_time                 datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted                   bit          default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id                    bigint       default 0                 not null comment '租户编号',
    revision                     bigint       default 1                 not null comment '版本号'
)
    comment '系统异常日志' collate = utf8mb4_unicode_ci;

create table ecp_system.t_infra_file
(
    id           bigint auto_increment comment '文件编号'
        primary key,
    config_id    bigint                                null comment '配置编号',
    name         varchar(256)                          null comment '文件名',
    path         varchar(512)                          not null comment '文件路径',
    url          varchar(1024)                         not null comment '文件 URL',
    type         varchar(128)                          null comment '文件类型',
    size         int                                   not null comment '文件大小',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id    bigint                                null comment '租户id',
    revision     bigint      default 1                 not null comment '版本号'
)
    comment '文件表' collate = utf8mb4_unicode_ci;

create table ecp_system.t_infra_file_config
(
    id           bigint auto_increment comment '编号'
        primary key,
    code         varchar(64)                           null comment '编码标识',
    name         varchar(63)                           not null comment '配置名',
    storage      tinyint                               not null comment '存储器',
    remark       varchar(255)                          null comment '备注',
    master       bit                                   not null comment '是否为主配置',
    config       varchar(4096)                         not null comment '存储配置',
    created_by   varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by   varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted   bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision     bigint      default 1                 not null comment '版本号'
)
    comment '文件配置表' collate = utf8mb4_unicode_ci;

create table ecp_subscription.t_inter_busi_error_log
(
    id             int auto_increment comment '编号'
        primary key,
    trace_id       varchar(64)                           not null comment '链路追踪编号',
    business_id    varchar(64)                           not null comment '业务单据id',
    business_type  varchar(32)                           not null comment '业务类型',
    busines_params varchar(8000)                         not null comment '业务参数',
    error_count    tinyint     default 0                 not null comment '失败次数',
    error_message  text                                  not null comment '异常导致的消息',
    created_by     varchar(64) default ''                not null comment '创建人',
    created_time   datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by     varchar(64) default ''                null comment '更新人',
    updated_time   datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted     bit         default b'0'              not null comment '是否删除',
    tenant_id      bigint      default 0                 not null comment '租户编号',
    revision       bigint      default 1                 not null comment '版本号'
)
    comment '业务异常日志表' collate = utf8mb4_unicode_ci;

create table ecp_inventory.t_inventory
(
    id               bigint auto_increment comment '主键'
        primary key,
    product_sku_code varchar(90)   not null comment '商品SKU编号',
    sales_num        int default 0 not null comment '可售卖数量',
    lock_num         int default 0 not null comment '锁定数量',
    created_by       varchar(20)   not null comment '创建人',
    created_time     datetime      not null comment '创建时间',
    updated_by       varchar(20)   null comment '更新人',
    updated_time     datetime      null comment '更新时间',
    is_deleted       int default 0 not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int default 1 not null comment '乐观锁',
    tenant_id        int default 1 not null,
    constraint t_inventory_sku_code_u_index
        unique (product_sku_code)
)
    comment 't_inventory';

create table ecp_inventory.t_inventory_backup_20250414
(
    id               bigint auto_increment comment '主键'
        primary key,
    product_sku_code varchar(90)   not null comment '商品SKU编号',
    sales_num        int default 0 not null comment '可售卖数量',
    lock_num         int default 0 not null comment '锁定数量',
    created_by       varchar(20)   not null comment '创建人',
    created_time     datetime      not null comment '创建时间',
    updated_by       varchar(20)   null comment '更新人',
    updated_time     datetime      null comment '更新时间',
    is_deleted       int default 0 not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int default 1 not null comment '乐观锁',
    tenant_id        int default 1 not null,
    constraint t_inventory_sku_code_u_index
        unique (product_sku_code)
)
    comment 't_inventory';

create table ecp_inventory.t_inventory_change_record
(
    id                bigint auto_increment comment '主键'
        primary key,
    order_code        varchar(90)   null comment '订单编号',
    product_sku_code  varchar(90)   not null comment '商品SKU编号',
    change_num        int default 0 not null comment '变更数量',
    current_sales_num int default 0 not null comment '当前可售卖数量',
    current_lock_num  int default 0 not null comment '当前锁定数量',
    event_time        datetime      null comment '记录时间',
    event_type        varchar(32)   not null comment '记录类型',
    created_by        varchar(20)   not null comment '创建人',
    created_time      datetime      not null comment '创建时间',
    updated_by        varchar(20)   null comment '更新人',
    updated_time      datetime      null comment '更新时间',
    is_deleted        int default 0 not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int default 1 not null comment '乐观锁',
    tenant_id         int default 1 not null
)
    comment 't_inventory_change_record';

create table ecp_inventory.t_inventory_sync_record
(
    id               bigint auto_increment comment '主键'
        primary key,
    product_sku_code varchar(90)   not null comment '商品SKU编号',
    gyy_num          int default 0 not null comment '管易云可销售数量数量',
    sync_time        datetime      null comment '同步时间',
    created_by       varchar(20)   not null comment '创建人',
    created_time     datetime      not null comment '创建时间',
    updated_by       varchar(20)   null comment '更新人',
    updated_time     datetime      null comment '更新时间',
    is_deleted       int default 0 not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int default 1 not null comment '乐观锁',
    tenant_id        int default 1 not null
)
    comment 't_inventory_sync_record';

create table ecp_payment.t_invoice_apply_record
(
    id                   bigint auto_increment comment '主键'
        primary key,
    invocie_apply_no     varchar(255) not null comment '发票申请号;发票申请号',
    order_no             varchar(90)  not null comment '订单号;订单号',
    order_no_for_apply   varchar(90)  null comment '申请发票订单号;申请发票订单号，针对二次开票场景',
    order_time           datetime     not null comment '订单时间;订单时间',
    invoice_title_no     varchar(90)  not null comment '发票抬头编号;发票抬头编号',
    order_payment_amount int          not null comment '订单支付金额;订单支付金额',
    total_tax_amount     int          null comment '合计税额;合计税额',
    valorem_total_amount int          null comment '价税合计总额;价税合计总额',
    submit_channel_code  tinyint      not null comment '发票发起渠道 1：路虎小程序 2：捷豹小程序 3：官网 4：APP',
    notify_url           varchar(90)  null comment '邮件结果回调通知地址;邮件结果回调通知地址',
    is_revoke            int          not null comment '是否红冲;0否 1是',
    cipher_text          varchar(255) null comment '发票密文;发票密文',
    pdf_url              varchar(255) null comment '发票pdf URL;发票pdf URL',
    machine_code         varchar(255) null comment '机器码;机器码',
    verify_code          varchar(255) null comment '校验码;校验码',
    qr_code              varchar(255) null comment '二维码;二维码',
    apply_status         tinyint      not null comment '发票申请状态0：待发起    1：开票成功   2：开票失败  3：提交成功 4：提交失败',
    apply_error_msg      varchar(255) null comment '发票申请错误信息',
    invoice_time         varchar(255) null comment '开盘时间;开盘时间',
    invoice_no           varchar(255) null comment '发票编码;发票编码,理论唯一',
    invoice_code         varchar(255) null comment '发票代码;发票代码',
    short_code           varchar(255) null comment '发票短码或短链接;发票短码或短链接',
    request_serial_no    varchar(255) null comment '发票请求流水号;发票请求流水号',
    send_email           varchar(255) null comment '发送邮箱;发送邮箱',
    email_send_result    int          not null comment '发票邮箱发送结果;0否 1是，默认否',
    email_send_time      datetime     null comment '发票邮箱发送时间;邮票发送时间',
    tenant_id            int          not null comment '租户号',
    created_by           varchar(90)  not null comment '创建人',
    created_time         datetime     not null comment '创建时间',
    updated_by           varchar(90)  null comment '更新人',
    updated_time         datetime     null comment '更新时间',
    revision             int          not null comment '乐观锁',
    is_deleted           int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    constraint idx_invocie_apply_no
        unique (invocie_apply_no)
)
    comment 't_invoice_apply_record' charset = utf8mb3
                                     row_format = DYNAMIC;

create index idx_order_no
    on ecp_payment.t_invoice_apply_record (order_no);

create table ecp_payment.t_invoice_apply_record_item
(
    id               bigint auto_increment comment '主键'
        primary key,
    invocie_apply_no varchar(90) not null comment '发票申请号;发票申请号',
    product_no       varchar(90) not null comment '商品编码',
    tax_code         varchar(90) not null comment '税收编码',
    tax_rate         int         not null comment '税率;税率，百分比%',
    tenant_id        int         not null comment '租户号',
    created_by       varchar(90) not null comment '创建人',
    created_time     datetime    not null comment '创建时间',
    updated_by       varchar(90) null comment '更新人',
    updated_time     datetime    null comment '更新时间',
    is_deleted       int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int         not null comment '乐观锁'
)
    comment 't_invoice_apply_record_item' charset = utf8mb3
                                          row_format = DYNAMIC;

create table ecp_payment.t_invoice_change_log
(
    id                  bigint auto_increment comment '主键'
        primary key,
    invoice_type        int         not null comment '发票类型;发票类型 1：电子普票 2：电子专票 3：纸质发票',
    invoice_apply_no    varchar(90) null comment '发票申请单号;发票申请单号，普票有',
    order_code          varchar(90) not null comment '订单号;订单号',
    change_filed        varchar(90) not null comment '变更字段;变更字段',
    change_before_value varchar(90) not null comment '变更前;变更前',
    change_after_value  varchar(90) not null comment '变更后;变更后',
    change_time         datetime    not null comment '变更时间;变更时间',
    tenant_id           int         not null comment '租户号',
    created_by          varchar(90) not null comment '创建人',
    created_time        datetime    not null comment '创建时间',
    updated_by          varchar(90) null comment '更新人',
    updated_time        datetime    null comment '更新时间',
    is_deleted          int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision            int         not null comment '乐观锁'
)
    comment 't_invoice_change_log';

create table ecp_payment.t_invoice_changelog_record
(
    id                            bigint auto_increment comment '主键'
        primary key,
    parent_order_code             varchar(64)    null comment '主订单号',
    order_code                    varchar(64)    null comment '子订单号',
    order_created_time            datetime       null comment '订单创建时间',
    fulfilment_type               int            null comment '履约方式',
    business_code                 varchar(90)    null comment '业务线编码',
    cost_amount                   int            null comment '应付现金金额(含税)',
    cost_amount_no_tax            int            null comment '应付现金金额(不含税)',
    refund_amount                 varchar(64)    null comment '商品退款总金额(含税)',
    refund_amount_no_tax          varchar(64)    null comment '商品退款总金额(不含税)',
    freight_amount                int            null comment '运费',
    invoice_apply_no              varchar(90)    null comment '发票票号',
    invoice_no                    varchar(90)    null comment '发票票号',
    invoice_type                  int            not null comment '发票类型;发票类型 1：电子普票 2：电子专票 3：纸质发票',
    invoice_status                int            null comment '开票状态',
    invoice_status_update_time    datetime       null comment '状态更新时间',
    invoice_amount                int            null comment '开票/红冲金额',
    title_type                    int            null comment '抬头类型',
    invoice_title_name            varchar(90)    null comment '发票抬头',
    title_tax_no                  varchar(90)    null comment '税号',
    invoice_email                 varchar(90)    null comment '接收邮箱',
    paper_invoice_title_name      varchar(90)    null comment '纸质发票抬头',
    paper_title_tax_no            varchar(90)    null comment '纸质发票税号',
    paper_company_mobile          varchar(90)    null comment '企业电话',
    paper_company_address         varchar(255)   null comment '企业注册地址',
    paper_company_bank_name       varchar(90)    null comment '开户行',
    paper_company_bank_account    varchar(90)    null comment '银行账号',
    paper_recipient_name          varchar(90)    null comment '接收人姓名',
    paper_recipient_phone         varchar(90)    null comment '接收人电话',
    paper_recipient_address       varchar(255)   null comment '接收人详细地址',
    especial_invoice_title_name   varchar(90)    null comment '电子专票发票抬头',
    especial_title_tax_no         varchar(90)    null comment '电子专票税号',
    especial_company_mobile       varchar(90)    null comment '电子专票企业电话',
    especial_company_address      varchar(255)   null comment '电子专票企业注册地址',
    especial_company_bank_name    varchar(90)    null comment '电子专票开户行',
    especial_company_bank_account varchar(90)    null comment '电子专票银行账号',
    especial_recipient_name       varchar(90)    null comment '电子专票接收人姓名',
    especial_recipient_phone      varchar(90)    null comment '电子专票接收人电话',
    especial_recipient_address    varchar(255)   null comment '电子专票接收人详细地址',
    tenant_id                     int            not null comment '租户号',
    created_by                    varchar(90)    not null comment '创建人',
    created_time                  datetime       not null comment '创建时间',
    updated_by                    varchar(90)    null comment '更新人',
    updated_time                  datetime       null comment '更新时间',
    is_deleted                    int            not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                      int            not null comment '乐观锁',
    freight_tax                   decimal(12, 2) null comment '运费费率'
)
    comment 't_invoice_changelog_record';

create table ecp_payment.t_invoice_especial_record
(
    id                   bigint auto_increment comment '主键'
        primary key,
    order_no             varchar(90)  not null comment '订单号;订单号',
    apply_time           datetime     null comment '电子专票申请时间',
    invoice_status       int          not null comment '电子专票状态;1：电子专票申请中 2：已开电子专票 3:电子专票已红冲 4:电子专票已重开',
    finish_time          datetime     null comment '完成时间',
    revoke_time          datetime     null comment '红冲时间，状态变更为红冲时记录',
    recipient_name       varchar(90)  null comment '接收人',
    recipient_phone      varchar(90)  null comment '接收人电话',
    recipient_address    varchar(255) null comment '接收人地址',
    invoice_title_name   varchar(90)  not null comment '发票抬头',
    title_type           tinyint      not null comment '1：个人/非企业 2：企业',
    title_tax_no         varchar(90)  null comment '抬头税号',
    company_address      varchar(100) null comment '企业地址',
    company_mobile       varchar(90)  null comment '企业电话',
    company_bank_name    varchar(90)  null comment '开户银行',
    company_bank_account varchar(90)  null comment '开户行账号',
    tenant_id            int          not null comment '租户号',
    created_by           varchar(90)  not null comment '创建人',
    created_time         datetime     not null comment '创建时间',
    updated_by           varchar(90)  null comment '更新人',
    updated_time         datetime     null comment '更新时间',
    is_deleted           int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision             int          not null comment '乐观锁',
    invoice_title_no     varchar(100) null comment '抬头no'
)
    comment 't_invoice_especial_record' charset = utf8mb3;

create index idx_order_no
    on ecp_payment.t_invoice_especial_record (order_no);

create table ecp_payment.t_invoice_paper_record
(
    id                   bigint auto_increment comment '主键'
        primary key,
    order_no             varchar(90)  not null comment '订单号;订单号',
    apply_time           datetime     null comment '纸质票申请时间',
    invoice_status       int          not null comment '纸质票状态;1：纸质票申请中 2：已开纸质票 3:纸质票已红冲 4:纸质票已重开',
    finish_time          datetime     null comment '完成时间',
    revoke_time          datetime     null comment '红冲时间，状态变更为红冲时记录',
    recipient_name       varchar(90)  null comment '接收人',
    recipient_phone      varchar(90)  null comment '接收人电话',
    recipient_address    varchar(255) null comment '接收人地址',
    invoice_title_name   varchar(100) not null comment '发票抬头;发票抬头',
    title_type           int          null comment '发票抬头类型;1：个人/非企业 2：企业',
    title_tax_no         varchar(100) null comment '抬头税号;抬头税号',
    company_address      varchar(255) null comment '企业地址;企业地址',
    company_mobile       varchar(100) null comment '企业电话;企业电话',
    company_bank_name    varchar(100) null comment '开户银行;开户银行',
    company_bank_account varchar(100) null comment '开户行账号;开户行账号',
    invoice_title_no     varchar(100) null comment 'invoice_title_no',
    tenant_id            int          not null comment '租户号',
    created_by           varchar(90)  not null comment '创建人',
    created_time         datetime     not null comment '创建时间',
    updated_by           varchar(90)  null comment '更新人',
    updated_time         datetime     null comment '更新时间',
    is_deleted           int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision             int          not null comment '乐观锁'
)
    comment 't_invoice_paper_record' charset = utf8mb3
                                     row_format = DYNAMIC;

create index idx_order_no
    on ecp_payment.t_invoice_paper_record (order_no);

create table ecp_payment.t_invoice_request_order
(
    id                   bigint auto_increment comment '主键'
        primary key,
    jlr_id               varchar(90)  not null comment '用户JLRID;jlr_id',
    order_no             varchar(90)  not null comment '申请订单号;申请订单号',
    order_payment_amount int          not null comment '订单开票金额;订单开票金额',
    apply_status         int          not null comment '发票申请状态;发票申请状态 0：待发起 1：开票成功 2：开票失败 3:提交成功 4：提交失败',
    is_revoke            int          not null comment '是否红冲;0否 1是',
    apply_error_msg      varchar(255) null comment '申请错误信息;申请错误信息，invoice center返回',
    apply_time           datetime     not null comment '申请时间;申请时间',
    invoice_apply_no     varchar(90)  null comment '发票申请单号;发票申请单号，从invoice center返回',
    invoice_no           varchar(90)  null comment '发票票号',
    finish_time          datetime     null comment '开票完成时间;开票完成时间',
    invoice_title_name   varchar(90)  not null comment '发票抬头;发票抬头',
    title_type           int          null comment '发票抬头类型;1：个人/非企业 2：企业',
    title_tax_no         varchar(90)  null comment '抬头税号;抬头税号',
    invoice_email        varchar(90)  null comment '发票接受邮箱',
    company_address      varchar(90)  null comment '企业地址;企业地址',
    company_mobile       varchar(90)  null comment '企业电话;企业电话',
    company_bank_name    varchar(90)  null comment '开户银行;开户银行',
    company_bank_account varchar(90)  null comment '开户行账号;开户行账号',
    total_tax_amount     int          null comment '合计税额;合计税额',
    valorem_total_amount int          null comment '价税合计总额;价税合计总额',
    pdf_url              varchar(255) null comment '发票pdf URL;发票pdf URL',
    tenant_id            int          not null comment '租户号',
    created_by           varchar(90)  not null comment '创建人',
    created_time         datetime     not null comment '创建时间',
    updated_by           varchar(90)  null comment '更新人',
    updated_time         datetime     null comment '更新时间',
    is_deleted           int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision             int          not null comment '乐观锁'
)
    comment 't_invoice_request_order' charset = utf8mb3;

create table ecp_payment.t_invoice_revoke_record
(
    id              bigint auto_increment comment '主键'
        primary key,
    request_id      varchar(90)  null comment '红冲申请ID，雪花算法ID',
    order_no        varchar(90)  not null comment '订单号;订单号',
    invoice_no      varchar(90)  not null comment '发票编码;发票编码',
    revoke_time     datetime     null comment '红冲时间;红冲时间',
    apply_status    tinyint      not null comment '申请状态，0：红冲成功，1：红冲失败  2：待提交 3：提交成功 4：提交失败',
    apply_error_msg varchar(100) null comment '申请错误信息',
    cipher_text     varchar(255) null comment '发票密文',
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁'
)
    comment 't_invoice_revoke_record' charset = utf8mb3
                                      row_format = DYNAMIC;

create table ecp_payment.t_invoice_revoke_request_order
(
    id                 bigint auto_increment comment '主键'
        primary key,
    order_no           varchar(90)  not null comment '订单号;订单号',
    invoice_apply_no   varchar(90)  not null comment '发票申请单号;发票申请单号，从invoice center返回',
    revoke_apply_no    varchar(90)  null comment '红冲申请单号;红冲申请单号',
    revoke_time        datetime     not null comment '红冲申请时间;红冲申请时间',
    revoke_finish_time datetime     null comment '红冲完成时间;红冲完成时间',
    apply_status       int          not null comment '申请状态;红冲申请状态，0：申请成功，1：申请失败 ,  2：待提交  3:提交成功 4：提交失败',
    apply_error_msg    varchar(255) null comment '申请错误信息;申请错误信息',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁',
    jlr_id             varchar(90)  not null comment '用户JLRID;jlr_id'
)
    comment 't_invoice_revoke_request_order' charset = utf8mb3;

create table ecp_payment.t_invoice_title_info
(
    id                   bigint auto_increment comment '主键'
        primary key,
    invoice_title_no     varchar(90) not null comment '发票抬头编号;发票抬头编号，雪花算法ID',
    consumer_code        varchar(90) not null comment '用户编码',
    invoice_type         varchar(90) not null comment '发票类型;电子发票ONLINE ，纸质发票OFFLINE',
    title_type           int         not null comment '发票抬头类型;1：个人/非企业 2：企业',
    invoice_title_name   varchar(90) not null comment '发票抬头;发票抬头',
    title_tax_no         varchar(90) not null comment '抬头税号;抬头税号',
    invoice_email        varchar(90) null comment '发票接受邮箱',
    company_address      varchar(90) null comment '企业地址;企业地址',
    company_mobile       varchar(90) null comment '企业电话;企业电话',
    company_bank_name    varchar(90) null comment '开户银行;开户银行',
    company_bank_account varchar(90) null comment '开户行账号;开户行账号',
    is_default           int         not null comment '是否默认;1：是 0：否',
    tenant_id            int         not null comment '租户号',
    created_by           varchar(90) not null comment '创建人',
    created_time         datetime    not null comment '创建时间',
    updated_by           varchar(90) null comment '更新人',
    updated_time         datetime    null comment '更新时间',
    is_deleted           int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision             int         not null comment '乐观锁'
)
    comment 't_invoice_title_info' charset = utf8mb3
                                   row_format = DYNAMIC;

create index idx_consumer_code_invoice_title_name
    on ecp_payment.t_invoice_title_info (consumer_code, invoice_title_name);

create index idx_invoice_title_no
    on ecp_payment.t_invoice_title_info (invoice_title_no);

create index idx_title_tax_no
    on ecp_payment.t_invoice_title_info (title_tax_no);

create table ecp_product.t_kingdee_sku_tax
(
    id             bigint auto_increment comment '主键'
        primary key,
    tax_id         bigint         not null comment '税率ID;税率ID，雪花算法',
    tax_rate       decimal(12, 2) not null comment '税率值;税率，精度为小数点后两位',
    tax_code       varchar(90)    null comment '税务编码;税务编码',
    kingdee_sku_id varchar(90)    null comment '金蝶skuId',
    tenant_id      int            not null comment '租户号',
    created_by     varchar(90)    not null comment '创建人',
    created_time   datetime       not null comment '创建时间',
    updated_by     varchar(90)    null comment '更新人',
    updated_time   datetime       null comment '更新时间',
    is_deleted     int            not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int            not null comment '乐观锁',
    constraint t_kingdee_sku_tax_tax_id_IDX
        unique (tax_id)
)
    comment '金蝶skuId税率表';

create table ecp_product.t_kingdee_sku_tax_temp
(
    id             bigint auto_increment comment '主键'
        primary key,
    kingdee_sku_id varchar(90)                        not null comment '金蝶skuId',
    tax_code       varchar(90)                        not null comment '税务编码;税务编码',
    tax_rate       varchar(90)                        not null comment '税率值;税率，精度为小数点后两位',
    created_time   datetime default CURRENT_TIMESTAMP null comment '创建时间'
)
    comment '金蝶skuId税率temp表';

create table ecp_subscription.t_manual_modify_log
(
    id                  bigint auto_increment comment '主键'
        primary key,
    car_vin             varchar(90)  not null comment 'VIN;VIN',
    modify_type         tinyint      not null comment '修改类型;修改类型 1：AMAP修改 2：APPDCU修改 3：ICCID修改 4：发票日期修改 5：手动补录VIN',
    modify_before_value varchar(255) not null comment '修改前值;修改前值',
    modify_after_value  varchar(255) not null comment '修改后值;修改后值',
    operator            varchar(255) not null comment '修改人;修改人',
    operate_time        datetime     not null comment '修改时间;修改时间',
    tenant_id           int          not null comment '租户号',
    created_by          varchar(90)  not null comment '创建人',
    created_time        datetime     not null comment '创建时间',
    updated_by          varchar(90)  null comment '更新人',
    updated_time        datetime     null comment '更新时间',
    is_deleted          int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision            int          not null comment '乐观锁'
)
    comment 't_manual_modify_log';

create table ecp_system.t_master_data_area
(
    code          varchar(255) not null
        primary key,
    name          varchar(255) null,
    city_code     varchar(255) null,
    province_code varchar(255) null
)
    collate = utf8mb4_general_ci;

create table ecp_system.t_master_data_city
(
    code          varchar(255) not null
        primary key,
    name          varchar(255) null,
    province_code varchar(255) null
)
    collate = utf8mb4_general_ci;

create table ecp_system.t_master_data_province
(
    code   varchar(255) not null
        primary key,
    name   varchar(255) null,
    region varchar(255) null
)
    collate = utf8mb4_general_ci;

create table ecp_product.t_merchant_account
(
    id                    bigint auto_increment comment '主键'
        primary key,
    business_code         varchar(50)  not null comment '业务线',
    fulfillment_type      int          not null comment '履约方式',
    merchant_account_no   varchar(50)  not null comment '商户号',
    merchant_account_name varchar(100) not null comment '商户名称',
    tenant_id             bigint       not null comment '租户号',
    revision              int          not null comment '乐观锁',
    created_by            varchar(90)  not null comment '创建人',
    created_time          datetime     not null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          not null comment '逻辑删除;逻辑删除0否1是'
)
    comment '商户号配置表';

create table ecp_notification.t_message_template
(
    id                 bigint auto_increment comment '主键'
        primary key,
    business_code      varchar(90)  not null comment '业务线编码;业务线编码',
    template_code      varchar(90)  null comment '模板编码;模板编码',
    template_name      varchar(90)  not null comment '模板名称;模板名称',
    template_type      int          not null comment '模板类型;模板类型 1自动配置 2手动配置',
    auto_type          int          not null,
    template_content   varchar(900) not null comment '模板内容;模板对应的通知内容，内部变量用NULL的方式来表示',
    template_variables varchar(255) null comment '模板变量;模板变量，json内容:{{"brandAndModel":"品牌+车型","serviceName":"服务名称","expireDate":"到期时间","wxUrl":"微信小程序链接"}',
    template_remarks   varchar(900) not null comment '模板场景说明;模板场景说明',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁',
    constraint idx_unique_template_code
        unique (template_code)
)
    comment 't_message_template' charset = utf8mb3
                                 row_format = DYNAMIC;

create index idx_business_code_updated_time
    on ecp_notification.t_message_template (business_code, updated_time);

create table ecp_system.t_mini_home_config
(
    id                    bigint auto_increment comment '主键'
        primary key,
    background_url        varchar(255) not null comment '背景图URL;背景图URL',
    focus_banner_spu_code varchar(90)  null comment '焦点商品编码;焦点商品编码',
    product_spu_list      json         not null comment '商品列表SPU LIST;商品列表SPU LIST，{{"spu":"xxx","sort":"1"},{"spu":"yyy","sort":"2"}}',
    product_category_name varchar(90)  not null comment '商品分类显示名称',
    brand_code            varchar(90)  not null comment '品牌code，LAN:路虎，JAG：捷豹',
    tenant_id             int          not null comment '租户号',
    created_by            varchar(90)  not null comment '创建人',
    created_time          datetime     not null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int          not null comment '乐观锁'
)
    comment 't_mini_home_config' collate = utf8mb4_general_ci
                                 row_format = DYNAMIC;

create table ecp_system.t_mini_home_config_draft
(
    id                    bigint auto_increment comment '主键'
        primary key,
    background_url        varchar(255) null comment '背景图URL;背景图URL',
    focus_banner_spu_code varchar(90)  not null comment '焦点商品编码;焦点商品编码',
    product_category_name varchar(90)  null comment '商品分类显示名称;商品分类显示名称',
    product_spu_list      json         not null comment '商品列表SPU LIST;商品列表SPU LIST，{{"spu":"xxx","sort":"1"},{"spu":"yyy","sort":"2"}}',
    remote_service_spu    varchar(90)  null comment 'remoteService短链商品SPU;remoteService短链商品SPU',
    pivi_service_spu      varchar(90)  null comment 'PIVIService短链商品SPU;PIVIService短链商品SPU',
    brand_code            varchar(90)  not null comment '品牌code;品牌code，LAN:路虎，JAG：捷豹',
    latest_release_date   varchar(255) null comment '最新发布时间;最新发布时间',
    release_status        int          null comment '发布状态;发布状态 0未发布 1已发布',
    tenant_id             int          not null comment '租户号',
    created_by            varchar(90)  not null comment '创建人',
    created_time          datetime     not null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int          not null comment '乐观锁'
)
    comment 't_mini_home_config_draft';

create table ecp_notification.t_minicode_config
(
    id                 bigint auto_increment comment '主键'
        primary key,
    record_code        varchar(90)  not null comment '记录编码;记录编码',
    wmp_brand          varchar(64)  not null comment '品牌标识',
    route_page_type    varchar(64)  not null comment '路由页面类型',
    product_code_param varchar(64)  null comment '产品编码参数',
    product_name_param varchar(255) null comment '产品名称参数',
    param_1            varchar(90)  null comment '参数1',
    param_2            varchar(90)  null comment '参数2',
    param_3            varchar(90)  null comment '参数3',
    param_4            varchar(90)  null comment '参数4',
    param_5            varchar(90)  null comment '参数5',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁'
)
    comment 't_minicode_config';

create table ecp_notification.t_minicode_generate_records
(
    id             bigint auto_increment comment '主键'
        primary key,
    record_code    varchar(255) not null comment '记录编码;雪花算法ID',
    route_path_url varchar(255) not null comment '小程序路由URL;小程序跳转页面',
    config_id      int          null comment '配置参数ID;配置参数ID',
    result         int          null comment '生成结果状态码（如：0 成功，1 失败）',
    error_code     varchar(64)  null comment '错误码',
    error_message  varchar(900) null comment '错误信息描述',
    tenant_id      int          not null comment '租户号',
    created_by     varchar(90)  not null comment '创建人',
    created_time   datetime     not null comment '创建时间',
    updated_by     varchar(90)  null comment '更新人',
    updated_time   datetime     null comment '更新时间',
    is_deleted     int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int          not null comment '乐观锁'
)
    comment 't_minicode_generate_records';

create table ecp_notification.t_mp_notice_expire_subscription
(
    id                         bigint auto_increment comment '自增主键id'
        primary key,
    jlr_id                     varchar(90)   not null comment '用户全局唯一ID',
    open_id                    varchar(90)   not null comment '小程序的openId',
    service_expiration_consent int           not null comment '服务到期的consent',
    tenant_id                  int           not null comment '租户号',
    created_by                 varchar(90)   not null comment '创建人',
    created_time               datetime      not null comment '创建时间',
    updated_by                 varchar(90)   null comment '更新人',
    updated_time               datetime      null comment '更新时间',
    is_deleted                 int default 0 not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                   int           not null comment '乐观锁'
)
    comment '小程序模板服务到期通知订阅表';

create table ecp_notification.t_mp_notice_order_subscription
(
    id                 bigint auto_increment comment '自增主键id'
        primary key,
    jlr_id             varchar(90)   not null comment '用户全局唯一ID',
    open_id            varchar(90)   not null comment '小程序的openId',
    order_success_flag int           not null comment '订单成功模板标识，0:没订阅、1：订阅',
    order_cancel_flag  int           not null comment '订单取消模板标识，0:没订阅、1：订阅',
    order_no           varchar(90)   not null comment '订单编号',
    tenant_id          int           not null comment '租户号',
    created_by         varchar(90)   not null comment '创建人',
    created_time       datetime      not null comment '创建时间',
    updated_by         varchar(90)   null comment '更新人',
    updated_time       datetime      null comment '更新时间',
    is_deleted         int default 0 not null comment '是否删除;0：否 1：是',
    revision           int           not null comment '乐观锁'
)
    comment '小程序模板订单变化、取消订单订阅通知表';

create table ecp_notification.t_mp_template_notice_record
(
    id           bigint auto_increment comment '自增主键id'
        primary key,
    message_id   varchar(90)   null comment '新增的消息ID',
    jlr_id       varchar(90)   not null comment '用户全局唯一ID',
    open_id      varchar(90)   not null comment '小程序的openId',
    sns_topic    varchar(90)   not null comment 'sns的topic',
    event_type   varchar(90)   not null comment '事件类型',
    order_no     varchar(90)   null comment '订单编号',
    car_vin      varchar(90)   null comment '车辆编号',
    send_message json          not null comment '发送消息，包含了发送的参数',
    tenant_id    int           not null comment '租户号',
    created_by   varchar(90)   not null comment '创建人',
    created_time datetime      not null comment '创建时间',
    updated_by   varchar(90)   null comment '更新人',
    updated_time datetime      null comment '更新时间',
    is_deleted   int default 0 not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int           not null comment '乐观锁'
)
    comment '小程序模板通知发送记录表';

create table ecp_notification.t_msg_black_list
(
    id           bigint auto_increment
        primary key,
    car_vin      varchar(90) not null,
    phone        varchar(90) null,
    tenant_id    tinyint     not null,
    created_by   varchar(90) not null,
    created_time datetime    not null,
    updated_by   varchar(90) not null,
    updated_time datetime    not null,
    is_deleted   tinyint     not null,
    revision     int         not null,
    constraint car_vin
        unique (car_vin)
);

create table ecp_notification.t_notification_auto_task
(
    id                    bigint auto_increment comment '主键'
        primary key,
    business_code         varchar(90) null comment '业务线标识码',
    task_code             varchar(90) not null comment '任务编码;任务编码，雪花算法',
    task_name             varchar(90) not null comment '任务名称;任务名称',
    task_time_type        int         not null comment '发送时间类型;发送时间类型 1：永久循环发送 2：限定时间发送',
    range_begin_date      datetime    null comment '范围开始时间;范围开始时间',
    range_end_date        datetime    null comment '范围结束时间;范围结束时间',
    message_template_code varchar(90) not null comment '发送模板消息编码;发送模板消息编码',
    daily_send_time       varchar(90) not null comment '指定每天的发送时间-时分;指定每天的发送时间时分，如10:00',
    status                int         not null comment '通知任务状态;状态;通知状态  0停用 1启用 2待启用 3已发送',
    activate_time         datetime    null comment '启用时间;启用时间',
    deactivate_time       datetime    null comment '停用时间;停用时间',
    trigger_action        varchar(90) not null comment '触发逻辑;触发逻辑',
    task_send_type        int         null comment '发送类型;发送类型 1：实时发送 2：定时发送',
    notify_spu_code       varchar(90) null comment '通知短信商品编码',
    tenant_id             int         not null comment '租户号',
    created_by            varchar(90) not null comment '创建人',
    created_time          datetime    not null comment '创建时间',
    updated_by            varchar(90) null comment '更新人',
    updated_time          datetime    null comment '更新时间',
    is_deleted            int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int         not null comment '乐观锁'
)
    comment 't_notification_auto_task' charset = utf8mb3;

create table ecp_notification.t_notification_config
(
    id                      bigint auto_increment
        primary key,
    remote_service_spu      varchar(90) not null,
    remote_service_spu_name varchar(90) not null,
    pivi_service_spu        varchar(90) not null,
    pivi_service_spu_name   varchar(90) not null,
    brand_code              varchar(90) not null,
    tenant_id               int         not null,
    created_by              varchar(90) not null,
    created_time            datetime    not null,
    updated_by              varchar(90) null,
    updated_time            datetime    null,
    is_deleted              int         not null,
    revision                int         not null
)
    comment 't_notification_config' charset = utf8mb3;

create table ecp_notification.t_notification_config_modify_log
(
    id             bigint auto_increment
        primary key,
    config_id      bigint       not null,
    modify_content varchar(900) not null,
    modify_date    datetime     not null,
    modify_user    varchar(90)  not null,
    tenant_id      int          not null,
    created_by     varchar(90)  not null,
    created_time   datetime     not null,
    updated_by     varchar(90)  null,
    updated_time   datetime     null,
    is_deleted     int          not null,
    revision       int          not null
)
    comment 't_notification_config_modify_log' charset = utf8mb3;

create table ecp_notification.t_notification_history
(
    id                       bigint auto_increment comment '主键'
        primary key,
    business_code            varchar(90)  null comment '业务线标识码',
    task_code                varchar(90)  not null comment '通知任务编码;通知任务编码',
    task_instance_code       varchar(100) not null comment '任务实例编码',
    task_send_time           datetime     not null comment '通知推送时间;通知推送时间',
    send_total_count         int          not null comment '总推送条数;总推送条数',
    send_success_count       int          null comment '推送成功条数;推送成功条数',
    send_fail_count          int          null comment '推送失败条数;推送失败条数',
    tenant_id                int          not null comment '租户号',
    created_by               varchar(90)  not null comment '创建人',
    created_time             datetime     not null comment '创建时间',
    updated_by               varchar(90)  null comment '更新人',
    updated_time             datetime     null comment '更新时间',
    is_deleted               int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                 int          not null comment '乐观锁',
    reach_user_success_count int          null comment '触达用户成功条数',
    reach_user_fail_count    int          null comment '触达用户失败条数',
    reach_result_status      tinyint      null comment '触达用户结果统计是否完成',
    open_short_link_count    int          null comment '短链点击数',
    constraint idx_unique_task_instance_code
        unique (task_instance_code)
)
    comment 't_notification_history' charset = utf8mb3
                                     row_format = DYNAMIC;

create index idx_task_code_instance_code
    on ecp_notification.t_notification_history (task_code, task_instance_code);

create index idx_task_code_send_time
    on ecp_notification.t_notification_history (task_code, task_send_time);

create table ecp_notification.t_notification_history_detail
(
    id                 bigint auto_increment comment '主键'
        primary key,
    business_code      varchar(90)  null comment '业务线标识码',
    msgid              varchar(255) null comment '信息发送UUID',
    task_instance_code varchar(90)  not null comment '通知任务实例编码;通知任务实例编码，雪花算法',
    send_message       varchar(900) not null comment '发送内容;发送内容',
    send_phone         varchar(90)  not null comment '发送手机号;发送手机号',
    car_vin            varchar(90)  null,
    send_time          datetime     null comment '发送时间',
    send_result        int          not null comment '发送结果;发送结果 0失败 1成功 2待发送',
    submit_error_code  varchar(255) null comment '提交错误编码，ECP到德成',
    error_message      varchar(255) null comment '发送失败原因;发送失败原因',
    task_code          varchar(255) null comment '任务编码;任务编码',
    brand_code         varchar(90)  null comment '品牌code，该条短信发送给捷豹或路虎，用于签名和短链的生成',
    task_send_type     int          null comment '任务发送;发送类型 1：实时发送 2：定时发送',
    msg_result         varchar(255) null comment '最终短信触达结果',
    msg_error_desc     varchar(255) null comment '短信触达错误描述',
    wg_code            varchar(255) null comment '短信触达运营商错误码',
    open_short_link    tinyint      null comment '是否打开短链，0否 1是',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁'
)
    comment 't_notification_history_detail' charset = utf8mb3
                                            row_format = DYNAMIC;

create index idx_car_vin
    on ecp_notification.t_notification_history_detail (car_vin);

create index idx_send_phone
    on ecp_notification.t_notification_history_detail (send_phone);

create index idx_task_code_created_time
    on ecp_notification.t_notification_history_detail (task_code, created_time);

create index idx_task_instance_code_send_phone
    on ecp_notification.t_notification_history_detail (task_instance_code, send_phone);

create table ecp_notification.t_notification_task
(
    id                      bigint auto_increment comment '主键'
        primary key,
    business_code           varchar(90)  null comment '业务线标识码',
    tenant_id               int          not null comment '租户号',
    created_by              varchar(90)  not null comment '创建人',
    created_time            datetime     not null comment '创建时间',
    updated_by              varchar(90)  null comment '更新人',
    updated_time            datetime     null comment '更新时间',
    is_deleted              int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                int          not null comment '乐观锁',
    task_code               varchar(90)  not null comment '任务编码;任务编码，雪花算法',
    task_name               varchar(90)  not null comment '任务名称;任务名称',
    trigger_action          varchar(90)  not null comment '触发逻辑;触发逻辑',
    task_send_schedule_time varchar(90)  null comment '定时发送时间CRON;当发送类型为定时发送时的时间的CRON表达式',
    task_send_type          int          not null comment '发送类型;发送类型 1：实时发送 2：定时发送',
    message_template_code   varchar(90)  null comment '通知模板编码;通知模板编码',
    status                  int          not null comment '状态;通知状态  0停用 1启用 2待启用 3已发送',
    message_file            varchar(255) null comment '消息发送文件URL',
    error_msg_file          varchar(255) null comment '错误消息文件URL',
    draft_version           tinyint      not null comment '是否是草稿',
    activate_time           datetime     null comment '启用时间;启用时间',
    deactivate_time         datetime     null comment '停用时间;停用时间',
    task_send_real_time     datetime     null comment '实时发送任务时间',
    task_type               tinyint      null comment '任务类型 1自动任务 2手动任务',
    submit_file_type        tinyint      null comment '上传文件类型 1：手机号 2：VIN+手机号',
    sign_brand_text         tinyint      null comment '品牌签名内容 1路虎中国 2捷豹中国',
    send_channel            tinyint      null comment '发送通道 1营销短信 2通知短信',
    auto_task_type          tinyint      null comment '自动通知任务类型，1：周期通知任务 2：事件触发任务'
)
    comment 't_notification_task' charset = utf8mb3
                                  row_format = DYNAMIC;

create index idx_task_code_type_updated
    on ecp_notification.t_notification_task (task_code, task_type, updated_time);

create table ecp_notification.t_notification_template_modify_log
(
    id                     bigint auto_increment comment '主键'
        primary key,
    template_code          varchar(90) null comment '模板编码',
    modify_module          varchar(90) not null comment '修改模块;修改模块',
    modify_field_count     int         null comment '修改字段数量;修改字段数量',
    modify_field_old_value text        null comment '修改前字段值;涉及修改字段旧值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleOld","rule_desc":"descOld"}',
    modify_field_new_value text        null comment '修改后字段值;涉及修改字段新值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleNew","rule_desc":"descNew"}',
    operate_time           datetime    null comment '修改时间;修改时间',
    operate_user           varchar(90) null comment '修改人账号;修改人账号',
    tenant_id              int         not null comment '租户号',
    created_by             varchar(90) not null comment '创建人',
    created_time           datetime    not null comment '创建时间',
    updated_by             varchar(90) null comment '更新人',
    updated_time           datetime    null comment '更新时间',
    is_deleted             int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int         not null comment '乐观锁'
)
    comment 't_notification_template_modify_log';

create table ecp_order.t_order_coupon_detail
(
    id                    bigint auto_increment comment '主键'
        primary key,
    order_code            varchar(90)   not null comment '订单编号',
    order_item_code       varchar(90)   not null comment '订单明细编号',
    coupon_model_code     varchar(255)  null comment '卡券模版 CODE',
    independent_status    int           null comment '分账状态',
    independent_succ_time datetime      null comment '分账成功时间',
    coupon_code           varchar(255)  not null comment '卡券code',
    status                int           not null comment '卡券状态: 1-入库; 2-未生效; 3-待使用; 4-已作废; 5-已核销; 6-已过期',
    valid_start_time      datetime      null comment '有效期开始时间',
    valid_end_time        datetime      null comment '有效期结束时间',
    used_time             datetime      null comment '核销时间',
    send_time             datetime      null comment '发券时间',
    created_by            varchar(90)   not null comment '创建人',
    created_time          datetime      not null comment '创建时间',
    updated_by            varchar(90)   null comment '更新人',
    updated_time          datetime      null comment '更新时间',
    is_deleted            int           not null comment '是否删除（0:否 1:是）',
    tenant_id             int           not null comment '租户号',
    revision              int default 1 not null comment '乐观锁'
)
    comment '卡券订单明细信息表';

create index t_order_coupon_detail_coupon_code_index
    on ecp_order.t_order_coupon_detail (coupon_code);

create table ecp_logistics.t_order_delivery
(
    id              bigint auto_increment comment '主键'
        primary key,
    order_code      varchar(90)                        not null comment '订单编码',
    order_item_code varchar(90)                        null comment '订单item编码',
    logistics_code  varchar(64)                        not null comment '快递公司编码',
    logistics_name  varchar(128)                       not null comment '快递公司名称',
    tracking_number varchar(128)                       not null comment '快递运单号',
    split           tinyint  default 1                 not null comment '是否拆分发货(1:整单发货 2:拆单发货)',
    operator        varchar(32)                        null comment '操作人',
    tenant_id       bigint                             not null comment '租户ID',
    created_by      varchar(90)                        null comment '创建者',
    created_time    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by      varchar(90)                        null comment '更新者',
    updated_time    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted      bit      default b'0'              not null comment '是否删除',
    revision        int                                null comment '乐观锁'
)
    comment '订单发货信息表';

create index idx_oid
    on ecp_logistics.t_order_delivery (order_item_code);

create index idx_tracking_number
    on ecp_logistics.t_order_delivery (tracking_number);

create index uk_tid
    on ecp_logistics.t_order_delivery (order_code);

create table ecp_order.t_order_discount_detail
(
    id                    bigint auto_increment comment '主键'
        primary key,
    order_code            varchar(90)  not null comment '订单编码',
    order_item_code       varchar(90)  not null comment '订单行编码',
    discount_type         int          null comment '优惠类型（1：积分，2：优惠券）',
    coupon_code           varchar(100) null comment '优惠券编码',
    cost_points           int          null comment '消费的积分',
    discount_amount       int          null comment '优惠金额',
    tenant_id             varchar(90)  null comment '租户号',
    revision              int          null comment '乐观锁',
    created_by            varchar(90)  null comment '创建人',
    created_time          datetime     null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          null comment '是否删除;逻辑删除，0否 1是',
    coupon_model_code     varchar(100) null comment '卡券模版code',
    coupon_model_name     varchar(100) null comment '卡券模版名称',
    coupon_model_classify int          null comment '卡券模板类型：1-兑换券 2-代金券 3-折扣券 4-满减券'
)
    comment '订单行优惠分摊明细';

create table ecp_order.t_order_gift_address
(
    id              bigint auto_increment comment '主键'
        primary key,
    order_code      varchar(90)  not null comment '订单编号;订单编号',
    province_code   varchar(255) not null comment '省编码;省编码',
    city_code       varchar(255) not null comment '市编码;市编码',
    area_code       varchar(255) not null comment '区编码;区编码',
    detail_address  varchar(255) not null comment '详细地址;详细地址',
    recipient       varchar(255) not null comment '收件人;收件人',
    recipient_phone varchar(255) not null comment '收件人电话;收件人电话',
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁'
)
    comment 't_order_gift_address';

create table ecp_system.t_order_gift_address
(
    id              bigint auto_increment comment '主键'
        primary key,
    order_code      varchar(90)  not null comment '订单编号;订单编号',
    province_code   varchar(255) not null comment '省编码;省编码',
    city_code       varchar(255) not null comment '市编码;市编码',
    area_code       varchar(255) not null comment '区编码;区编码',
    detail_address  varchar(255) not null comment '详细地址;详细地址',
    recipient       varchar(255) not null comment '收件人;收件人',
    recipient_phone varchar(255) not null comment '收件人电话;收件人电话',
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁'
)
    comment 't_order_gift_address';

create table ecp_order.t_order_independent
(
    id                    bigint auto_increment comment '主键'
        primary key,
    independent_code      varchar(60) not null comment '分账申请单号',
    independent_type      varchar(60) not null comment '分账类型',
    order_code            varchar(90) not null comment '订单号',
    coupon_code           varchar(90) null comment '券编码，分账类型为“券核销”时有值',
    consumer_code         varchar(90) not null comment '用户编码',
    pay_apply_no          varchar(90) not null comment '支付中心交易流水号',
    total_div_amt         int         not null comment '分账金额，单位分',
    business_code         varchar(90) not null comment '业务线编码',
    independent_status    int         not null comment '分账状态',
    trans_apply_no        varchar(90) null comment '支付中心分账申请编号',
    independent_succ_time datetime    null comment '分账成功时间',
    tenant_id             int         not null comment '租户号',
    created_by            varchar(90) not null comment '创建人',
    created_time          datetime    not null comment '创建时间',
    updated_by            varchar(90) null comment '更新人',
    updated_time          datetime    null comment '更新时间',
    is_deleted            int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int         not null comment '乐观锁',
    constraint unique_toi_independent_code
        unique (independent_code)
)
    comment '分账申请表';

create index idx_toi_order_code
    on ecp_order.t_order_independent (order_code);

create table ecp_order.t_order_independent_item
(
    id                  bigint auto_increment comment '主键'
        primary key,
    independent_code    varchar(60) not null comment '分账申请单号',
    independent_type    varchar(60) not null comment '分账类型',
    order_code          varchar(90) not null comment '订单号',
    div_amt             int         not null comment '分账金额，单位分',
    merchant_account_no varchar(90) not null comment '商户号',
    tenant_id           int         not null comment '租户号',
    created_by          varchar(90) not null comment '创建人',
    created_time        datetime    not null comment '创建时间',
    updated_by          varchar(90) null comment '更新人',
    updated_time        datetime    null comment '更新时间',
    is_deleted          int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision            int         not null comment '乐观锁'
)
    comment '分账申请明细表(商户号维度)';

create index idx_toii_independent_code
    on ecp_order.t_order_independent_item (independent_code);

create index idx_toii_order_code
    on ecp_order.t_order_independent_item (order_code);

create table ecp_order.t_order_independent_order_item
(
    id                  bigint auto_increment comment '主键'
        primary key,
    independent_code    varchar(60) not null comment '分账申请单号',
    independent_type    varchar(60) not null comment '分账类型',
    order_code          varchar(90) not null comment '订单号',
    order_item_code     varchar(60) not null comment '订单行编号',
    div_amt             int         not null comment '分账金额，单位分',
    merchant_account_no varchar(90) not null comment '商户号',
    tenant_id           int         not null comment '租户号',
    created_by          varchar(90) not null comment '创建人',
    created_time        datetime    not null comment '创建时间',
    updated_by          varchar(90) null comment '更新人',
    updated_time        datetime    null comment '更新时间',
    is_deleted          int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision            int         not null comment '乐观锁'
)
    comment '分账申请明细表(订单行维度)';

create index idx_toioi_independent_code
    on ecp_order.t_order_independent_order_item (independent_code);

create index idx_toioi_order_code
    on ecp_order.t_order_independent_order_item (order_code);

create table ecp_order.t_order_info
(
    id                          bigint auto_increment comment '主键'
        primary key,
    consumer_code               varchar(90)       not null comment '用户编码;用户编码',
    order_code                  varchar(90)       not null comment '订单编码;订单编码，编码规则：业务线code+时间戳(日期+时分秒)+6位随机数',
    original_fee_total_amount   int               not null comment '原始订单金额;原始订单金额，单位分',
    fee_total_amount            int               not null comment '应付金额;订单金额，单位分',
    cost_amount                 int               null comment '实付金额;实付金额，单位分',
    discount_total_amount       int               not null comment '折扣金额;折扣金额，单位分',
    exclude_tax_amount          int               null comment '不含税金额',
    tax_amount                  int               null comment '订单税费',
    freight_amount              int               not null comment '运费金额;运费金额，虚拟商品为0',
    parent_order_code           varchar(90)       not null comment '父订单号;父订单号，用于标记多个子订单从属于哪个订单，当只有一个订单时为本单号',
    order_status                int               not null comment '订单状态;订单状态；
1：已下单
2：已支付
3：订单完成
4：订单关闭
5：售后处理中
6：订单部分取消
7：订单整单取消  ',
    payment_status              int               not null comment '支付状态;支付状态；0:未支付 1：已支付',
    payment_time                datetime          null comment '支付时间;支付时间',
    order_time                  datetime          not null comment '订单提交时间;订单提交时间',
    order_channel               int               not null comment '下单渠道
1 路虎小程序
2捷豹小程序
3官网 （暂未使用）
4app  （暂未使用）
5代客下单 ',
    order_type                  int               not null comment '履约方式：
1：PIVI-Remote
2：PIVI-Online Pack
3：实物商品子订单
4：组合商品
5. 电子兑换券',
    wx_nick_name                varchar(100)      null comment '微信昵称',
    wx_phone                    varchar(1000)     null comment '微信授权手机',
    wx_phone_mix                varchar(90)       null comment '半隐藏微信手机号',
    wx_phone_md5                varchar(90)       null comment '微信手机号MD5',
    customer_remark             varchar(255)      null comment '客户留言信息;客户留言信息',
    contact_phone               varchar(1000)     null comment '客户联系手机;客户联系手机',
    contact_phone_mix           varchar(90)       null comment '半隐藏联系人手机号',
    contact_phone_md5           varchar(90)       null comment '联系人手机号MD5',
    operator_remark             varchar(255)      null comment '操作员备注',
    brand_code                  varchar(90)       null comment '品牌code',
    business_code               varchar(90)       null comment '业务线编码',
    refund_status               int               not null comment '退款状态;退款状态  0：未退款 1：部分退款 2：全退款',
    order_close_reason          varchar(90)       null comment '订单关闭原因;订单退款状态',
    gift_address                tinyint           null comment '领取礼物地址：0否 1是',
    tenant_id                   int               not null comment '租户号',
    created_by                  varchar(90)       not null comment '创建人',
    created_time                datetime          not null comment '创建时间',
    updated_by                  varchar(90)       null comment '更新人',
    updated_time                datetime          null comment '更新时间',
    is_deleted                  int               not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                    int               not null comment '乐观锁',
    completed_time              datetime          null comment '订单完成时间',
    point_amount                int               null comment '积分',
    independent_status          int     default 0 null comment '分账状态 0-无需分账 1-待分账 2-分账中 3-已分账',
    closed_time                 datetime          null comment '订单关闭时间',
    logistics_status            int               null comment '90101：待支付  90301：订单完成  90401：订单关闭  90501：售后处理中，90201：待发货，90202：部分发货，90203：全部发货',
    coupon_status               int               null comment '90101：待支付 90401：订单关闭  90501：售后处理中，90201：待发放，90202：待核销，90301：已核销',
    freight_code                varchar(100)      null comment '税收编码',
    freight_tax                 decimal(12, 2)    null comment '运费费率',
    freight_tax_update_time     datetime          null comment '运费费率更新时间',
    original_exclude_tax_amount int               null comment '售价不含税订单金额',
    product_quantity            int               null comment '商品数量',
    erp_sync                    tinyint default 0 null comment '是否同步给erp系统: 1-已同步 0-未同步',
    constraint t_order_info_order_code_IDX
        unique (order_code)
)
    comment 't_order_info' row_format = DYNAMIC;

create index idx_business_order_time
    on ecp_order.t_order_info (business_code, order_time);

create index t_order_info_consumer_code_index
    on ecp_order.t_order_info (consumer_code);

create index t_order_info_contact_phone_md5_IDX
    on ecp_order.t_order_info (contact_phone_md5);

create index t_order_info_created_time_IDX
    on ecp_order.t_order_info (created_time);

create index t_order_info_parent_order_code_IDX
    on ecp_order.t_order_info (parent_order_code);

create index t_order_info_wx_phone_md5_IDX
    on ecp_order.t_order_info (wx_phone_md5);

create table ecp_order.t_order_info_backup_20250319
(
    id                        bigint default 0 not null comment '主键',
    consumer_code             varchar(90)      not null comment '用户编码;用户编码',
    order_code                varchar(90)      not null comment '订单编码;订单编码，编码规则：业务线code+时间戳(日期+时分秒)+6位随机数',
    original_fee_total_amount int              not null comment '原始订单金额;原始订单金额，单位分',
    fee_total_amount          int              not null comment '应付金额;订单金额，单位分',
    cost_amount               int              null comment '实付金额;实付金额，单位分',
    discount_total_amount     int              not null comment '折扣金额;折扣金额，单位分',
    exclude_tax_amount        int              null comment '不含税金额',
    tax_amount                int              null comment '订单税费',
    freight_amount            int              not null comment '运费金额;运费金额，虚拟商品为0',
    parent_order_code         varchar(90)      not null comment '父订单号;父订单号，用于标记多个子订单从属于哪个订单，当只有一个订单时为本单号',
    order_status              int              not null comment '订单状态;订单状态；
1：已下单
2：已支付
3：订单完成
4：订单关闭
5：售后处理中
6：订单部分取消
7：订单整单取消  ',
    payment_status            int              not null comment '支付状态;支付状态；0:未支付 1：已支付',
    payment_time              datetime         null comment '支付时间;支付时间',
    order_time                datetime         not null comment '订单提交时间;订单提交时间',
    order_channel             int              not null comment '下单渠道
1 路虎小程序
2捷豹小程序
3官网 （暂未使用）
4app  （暂未使用）
5代客下单 ',
    order_type                int              not null comment '履约方式：
1：PIVI-Remote
2：PIVI-Online Pack
3：实物商品子订单
4：组合商品
5. 电子兑换券',
    wx_nick_name              varchar(100)     null comment '微信昵称',
    wx_phone                  varchar(1000)    null comment '微信授权手机',
    wx_phone_mix              varchar(90)      null comment '半隐藏微信手机号',
    wx_phone_md5              varchar(90)      null comment '微信手机号MD5',
    customer_remark           varchar(255)     null comment '客户留言信息;客户留言信息',
    contact_phone             varchar(1000)    null comment '客户联系手机;客户联系手机',
    contact_phone_mix         varchar(90)      null comment '半隐藏联系人手机号',
    contact_phone_md5         varchar(90)      null comment '联系人手机号MD5',
    operator_remark           varchar(255)     null comment '操作员备注',
    brand_code                varchar(90)      null comment '品牌code',
    business_code             varchar(90)      not null comment '业务线编码',
    refund_status             int              not null comment '退款状态;退款状态  0：未退款 1：部分退款 2：全退款',
    order_close_reason        varchar(90)      null comment '订单关闭原因;订单退款状态',
    gift_address              tinyint          null comment '领取礼物地址：0否 1是',
    tenant_id                 int              not null comment '租户号',
    created_by                varchar(90)      not null comment '创建人',
    created_time              datetime         not null comment '创建时间',
    updated_by                varchar(90)      null comment '更新人',
    updated_time              datetime         null comment '更新时间',
    is_deleted                int              not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                  int              not null comment '乐观锁',
    completed_time            datetime         null comment '订单完成时间',
    point_amount              int              null comment '积分',
    independent_status        int    default 0 null comment '分账状态 0-无需分账 1-待分账 2-分账中 3-已分账',
    logistics_status          int              null comment '90101：待支付  90301：订单完成  90401：订单关闭  90501：售后处理中，90201：待发货，90202：部分发货，90203：全部发货',
    coupon_status             int              null comment '90101：待支付 90401：订单关闭  90501：售后处理中，90201：待发放，90202：待核销，90301：已核销'
);

create table ecp_order.t_order_info_bak
(
    consumer_code             varchar(90)   not null comment '用户编码;用户编码',
    order_code                varchar(90)   not null comment '订单编码;订单编码，编码规则：业务线code+时间戳(日期+时分秒)+6位随机数',
    original_fee_total_amount int           not null comment '原始订单金额;原始订单金额，单位分',
    fee_total_amount          int           not null comment '应付金额;订单金额，单位分',
    cost_amount               int           null comment '实付金额;实付金额，单位分',
    discount_total_amount     int           not null comment '折扣金额;折扣金额，单位分',
    exclude_tax_amount        int           null comment '不含税金额',
    tax_amount                int           null comment '订单税费',
    freight_amount            int           not null comment '运费金额;运费金额，虚拟商品为0',
    parent_order_code         varchar(90)   not null comment '父订单号;父订单号，用于标记多个子订单从属于哪个订单，当只有一个订单时为本单号',
    order_status              int           not null comment '订单状态;订单状态；
1：已下单
2：已支付
3：订单完成
4：订单关闭
5：售后处理中
6：订单部分取消
7：订单整单取消  ',
    payment_status            int           not null comment '支付状态;支付状态；0:未支付 1：已支付',
    payment_time              datetime      null comment '支付时间;支付时间',
    order_time                datetime      not null comment '订单提交时间;订单提交时间',
    order_channel             int           not null comment '下单渠道
1 路虎小程序
2捷豹小程序
3官网 （暂未使用）
4app  （暂未使用）
5代客下单 ',
    order_type                int           not null comment '履约方式：
1：PIVI-Remote
2：PIVI-Online Pack
3：实物商品子订单
4：组合商品
5. 电子兑换券',
    wx_nick_name              varchar(100)  null comment '微信昵称',
    wx_phone                  varchar(1000) null comment '微信授权手机',
    wx_phone_mix              varchar(90)   null comment '半隐藏微信手机号',
    wx_phone_md5              varchar(90)   null comment '微信手机号MD5',
    customer_remark           varchar(255)  null comment '客户留言信息;客户留言信息',
    contact_phone             varchar(1000) null comment '客户联系手机;客户联系手机',
    contact_phone_mix         varchar(90)   null comment '半隐藏联系人手机号',
    contact_phone_md5         varchar(90)   null comment '联系人手机号MD5',
    operator_remark           varchar(255)  null comment '操作员备注',
    brand_code                varchar(90)   null comment '品牌code',
    business_code             varchar(90)   not null comment '业务线编码',
    refund_status             int           not null comment '退款状态;退款状态  0：未退款 1：部分退款 2：全退款',
    order_close_reason        varchar(90)   null comment '订单关闭原因;订单退款状态',
    gift_address              tinyint       null comment '领取礼物地址：0否 1是',
    tenant_id                 int           not null comment '租户号',
    created_by                varchar(90)   not null comment '创建人',
    created_time              datetime      not null comment '创建时间',
    updated_by                varchar(90)   null comment '更新人',
    updated_time              datetime      null comment '更新时间',
    is_deleted                int           not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                  int           not null comment '乐观锁',
    completed_time            datetime      null comment '订单完成时间',
    point_amount              int           null comment '积分',
    independent_status        int default 0 null comment '分账状态 0-无需分账 1-待分账 2-分账中 3-已分账',
    closed_time               datetime      null comment '订单关闭时间',
    logistics_status          int           null comment '90101：待支付  90301：订单完成  90401：订单关闭  90501：售后处理中，90201：待发货，90202：部分发货，90203：全部发货',
    coupon_status             int           null comment '90101：待支付 90401：订单关闭  90501：售后处理中，90201：待发放，90202：待核销，90301：已核销'
)
    comment 't_order_info_bak';

create table ecp_order.t_order_item
(
    id                       bigint auto_increment comment '主键'
        primary key,
    order_item_code          varchar(90)    not null comment '订单item编码;订单item编码，雪花算法ID',
    order_code               varchar(90)    not null comment '订单编码;订单编码',
    product_version_code     varchar(90)    not null comment '商品快照编码;商品快照编码',
    product_code             varchar(90)    not null comment '商品编码;商品编码',
    product_sku_code         varchar(90)    not null comment '商品SKU编码;商品SKU编码',
    product_name             varchar(255)   not null comment '商品名称;商品名称',
    product_image_url        varchar(255)   not null comment '商品主图URL;商品主图URL',
    product_attribute        varchar(1000)  null comment 'stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI',
    product_market_price     int            null comment '商品市场价格',
    product_sale_price       int            not null comment '商品销售单价;商品销售单价，单位分',
    product_quantity         int            not null comment '商品数量;商品数量',
    total_amount             int            not null comment '应付总金额;应付总金额，单位分',
    cost_amount              int            not null comment '实付金额;实付金额，单位分',
    discount_fee_amount      int            not null comment '折扣金额;折扣金额，单位分',
    exclude_tax_total_amount int            null comment '不含税总金额',
    tax_amount               int            null comment '税费总金额',
    order_item_spu_type      tinyint        not null comment '订单item商品的类型，1普通商品 2组合商品',
    remark                   varchar(500)   null comment '备注信息',
    tenant_id                int            not null comment '租户号',
    created_by               varchar(90)    not null comment '创建人',
    created_time             datetime       not null comment '创建时间',
    updated_by               varchar(90)    null comment '更新人',
    updated_time             datetime       null comment '更新时间',
    is_deleted               int            not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                 int            not null comment '乐观锁',
    aftersales_status        int            null comment '售后处理状态;售后处理中 1：售后处理中 2：售后已完成 3：售后关闭',
    merchant_account_no      varchar(50)    null comment '商户号',
    point_amount             int            null comment '积分金额',
    item_status              int            null comment 'item状态;VCS: 1:待激活  2:激活中  3：已激活 4：激活失败
物流履约订单状态：1：待发货，2：已发货，3：已妥投，4: 已收货  5:已关闭
优惠券履约订单状态：1：待发放，2：待核销  3：已核销  4：已回收',
    coupon_model_code        varchar(255)   null comment '卡券模版 CODE',
    item_fufilement_type     int            null comment 'item履约类型;1：远程车控REMOTE SERVICE； 2：PIVI Subscription   3: 实物商品；4：组合商品 5：优惠券商品',
    tax_rate                 decimal(12, 2) null comment '税率值;税率，精度为小数点后两位',
    tax_code                 varchar(100)   null comment '税务编码',
    kingdee_sku_code         varchar(255)   null comment '金蝶sku编码',
    constraint t_order_item_order_item_code_IDX
        unique (order_item_code)
)
    comment 't_order_item' row_format = DYNAMIC;

create index t_order_item_order_code_IDX
    on ecp_order.t_order_item (order_code, product_sku_code);

create index t_order_item_product_code_IDX
    on ecp_order.t_order_item (product_code);

create table ecp_order.t_order_item_backup_20250319
(
    id                       bigint default 0 not null comment '主键',
    order_item_code          varchar(90)      not null comment '订单item编码;订单item编码，雪花算法ID',
    order_code               varchar(90)      not null comment '订单编码;订单编码',
    product_version_code     varchar(90)      not null comment '商品快照编码;商品快照编码',
    product_code             varchar(90)      not null comment '商品编码;商品编码',
    product_sku_code         varchar(90)      not null comment '商品SKU编码;商品SKU编码',
    product_name             varchar(255)     not null comment '商品名称;商品名称',
    product_image_url        varchar(255)     not null comment '商品主图URL;商品主图URL',
    product_attribute        varchar(1000)    null comment 'stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI',
    product_market_price     int              null comment '商品市场价格',
    product_sale_price       int              not null comment '商品销售单价;商品销售单价，单位分',
    product_quantity         int              not null comment '商品数量;商品数量',
    total_amount             int              not null comment '应付总金额;应付总金额，单位分',
    cost_amount              int              not null comment '实付金额;实付金额，单位分',
    discount_fee_amount      int              not null comment '折扣金额;折扣金额，单位分',
    exclude_tax_total_amount int              null comment '不含税总金额',
    tax_amount               int              null comment '税费总金额',
    order_item_spu_type      tinyint          not null comment '订单item商品的类型，1普通商品 2组合商品',
    remark                   varchar(500)     null comment '备注信息',
    tenant_id                int              not null comment '租户号',
    created_by               varchar(90)      not null comment '创建人',
    created_time             datetime         not null comment '创建时间',
    updated_by               varchar(90)      null comment '更新人',
    updated_time             datetime         null comment '更新时间',
    is_deleted               int              not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                 int              not null comment '乐观锁',
    aftersales_status        int              null comment '售后处理状态;售后处理中 1：售后处理中 2：售后已完成 3：售后关闭',
    merchant_account_no      varchar(50)      null comment '商户号',
    point_amount             int              null comment '积分金额',
    item_status              int              null comment 'item状态;VCS: 1:待激活  2:激活中  3：已激活 4：激活失败
物流履约订单状态：1：待发货，2：已发货，3：已妥投，4: 已收货
优惠券履约订单状态：1：待发放，2：待核销  3：已核销  4：已回收',
    coupon_model_code        varchar(255)     null comment '卡券模版 CODE',
    item_fufilement_type     int              null comment 'item履约类型;1：远程车控REMOTE SERVICE； 2：PIVI Subscription   3: 实物商品；4：组合商品 5：优惠券商品',
    tax_rate                 decimal(12, 2)   null comment '税率值;税率，精度为小数点后两位',
    tax_code                 varchar(100)     null comment '税务编码'
);

create table ecp_order.t_order_item_logistics
(
    id                     bigint auto_increment comment '主键'
        primary key,
    order_code             varchar(90)   not null comment '订单编码',
    order_item_code        varchar(90)   not null comment '订单明细编码',
    logistics_status       int           null comment '快递状态：1-待发货，2-已发货，3-已签收(已妥投)，4-确认收货',
    send_time              datetime      null comment '发货时间',
    logistics_company      varchar(90)   null comment '物流公司名称;物流公司',
    logistics_no           varchar(90)   null comment '物流单号;物流单号',
    sign_time              datetime      null comment '签收时间',
    confirm_package_time   datetime      null comment '用户确认收货的时间',
    recipient              varchar(90)   not null comment '收件人;收件人',
    recipient_phone        varchar(1000) null comment '收件人联系手机;',
    recipient_phone_mix    varchar(90)   null comment '半隐藏收件人联系手机',
    recipient_phone_md5    varchar(90)   null comment '收件人联系手机MD5',
    province_code          varchar(90)   not null comment '收件省;收件省',
    city_code              varchar(90)   not null comment '收件市;收件市',
    area_code              varchar(90)   not null comment '收件区;收件区',
    detail_address         varchar(255)  not null comment '收件详细地址;收件详细地址',
    post_code              varchar(90)   null comment '邮编地址;邮编地址',
    no_reason_returns_time datetime      null comment 'N天无理由退货截至时间',
    tenant_id              int           not null comment '租户号',
    created_by             varchar(90)   not null comment '创建人',
    created_time           datetime      not null comment '创建时间',
    updated_by             varchar(90)   null comment '更新人',
    updated_time           datetime      null comment '更新时间',
    is_deleted             int           not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int           not null comment '乐观锁',
    recipient_md5          varchar(90)   null comment '收件人MD5',
    recipient_mix          varchar(90)   null comment '收件人半隐藏',
    detail_address_md5     varchar(90)   null comment '收件详细地址MD5',
    detail_address_mix     varchar(90)   null comment '收件详细地址半隐藏',
    full_detail_address    varchar(255)  null comment '收件地址完整信息',
    is_sign_task           int default 0 not null comment '签收任务是否执行'
)
    comment '履约方式为实物时，记录物流信息';

create index t_order_item_logistics_order_code_IDX
    on ecp_order.t_order_item_logistics (order_code, is_deleted);

create table ecp_order.t_order_modify_detail_log
(
    id                     bigint auto_increment comment '主键'
        primary key,
    order_code             varchar(90) null comment '订单号;订单号',
    modify_module          varchar(90) not null comment '修改模块;修改模块',
    modify_field_count     int         null comment '修改字段数量;修改字段数量',
    modify_field_old_value mediumtext  null comment '修改前字段值;涉及修改字段旧值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleOld","rule_desc":"descOld"}',
    modify_field_new_value mediumtext  null comment '修改后字段值;涉及修改字段新值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleNew","rule_desc":"descNew"}',
    operate_time           datetime    null comment '修改时间;修改时间',
    operate_user           varchar(90) null comment '修改人账号;修改人账号',
    tenant_id              int         not null comment '租户号',
    created_by             varchar(90) not null comment '创建人',
    created_time           datetime    not null comment '创建时间',
    updated_by             varchar(90) null comment '更新人',
    updated_time           datetime    null comment '更新时间',
    is_deleted             int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int         not null comment '乐观锁'
)
    comment 't_order_modify_detail_log' row_format = DYNAMIC;

create index t_order_modify_detail_log_order_code_IDX
    on ecp_order.t_order_modify_detail_log (order_code);

create table ecp_order.t_order_modify_log
(
    id              bigint auto_increment comment '主键'
        primary key,
    order_code      varchar(90)  not null comment '订单编码;订单编码',
    operate_time    datetime     not null comment '操作时间;操作时间',
    operate_content varchar(255) not null comment '操作内容;操作内容',
    operate_user    varchar(90)  not null comment '操作人;操作人',
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁'
)
    comment 't_order_modify_log' row_format = DYNAMIC;

create table ecp_notification.t_order_notification_detail
(
    id                bigint auto_increment comment '主键'
        primary key,
    msgid             varchar(255) null comment '信息发送UUID;信息发送UUID',
    send_message      varchar(900) not null comment '发送内容;发送内容',
    send_phone        varchar(90)  not null comment '发送手机号;发送手机号',
    car_vin           varchar(90)  null comment '车辆VIN;车辆VIN',
    send_time         datetime     null comment '发送时间;发送时间',
    send_result       int          not null comment '发送结果;发送结果 0失败 1成功 2待发送',
    error_message     varchar(255) null comment '发送失败原因;发送失败原因',
    brand_code        varchar(90)  null comment '品牌code;品牌code，该条短信发送给捷豹或路虎，用于签名和短链的生成',
    msg_result        varchar(255) null comment '最终短信触达结果;最终短信触达结果，0：成功 1：接口处理失败 2：运营商网关失败',
    submit_error_code varchar(255) null comment '提交错误编码，ECP到德成',
    msg_error_desc    varchar(255) null comment '短信触达错误描述;短信触达错误描述',
    wgcode            varchar(255) null comment '短信触达运营商错误码;短信触达运营商错误码',
    short_link        varchar(255) null comment '短鏈URL;短鏈URL',
    open_short_link   int          null comment '是否打开短链;是否打开短链，0否 1是',
    tenant_id         int          not null comment '租户号',
    created_by        varchar(90)  not null comment '创建人',
    created_time      datetime     not null comment '创建时间',
    updated_by        varchar(90)  null comment '更新人',
    updated_time      datetime     null comment '更新时间',
    is_deleted        int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int          not null comment '乐观锁'
)
    comment 't_order_notification_detail';

create index idx_car_vin
    on ecp_notification.t_order_notification_detail (car_vin);

create index idx_send_phone
    on ecp_notification.t_order_notification_detail (send_phone);

create table ecp_order.t_order_payment_records
(
    id              bigint auto_increment comment '主键'
        primary key,
    consumer_code   varchar(90) not null comment '发起用户JLRID;发起用户JLRID',
    order_code      varchar(90) not null comment '订单编号;订单编码',
    pay_apply_no    varchar(90) not null comment '交易流水号;支付中心交易流水号pay_apply_no',
    submit_time     datetime    not null comment '发起交易时间;发起交易时间',
    pay_status      int         not null comment '支付状态;支付状态 1待支付 2已支付',
    pay_finish_time datetime    null comment '支付完成时间;支付完成时间',
    tenant_id       int         not null comment '租户号',
    created_by      varchar(90) not null comment '创建人',
    created_time    datetime    not null comment '创建时间',
    updated_by      varchar(90) null comment '更新人',
    updated_time    datetime    null comment '更新时间',
    is_deleted      int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int         not null comment '乐观锁'
)
    comment 't_order_payment_records';

create index idx_topr_order_code
    on ecp_order.t_order_payment_records (order_code);

create table ecp_order.t_order_refund
(
    id                          bigint auto_increment comment '主键'
        primary key,
    refund_order_code           varchar(90)   not null comment '退单订单号;退单订单号,订单号规则：R+年月日+6位序列号',
    origin_order_code           varchar(90)   not null comment '原始订单号;原始订单号',
    refund_order_status         int           not null comment '退单状态;退单状态
1：发起整单退款申请
2：发起部分退款申请
3：同意整单退款申请
4：同意部分退款申请
5：订单整单退款完成
6：订单部分退款完成
7：拒绝整单退款申请
8：拒绝部分退款申请',
    submit_user                 varchar(90)   not null comment '提交人;提交人',
    refund_money                int           not null comment '是否退款;是否退款 0：否 1：是',
    refund_money_amount         int           not null comment '退款金额;退款金额',
    refund_remark               varchar(900)  null comment '退款备注信息;退款备注信息',
    reject_reason               varchar(255)  null comment '拒绝理由',
    service_end_date            datetime      null comment '指定服务过期时间',
    tenant_id                   int           not null comment '租户号',
    created_by                  varchar(90)   not null comment '创建人',
    created_time                datetime      not null comment '创建时间',
    updated_by                  varchar(90)   null comment '更新人',
    updated_time                datetime      null comment '更新时间',
    is_deleted                  int           not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                    int           not null comment '乐观锁',
    refund_reason               int           null comment '退款原因-
七天无理由退换货 1
不喜欢/不想要 2
拍错/多拍 3
电话信息填写错误 4
没用/少用 5
计划有变没时间消费 6
担心安全问题(天气等) 7
预约不上/排队太久 8
联系不上商家 9
其他 0',
    origin_order_type           int           null comment '履约方式：
1：PIVI-Remote
2：PIVI-Online Pack
3：实物商品子订单
4：组合商品
5.   电子兑换券',
    refund_source               int           null comment '1:  用户发起
2：系统自动
3：运营发起',
    refund_order_type           int           null comment '售后类型
1: 退货退款
2: 仅退款',
    refund_order_fufilment_type int           null comment '退单履约类型;1：远程车控REMOTE SERVICE； 2：PIVI Subscription   3: 实物商品；4：组合商品 5：优惠券商品',
    logistics_refund_status     int           null comment '物流退单状态;
90101 待退货审核
90102 待商品寄回
90103 待退款审核
90301 分账退款中
90302 退款处理中
90501 售后完成，已退款
90701 售后关闭，拒绝退款申请
90702 售后关闭，买家已撤销申请
90703 售后关闭，拒绝退货申请',
    coupon_refund_status        int           null comment '优惠券退单状态;
90101       发起退款申请
90701       拒绝退款申请
90301       退款处理中
90501       退款完成售后关闭
90702       买家已撤销退款申请',
    sup_desc                    varchar(200)  null comment '用户补充描述',
    refund_coupon_code          varchar(100)  null comment '满减优惠券code',
    refund_status_sup           int           null comment '售后状态(补充说明)  1-券码逾期 2-券码逾期(部分) 3-主动退款 4-主动退款(部分)',
    refund_freight              int           null comment '退单运费',
    logistics_code              varchar(100)  null comment '物流单号',
    logistics_company_code      varchar(100)  null comment '物流公司code',
    logistics_company_name      varchar(100)  null comment '物流公司名称',
    return_audit_remark         varchar(200)  null comment '退货审核备注',
    refund_audit_remark         varchar(200)  null comment '退款审核备注',
    logistics_attachment        varchar(1500) null comment '凭证图片,多张图片用英文,分隔开',
    refund_independent_type     int default 0 null comment '是否进行了分账退款  0-否 1-是 默认0',
    constraint t_order_refund_refund_order_code_IDX
        unique (refund_order_code)
)
    comment 't_order_refund' row_format = DYNAMIC;

create index t_order_refund_created_time_IDX
    on ecp_order.t_order_refund (created_time);

create index t_order_refund_origin_order_code_IDX
    on ecp_order.t_order_refund (origin_order_code);

create table ecp_order.t_order_refund_attachment
(
    id                bigint auto_increment comment '主键'
        primary key,
    refund_order_code varchar(90)  not null comment '退款订单号;退款订单号',
    attachment_url    varchar(255) not null comment '退款附件链接URL;退款附件链接URL',
    tenant_id         int          not null comment '租户号',
    created_by        varchar(90)  not null comment '创建人',
    created_time      datetime     not null comment '创建时间',
    updated_by        varchar(90)  null comment '更新人',
    updated_time      datetime     null comment '更新时间',
    is_deleted        int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int          not null comment '乐观锁'
)
    comment 't_order_refund_attachment' row_format = DYNAMIC;

create index t_order_refund_attachment_refund_order_code_IDX
    on ecp_order.t_order_refund_attachment (refund_order_code);

create table ecp_order.t_order_refund_backup_20250319
(
    id                          bigint default 0 not null comment '主键',
    refund_order_code           varchar(90)      not null comment '退单订单号;退单订单号,订单号规则：R+年月日+6位序列号',
    origin_order_code           varchar(90)      not null comment '原始订单号;原始订单号',
    refund_order_status         int              not null comment '退单状态;退单状态
1：发起整单退款申请
2：发起部分退款申请
3：同意整单退款申请
4：同意部分退款申请
5：订单整单退款完成
6：订单部分退款完成
7：拒绝整单退款申请
8：拒绝部分退款申请',
    submit_user                 varchar(90)      not null comment '提交人;提交人',
    refund_money                int              not null comment '是否退款;是否退款 0：否 1：是',
    refund_money_amount         int              not null comment '退款金额;退款金额',
    refund_remark               varchar(900)     null comment '退款备注信息;退款备注信息',
    reject_reason               varchar(255)     null comment '拒绝理由',
    service_end_date            datetime         null comment '指定服务过期时间',
    tenant_id                   int              not null comment '租户号',
    created_by                  varchar(90)      not null comment '创建人',
    created_time                datetime         not null comment '创建时间',
    updated_by                  varchar(90)      null comment '更新人',
    updated_time                datetime         null comment '更新时间',
    is_deleted                  int              not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                    int              not null comment '乐观锁',
    refund_reason               int              null comment '退款原因-
七天无理由退换货 1
不喜欢/不想要 2
拍错/多拍 3
电话信息填写错误 4
没用/少用 5
计划有变没时间消费 6
担心安全问题(天气等) 7
预约不上/排队太久 8
联系不上商家 9
其他 0',
    origin_order_type           int              null comment '履约方式：
1：PIVI-Remote
2：PIVI-Online Pack
3：实物商品子订单
4：组合商品
5.   电子兑换券',
    refund_source               int              null comment '1:  用户发起
2：系统自动
3：运营发起',
    refund_order_type           int              null comment '售后类型
1: 退货退款
2: 仅退款',
    refund_order_fufilment_type int              null comment '退单履约类型;1：远程车控REMOTE SERVICE； 2：PIVI Subscription   3: 实物商品；4：组合商品 5：优惠券商品',
    logistics_refund_status     int              null comment '物流退单状态;
90101
待退货审核
90102
待商品寄回
90103
待退款审核
90301
分账退款中
90302
退款处理中
90501
售后完成，已退款
90701
售后关闭，拒绝退款申请
90702
售后关闭，买家已撤销申请',
    coupon_refund_status        int              null comment '优惠券退单状态;
90101       发起退款申请
90701       拒绝退款申请
90301       退款处理中
90501       退款完成售后关闭
90702       买家已撤销退款申请',
    sup_desc                    varchar(200)     null comment '用户补充描述',
    refund_coupon_code          varchar(100)     null comment '满减优惠券code'
);

create table ecp_order.t_order_refund_item
(
    id                     bigint auto_increment comment '主键'
        primary key,
    refund_order_code      varchar(90)   not null comment '退单订单号;退单订单号',
    order_item_code        varchar(255)  not null comment '订单item号;订单item号',
    order_refund_item_code varchar(255)  null comment '订单item退款号;订单item退款号
用于取消卡券的时候传入进行安全验证
生成规则为R+order_item_code',
    service_end_date       datetime      null comment '指定服务过期时间',
    tenant_id              int           not null comment '租户号',
    created_by             varchar(90)   not null comment '创建人',
    created_time           datetime      not null comment '创建时间',
    updated_by             varchar(90)   null comment '更新人',
    updated_time           datetime      null comment '更新时间',
    is_deleted             int           not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int           not null comment '乐观锁',
    refund_money           int           null comment '退单实付金额',
    refund_point           int           null comment '退单实付积分',
    refund_quantity        int default 1 null comment '退单数量',
    refund_freight         int           null comment '退单运费'
)
    comment 't_order_refund_item' row_format = DYNAMIC;

create index t_order_refund_item_order_item_code_IDX
    on ecp_order.t_order_refund_item (order_item_code, is_deleted);

create table ecp_order.t_order_refund_item_backup_20250319
(
    id                     bigint default 0 not null comment '主键',
    refund_order_code      varchar(90)      not null comment '退单订单号;退单订单号',
    order_item_code        varchar(255)     not null comment '订单item号;订单item号',
    order_refund_item_code varchar(255)     null comment '订单item退款号;订单item退款号
用于取消卡券的时候传入进行安全验证
生成规则为R+order_item_code',
    service_end_date       datetime         null comment '指定服务过期时间',
    tenant_id              int              not null comment '租户号',
    created_by             varchar(90)      not null comment '创建人',
    created_time           datetime         not null comment '创建时间',
    updated_by             varchar(90)      null comment '更新人',
    updated_time           datetime         null comment '更新时间',
    is_deleted             int              not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int              not null comment '乐观锁',
    refund_money           int              null comment '退单实付金额',
    refund_point           int              null comment '退单实付积分',
    refund_quantity        int    default 1 null comment '退单数量'
);

create table ecp_order.t_order_refund_payment_records
(
    id                bigint auto_increment comment '主键'
        primary key,
    order_code        varchar(90) not null comment '原单号;原单号',
    refund_order_code varchar(90) not null comment '退单号;退单号',
    pay_apply_no      varchar(90) not null comment '交易单号;支付中心交易单号',
    refund_apply_no   varchar(90) not null comment '退款申请单号;支付中心退款申请单号',
    trade_status      varchar(90) not null comment '退单状态;交易处理中： PENDING
交易成功 ：SUCCESS
交易失败 ：FAIL',
    tenant_id         int         null comment '租户号',
    created_by        varchar(90) not null comment '创建人',
    created_time      datetime    not null comment '创建时间',
    updated_by        varchar(90) null comment '更新人',
    updated_time      datetime    null comment '更新时间',
    is_deleted        int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int         not null comment '乐观锁'
)
    comment 't_order_refund_payment_records';

create table ecp_order.t_order_statistic
(
    id                      bigint auto_increment comment '主键'
        primary key,
    consumer_code           varchar(90) not null comment '用户编码;用户编码',
    order_total_count       int         not null comment '订单总数量;订单总数量',
    vcs_order_total_count   int         not null comment 'vcs订单总数量;vcs订单总数量',
    brand_goods_total_count int         not null comment 'goods订单总数量;goods订单总数量',
    tenant_id               int         not null comment '租户号',
    created_by              varchar(90) not null comment '创建人',
    created_time            datetime    not null comment '创建时间',
    updated_by              varchar(90) null comment '更新人',
    updated_time            datetime    null comment '更新时间',
    is_deleted              int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                int         not null comment '乐观锁',
    constraint t_order_statistic_consumer_code_IDX
        unique (consumer_code)
)
    comment 't_order_statistic' row_format = DYNAMIC;

create table ecp_order.t_order_status_log
(
    id            bigint auto_increment comment '主键'
        primary key,
    tenant_id     int         not null comment '租户号',
    created_by    varchar(90) not null comment '创建人',
    created_time  datetime    not null comment '创建时间',
    updated_by    varchar(90) null comment '更新人',
    updated_time  datetime    null comment '更新时间',
    is_deleted    int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int         not null comment '乐观锁',
    order_code    varchar(90) not null comment '订单号;订单号',
    before_status int         not null comment '当前状态;变更前状态',
    after_status  int         not null comment '变更后状态;变更后状态',
    change_time   datetime    not null comment '变更时间;变更时间'
)
    comment 't_order_status_log' row_format = DYNAMIC;

create index t_order_status_log_order_code_IDX
    on ecp_order.t_order_status_log (order_code);

create table ecp_order.t_order_status_mapping
(
    id                                        bigint auto_increment comment '主键'
        primary key,
    order_status                              int          not null comment '订单状态;订单状态',
    refund_order_status                       int          not null comment '退单状态;退单状态',
    customer_order_status_view                varchar(90)  not null comment '小程序订单状态;小程序订单状态',
    customer_after_sales_order_status_view    varchar(90)  not null comment '小程序售后状态;小程序售后状态',
    operation_origin_order_status_view        varchar(90)  not null comment '原子订单运营状态;原子订单运营状态',
    operation_origin_order_cancel_status_view varchar(90)  not null comment '原单服务取消状态;原单服务取消状态',
    customer_refund_order_status_view         varchar(100) not null comment '小程序退单状态',
    tenant_id                                 int          not null comment '租户号',
    created_by                                varchar(90)  not null comment '创建人',
    created_time                              datetime     not null comment '创建时间',
    updated_by                                varchar(90)  null comment '更新人',
    updated_time                              datetime     null comment '更新时间',
    is_deleted                                int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                                  int          not null comment '乐观锁'
)
    comment 't_order_status_mapping' row_format = DYNAMIC;

create table ecp_order.t_order_terms
(
    id              bigint auto_increment comment '主键'
        primary key,
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁',
    order_code      varchar(90)  not null comment '订单号;订单编号',
    order_item_code varchar(100) null comment '订单明细编号',
    terms_code      varchar(90)  null comment '服务条款编码;服务条款编码，来源于VCS policy',
    sign_time       datetime     null comment '签署时间;签署时间',
    consumer_code   varchar(90)  null comment '用户编码;用户编码',
    constraint t_order_terms_terms_code_IDX
        unique (terms_code, order_item_code)
)
    comment 't_order_terms' row_format = DYNAMIC;

create index t_order_terms_order_code_IDX
    on ecp_order.t_order_terms (order_code);

create table ecp_payment.t_pay_nofity_log
(
    id            bigint auto_increment comment '主键'
        primary key,
    task_id       varchar(255) not null comment '任务id;t_pay_nofity_task的id',
    notify_times  int          not null comment '第几次被通知;第几次被通知',
    response_text varchar(255) not null comment '响应结果;响应结果',
    notify_status int          not null comment '通知状态;通知状态 0：等待通知 10：通知成功 20：通知失败 21：请求成功，但结果失败 22：请求失败',
    tenant_id     int          not null comment '租户号',
    created_by    varchar(90)  not null comment '创建人',
    created_time  datetime     not null comment '创建时间',
    updated_by    varchar(90)  null comment '更新人',
    updated_time  datetime     null comment '更新时间',
    is_deleted    int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int          not null comment '乐观锁'
)
    comment 't_pay_nofity_log' charset = utf8mb3
                               row_format = DYNAMIC;

create index idx_task_id
    on ecp_payment.t_pay_nofity_log (task_id);

create table ecp_payment.t_pay_nofity_task
(
    id                bigint auto_increment comment '主键'
        primary key,
    app_no            varchar(90)  not null comment '应用编号;应用编号',
    notify_type       int          not null comment '通知类型;通知类型 1支付单  2退款单',
    data_order_code   varchar(90)  not null comment '交易订单编码;订单编码，根据不同 type 进行关联',
    merchant_order_no varchar(90)  not null comment '商户订单号;商户订单号',
    status            int          not null comment '通知状态;通知状态
0：等待通知
10：通知成功
20：通知失败
21：请求成功，但结果失败
22：请求失败',
    next_notify_time  datetime     not null comment '下一次通知时间;下一次通知时间',
    last_execute_time datetime     null comment '最后一次执行时间;最后一次执行时间',
    notify_times      int          not null comment '当前通知次数;当前通知次数',
    max_notify_times  int          null comment '最大通知次数;最大通知次数',
    notify_url        varchar(255) not null comment '通知地址;通知地址',
    tenant_id         int          not null comment '租户号',
    created_by        varchar(90)  not null comment '创建人',
    created_time      datetime     not null comment '创建时间',
    updated_by        varchar(90)  null comment '更新人',
    updated_time      datetime     null comment '更新时间',
    is_deleted        int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int          not null comment '乐观锁'
)
    comment 't_pay_nofity_task' charset = utf8mb3
                                row_format = DYNAMIC;

create index idx_next_notify_time
    on ecp_payment.t_pay_nofity_task (next_notify_time);

create table ecp_payment.t_payment_broker
(
    id           bigint auto_increment comment '主键'
        primary key,
    broker_code  varchar(90)  not null comment '渠道商编码,例如：CUSC-JAGUAR，CUSC-LANDROVER',
    broker_name  varchar(90)  not null comment '渠道商名称;渠道商名称',
    app_id       varchar(90)  null comment '应用ID;应用ID',
    payment_url  varchar(255) null comment '支付URL;支付URL',
    refund_url   varchar(255) null comment '退款URL;退款URL',
    invoice_url  varchar(255) null comment '发票URL;发票URL',
    status       int          not null comment '渠道状态;状态，0关闭 1启用',
    tenant_id    int          not null comment '租户号',
    created_by   varchar(90)  not null comment '创建人',
    created_time datetime     not null comment '创建时间',
    updated_by   varchar(90)  null comment '更新人',
    updated_time datetime     null comment '更新时间',
    is_deleted   int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int          not null comment '乐观锁'
)
    comment 't_payment_broker' charset = utf8mb3
                               row_format = DYNAMIC;

create table ecp_payment.t_payment_order
(
    id                 bigint auto_increment comment '主键'
        primary key,
    payment_order_code varchar(90)                             not null comment '支付单号;支付单号,雪花算法ID',
    app_no             varchar(90)                             not null comment '应用编码;应用编码，来自于哪个业务应用',
    order_code         varchar(90)                             not null comment '订单号;业务订单号',
    openid             varchar(255)                            not null comment '用户openid',
    trade_no           varchar(90)                             not null comment '交易流水号;交易流水号，支付方生成',
    pay_amount         int                                     not null comment '支付金额;支付金额单位分',
    pay_type           varchar(90)                             not null comment '支付方式;ALIPAY("支付宝支付"), WXPAY("微信支付"), CHINAPAY("银联支付");',
    channel_code       varchar(90)                             not null comment '支付渠道;支付渠道，UNICOM-JAGUAR：联通智网jaguar',
    pay_params         varchar(900) collate utf8mb4_general_ci null comment '支付参数;支付参数，针对不同支付商户有不同的独有参数',
    pay_status         int                                     not null comment '支付状态;支付状态 0未支付 1支付中 2已支付 3交易关闭',
    submit_time        datetime                                not null comment '支付提交时间;支付提交时间',
    pay_finish_time    datetime                                null comment '支付完成时间;支付完成时间',
    notify_url         varchar(255)                            not null comment '回调URL地址;回调URL地址',
    tenant_id          int                                     not null comment '租户号',
    created_by         varchar(90)                             not null comment '创建人',
    created_time       datetime                                not null comment '创建时间',
    updated_by         varchar(90)                             null comment '更新人',
    updated_time       datetime                                null comment '更新时间',
    is_deleted         int                                     not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int                                     not null comment '乐观锁'
)
    comment 't_payment_order' charset = utf8mb3
                              row_format = DYNAMIC;

create index idx_order_code
    on ecp_payment.t_payment_order (order_code);

create index idx_trade_no
    on ecp_payment.t_payment_order (trade_no);

create table ecp_payment.t_payment_order_bak
(
    id              bigint auto_increment comment '主键'
        primary key,
    pay_apply_no    varchar(90)            not null comment '支付申请单号',
    app_no          varchar(90)            not null comment '应用编码;应用编码，来自于哪个业务应用',
    jlrid           varchar(255)           not null comment '用户JLRID;JLR全局用户ID',
    order_no        varchar(90)            not null comment '订单号;业务订单号',
    trade_no        varchar(90)            not null comment '交易流水号;交易流水号，支付方生成',
    pay_amount      int                    not null comment '支付金额;支付金额单位分',
    pay_type        varchar(90)            not null comment '支付类型;CASH：现金支付 COMBINE：组合支付 POINTS：积分抵扣;支付类型，CASH：现金支付 COMBINE：组合支付 POINTS：积分抵扣',
    channel_code    varchar(90)            not null comment '支付渠道;CUSC-JAGUAR，CUSC-LANDROVER;支付渠道，CUSC-JAGUAR，CUSC-LANDROVER',
    pay_method      varchar(90)            not null comment '支付方式;ALIPAY("支付宝支付"), WXPAY("微信支付"), CHINAPAY("银联支付");',
    pay_status      varchar(90)            not null comment '支付状态;初始化:INIT, 交易处理中:PENDING, 交易成功:SUCCESS, 交易失败:FAIL, 交易关闭:CLOSED',
    submit_time     datetime               not null comment '支付提交时间;支付提交时间',
    open_id         varchar(255)           null comment '三方ID;用户openid（微信、支付宝、京东天猫三方ID）',
    pay_params      json                   null comment '支付参数;支付参数，针对不同支付商户有不同的独有参数',
    pay_finish_time datetime               null comment '支付完成时间;支付完成时间',
    device_info     json                   null comment '设备信息',
    order_title     varchar(90) default '' not null comment '订单标题',
    product_no      varchar(90) default '' not null comment '订单编码',
    remark          varchar(255)           null comment '备注;支付场景描述',
    notify_url      varchar(255)           not null comment '回调地址URL;回调地址URL',
    error_msg       varchar(255)           null comment '错误信息',
    is_refund       int         default 0  null comment '是否退款；0-否，1-是',
    created_by      varchar(20)            not null comment '创建人',
    created_time    datetime               not null comment '创建时间',
    updated_by      varchar(20)            null comment '更新人',
    updated_time    datetime               null comment '更新时间',
    is_deleted      int                    not null comment '是否删除;逻辑删除字段，0：否 1：是',
    tenant_id       int         default 0  not null comment '租户号',
    revision        int                    not null comment '乐观锁'
)
    comment 't_payment_order_bak';

create table ecp_payment.t_payment_request_order
(
    id              bigint auto_increment comment '主键'
        primary key,
    jlr_id          varchar(90) not null comment '用户JLRID;jlr_id',
    order_code      varchar(90) not null comment '订单号;订单号',
    pay_apply_no    varchar(90) not null comment '支付交易单号;支付交易单号，来源于payment center',
    pay_status      int         not null comment '支付状态;支付状态 0未支付 1支付中 2已支付 3交易关闭',
    submit_time     datetime    not null comment '支付提交时间;支付提交时间',
    pay_finish_time datetime    null comment '支付完成时间;支付完成时间',
    tenant_id       int         not null comment '租户号',
    created_by      varchar(90) not null comment '创建人',
    created_time    datetime    not null comment '创建时间',
    updated_by      varchar(90) null comment '更新人',
    updated_time    datetime    null comment '更新时间',
    is_deleted      int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int         not null comment '乐观锁'
)
    comment 't_payment_request_order' charset = utf8mb3;

create table ecp_product.t_pivi_model_mapping
(
    id               bigint auto_increment comment '主键'
        primary key,
    car_system_model varchar(90)  not null comment '车机类型;车机类型，PIVI',
    car_model        varchar(90)  not null comment '车型;车型',
    car_model_year   varchar(255) not null comment '年款集合;车型年款，多个车型用,隔开',
    business_code    varchar(90)  not null comment '业务线编码;业务线CODE',
    tenant_id        int          null comment '租户号',
    created_by       varchar(90)  not null comment '创建人',
    created_time     datetime     not null comment '创建时间',
    updated_by       varchar(90)  null comment '更新人',
    updated_time     datetime     null comment '更新时间',
    is_deleted       int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int          null comment '乐观锁'
)
    comment 't_pivi_model_mapping' charset = utf8mb3
                                   row_format = DYNAMIC;

create table ecp_subscription.t_pivi_package
(
    id                   bigint auto_increment comment '主键'
        primary key,
    package_code         varchar(90)  not null comment '服务包编码;服务包编码',
    service_name         varchar(90)  not null comment '服务名;服务名',
    vehicle_feature_code varchar(90)  null comment '服务编码;服务特性编码',
    vehicle_feature_name varchar(90)  null comment '服务名;服务名',
    element_instance_id  varchar(90)  null comment '服务唯一实例ID;服务唯一实例ID',
    short_code           varchar(90)  null comment '短码;短码',
    element_name         varchar(90)  null comment '服务唯一实例名;服务唯一实例名',
    jlr_subscription_id  bigint       null comment '订阅ID',
    vin                  varchar(90)  null comment '车辆VIN;车辆VIN',
    ad_id                varchar(100) null comment 'appd_id',
    iccid                varchar(100) null comment '联通ICCID',
    dms_invoice_date     datetime     not null comment '开票时间',
    amap_expire_date     datetime     null comment '高德服务过期时间',
    expire_date          datetime     null comment '服务过期时间;服务过期时间',
    expire_date_utc0     datetime     null comment '过期时间UTC+0 , 如 2024/7/30 08:00:00',
    tenant_id            int          not null comment '租户号',
    created_by           varchar(90)  not null comment '创建人',
    created_time         datetime     not null comment '创建时间',
    updated_by           varchar(90)  null comment '更新人',
    updated_time         datetime     null comment '更新时间',
    is_deleted           int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision             int          not null comment '乐观锁',
    constraint t_pivi_package_vin_IDX
        unique (vin)
)
    comment 't_pivi_package' charset = utf8mb3;

create index idx_pivi_expire
    on ecp_subscription.t_pivi_package (expire_date);

create table ecp_subscription.t_pivi_package_log
(
    id                         bigint auto_increment comment '主键'
        primary key,
    modify_user                varchar(90) not null comment '修改人;修改人',
    modify_time                datetime    not null comment '修改时间;修改时间',
    car_vin                    varchar(90) not null comment '车架号;车架号',
    modify_invoice_date_before datetime    not null comment '修改前发票日期;修改前发票日期',
    modify_invoice_date_after  datetime    not null comment '修改后发票日期;修改后发票日期',
    tenant_id                  int         not null comment '租户号',
    created_by                 varchar(90) not null comment '创建人',
    created_time               datetime    not null comment '创建时间',
    updated_by                 varchar(90) null comment '更新人',
    updated_time               datetime    null comment '更新时间',
    is_deleted                 int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                   int         not null comment '乐观锁'
)
    comment 't_pivi_package_log';

create table ecp_product.t_product_attribute
(
    TENANT_ID            int         null comment '租户号',
    REVISION             int         null comment '乐观锁',
    CREATED_BY           varchar(90) null comment '创建人',
    CREATED_TIME         datetime    null comment '创建时间',
    UPDATED_BY           varchar(90) null comment '更新人',
    UPDATED_TIME         datetime    null comment '更新时间',
    IS_DELETED           int         not null comment '逻辑删除',
    id                   bigint auto_increment comment '主键'
        primary key,
    attribute_code       varchar(90) not null comment '属性值编码;属性值编码',
    product_code         varchar(90) not null comment '商品编码;商品编码',
    attribute_name_code  varchar(90) not null comment '商品属性名编码;商品属性名编码',
    attribute_value_code varchar(90) not null comment '商品属性值编码;商品属性值编码'
)
    comment 't_product_attribute' charset = utf8mb3
                                  row_format = DYNAMIC;

create index t_product_attribute_attribute_name_code_IDX
    on ecp_product.t_product_attribute (attribute_name_code);

create index t_product_attribute_product_code_IDX
    on ecp_product.t_product_attribute (product_code);

create table ecp_product.t_product_attribute_name
(
    TENANT_ID             int          null comment '租户号',
    REVISION              int          null comment '乐观锁',
    CREATED_BY            varchar(90)  null comment '创建人',
    CREATED_TIME          datetime     null comment '创建时间',
    UPDATED_BY            varchar(90)  null comment '更新人',
    UPDATED_TIME          datetime     null comment '更新时间',
    IS_DELETED            varchar(90)  not null comment '逻辑删除',
    id                    bigint auto_increment comment '主键'
        primary key,
    attribute_name_code   varchar(90)  not null comment '属性名code;属性名code',
    attribute_name        varchar(90)  not null comment '属性名;属性名名称，如颜色',
    attribute_name_desc   varchar(255) null comment '属性描述;描述信息',
    category_code_level_1 varchar(90)  null comment '一级分类code;一级分类编码',
    category_code_level_2 varchar(90)  null comment '二级分类code;二级分类编码',
    data_type             varchar(90)  not null comment '属性分类;属性分类，销售属性SALE或库存属性STOCK',
    sort                  int          null comment '排序;排序顺序，大的在前',
    status                int          not null comment '状态;启用状态 0：停用 1：启用',
    business_code         varchar(90)  not null comment '业务线编码',
    constraint t_product_attribute_name_attribute_name_code_IDX
        unique (attribute_name_code)
)
    comment 't_product_attribute_name' charset = utf8mb3
                                       row_format = DYNAMIC;

create index t_product_attribute_name_attribute_name_IDX
    on ecp_product.t_product_attribute_name (attribute_name);

create index t_product_attribute_name_business_code_IDX
    on ecp_product.t_product_attribute_name (business_code);

create table ecp_product.t_product_attribute_value
(
    TENANT_ID            int          null comment '租户号',
    REVISION             int          null comment '乐观锁',
    CREATED_BY           varchar(90)  null comment '创建人',
    CREATED_TIME         datetime     null comment '创建时间',
    UPDATED_BY           varchar(90)  null comment '更新人',
    UPDATED_TIME         datetime     null comment '更新时间',
    IS_DELETED           int          not null comment '逻辑删除',
    id                   bigint auto_increment comment '主键'
        primary key,
    attribute_name_code  varchar(90)  null comment '属性名code;属性名code，该value属于哪个属性名，如颜色',
    attribute_value_code varchar(90)  not null comment '属性值code;属性值编码，比如RED',
    attribute_value_name varchar(90)  not null comment '属性值名称;属性值名称，比如红黄蓝等',
    attribute_value_desc varchar(255) null comment '属性值描述;描述信息',
    sort                 int          null comment '排序;排序'
)
    comment 't_product_attribute_value' charset = utf8mb3
                                        row_format = DYNAMIC;

create index t_product_attribute_value_attribute_name_code_IDX
    on ecp_product.t_product_attribute_value (attribute_name_code);

create index t_product_attribute_value_attribute_value_code_IDX
    on ecp_product.t_product_attribute_value (attribute_value_code);

create table ecp_product.t_product_brand
(
    TENANT_ID         int          null comment '租户号',
    REVISION          int          null comment '乐观锁',
    CREATED_BY        varchar(90)  null comment '创建人',
    CREATED_TIME      datetime     null comment '创建时间',
    UPDATED_BY        varchar(90)  null comment '更新人',
    UPDATED_TIME      datetime     null comment '更新时间',
    IS_DELETED        int          not null comment '逻辑删除;逻辑删除0否1是',
    id                bigint auto_increment comment '主键'
        primary key,
    brand_name        varchar(90)  not null comment '品牌名',
    brand_code        varchar(90)  not null comment '品牌CODE',
    brand_desc        varchar(255) null comment '品牌描述',
    brand_logo_url    varchar(900) null comment '品牌logo图片',
    status            int          not null comment '状态;品牌状态，0：停用 1：启用',
    brand_parent_code varchar(90)  null comment '父级品牌CODE',
    level_code        varchar(255) not null comment '层级编码;品牌层级，level1，level2',
    constraint t_product_brand_brand_code_IDX
        unique (brand_code)
)
    comment 't_product_brand' charset = utf8mb3
                              row_format = DYNAMIC;

create index t_product_brand_brand_parent_code_IDX
    on ecp_product.t_product_brand (brand_parent_code);

create table ecp_product.t_product_bundles_sku
(
    id                  bigint auto_increment comment '主键'
        primary key,
    bundle_product_code varchar(90) not null comment '捆绑商品编码;捆绑商品编码',
    bundle_sku_code     varchar(90) not null comment '捆绑商品SKU编码',
    product_spu_code    varchar(90) null comment '组合商品SPU编码',
    product_sku_code    varchar(90) not null comment '商品SKU编码',
    product_quantity    int         null comment '商品数量;商品数量',
    sort                int         null comment '排序;排序',
    tenant_id           int         not null comment '租户号',
    created_by          varchar(90) not null comment '创建人',
    created_time        datetime    not null comment '创建时间',
    updated_by          varchar(90) null comment '更新人',
    updated_time        datetime    null comment '更新时间',
    is_deleted          int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision            int         not null comment '乐观锁'
)
    comment 't_product_bundles_sku' charset = utf8mb3
                                    row_format = DYNAMIC;

create table ecp_product.t_product_car_series
(
    tenant_id       varchar(90) null comment '租户号',
    revision        int         null comment '乐观锁',
    created_by      varchar(90) null comment '创建人',
    created_time    datetime    null comment '创建时间',
    updated_by      varchar(90) null comment '更新人',
    updated_time    datetime    null comment '更新时间',
    id              bigint auto_increment comment '主键'
        primary key,
    car_series_code varchar(90) null comment '车系编码',
    car_series_name varchar(90) null comment '车系名称',
    is_deleted      int         not null comment '是否删除;逻辑删除，0否 1是'
)
    comment '商品车系表';

create table ecp_product.t_product_category
(
    TENANT_ID            int          null comment '租户号',
    REVISION             int          null comment '乐观锁',
    CREATED_BY           varchar(90)  null comment '创建人',
    CREATED_TIME         datetime     null comment '创建时间',
    UPDATED_BY           varchar(90)  null comment '更新人',
    UPDATED_TIME         datetime     null comment '更新时间',
    IS_DELETED           int          not null comment '是否删除;逻辑删除 0：否 1：是',
    id                   bigint auto_increment comment '主键'
        primary key,
    category_code        varchar(90)  not null comment '分类id',
    category_name        varchar(255) not null comment '类别名称',
    category_desc        varchar(255) null comment '分类描述',
    category_logo_url    varchar(900) null comment '分类图片',
    brand_code           varchar(90)  null comment '品牌code',
    category_level       varchar(90)  not null comment '类别级别;类别级别，level1、level2、level3',
    parent_category_code varchar(90)  null comment '父类别code;当有父级别时有',
    business_code        varchar(90)  not null comment '业务线CODE',
    status               int          not null comment '状态;状态，0：禁用 1：启用',
    sort                 int          null comment '排序;排序',
    constraint t_product_category_category_code_IDX
        unique (category_code)
)
    comment 't_product_category' charset = utf8mb3
                                 row_format = DYNAMIC;

create index t_product_category_parent_category_code_IDX
    on ecp_product.t_product_category (parent_category_code);

create table ecp_product.t_product_content
(
    TENANT_ID              int                         null comment '租户号',
    REVISION               int                         null comment '乐观锁',
    CREATED_BY             varchar(90)                 null comment '创建人',
    CREATED_TIME           datetime                    null comment '创建时间',
    UPDATED_BY             varchar(90)                 null comment '更新人',
    UPDATED_TIME           datetime                    null comment '更新时间',
    IS_DELETED             int                         not null comment '逻辑删除',
    id                     bigint auto_increment comment '主键'
        primary key,
    product_code           varchar(90) charset utf8mb3 not null comment '商品编码',
    product_detail_content text charset utf8mb3        not null comment '商品详情;富文本存储的商品详情的HTML'
)
    comment 't_product_content' collate = utf8mb4_general_ci
                                row_format = DYNAMIC;

create index t_product_content_product_code_IDX
    on ecp_product.t_product_content (product_code);

create table ecp_product.t_product_coupon_usage_item
(
    id           bigint auto_increment comment '主键'
        primary key,
    rule_code    varchar(200) not null comment '规则编码',
    product_code varchar(200) null comment '商品编码',
    tenant_id    int          not null comment '租户号',
    created_by   varchar(90)  not null comment '创建人',
    created_time datetime     not null comment '创建时间',
    updated_by   varchar(90)  null comment '更新人',
    updated_time datetime     null comment '更新时间',
    is_deleted   int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int          not null comment '乐观锁',
    product_name varchar(200) null
)
    comment '优惠券与商品关联规则表' charset = utf8mb3;

create index idx_tpcui_product_code
    on ecp_product.t_product_coupon_usage_item (product_code);

create table ecp_product.t_product_coupon_usage_rule
(
    id                bigint auto_increment comment '主键'
        primary key,
    rule_code         varchar(200) not null comment '规则编码',
    rule_name         varchar(200) not null comment '规则名称',
    coupon_model_code varchar(200) null comment '卡券编码',
    coupon_name       varchar(200) null comment '卡券名称',
    tenant_id         int          not null comment '租户号',
    created_by        varchar(90)  not null comment '创建人',
    created_time      datetime     not null comment '创建时间',
    updated_by        varchar(90)  null comment '更新人',
    updated_time      datetime     null comment '更新时间',
    is_deleted        int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int          not null comment '乐观锁',
    business_code     varchar(90)  null comment '业务线编码',
    constraint rule_name
        unique (rule_name)
)
    comment '优惠券与商品关联规则表' charset = utf8mb3;

create index idx_tpcur_rule_code
    on ecp_product.t_product_coupon_usage_rule (rule_code);

create table ecp_product.t_product_detail
(
    TENANT_ID       int          null comment '租户号',
    REVISION        int          null comment '乐观锁',
    CREATED_BY      varchar(90)  null comment '创建人',
    CREATED_TIME    datetime     null comment '创建时间',
    UPDATED_BY      varchar(90)  null comment '更新人',
    UPDATED_TIME    datetime     null comment '更新时间',
    IS_DELETED      int          not null comment '逻辑删除',
    id              bigint auto_increment comment '主键'
        primary key,
    detail_code     varchar(90)  not null comment '详情编码;商品详情编码',
    product_code    varchar(90)  not null comment '商品编码;商品编码',
    detail_position varchar(90)  not null comment '详情位置;详情位置，LIST列表，BANNER图，DETAIL详情',
    detail_type     varchar(90)  not null comment '详情类型;详情类型，IMAGE突破，VIDEO视频，TEXT文字',
    detail_content  varchar(900) not null comment '内容详情;详情内容，如文字内容，视频和图片以S3的URL存储',
    sort            int          not null comment '内容排序;内容排序，控制展示顺序'
)
    comment 't_product_detail' charset = utf8mb3
                               row_format = DYNAMIC;

create index t_product_detail_product_code_IDX
    on ecp_product.t_product_detail (product_code);

create table ecp_product.t_product_detail_backup_20250414
(
    TENANT_ID       int                          null comment '租户号',
    REVISION        int                          null comment '乐观锁',
    CREATED_BY      varchar(90) charset utf8mb3  null comment '创建人',
    CREATED_TIME    datetime                     null comment '创建时间',
    UPDATED_BY      varchar(90) charset utf8mb3  null comment '更新人',
    UPDATED_TIME    datetime                     null comment '更新时间',
    IS_DELETED      int                          not null comment '逻辑删除',
    id              bigint default 0             not null comment '主键',
    detail_code     varchar(90) charset utf8mb3  not null comment '详情编码;商品详情编码',
    product_code    varchar(90) charset utf8mb3  not null comment '商品编码;商品编码',
    detail_position varchar(90) charset utf8mb3  not null comment '详情位置;详情位置，LIST列表，BANNER图，DETAIL详情',
    detail_type     varchar(90) charset utf8mb3  not null comment '详情类型;详情类型，IMAGE突破，VIDEO视频，TEXT文字',
    detail_content  varchar(900) charset utf8mb3 not null comment '内容详情;详情内容，如文字内容，视频和图片以S3的URL存储',
    sort            int                          not null comment '内容排序;内容排序，控制展示顺序'
);

create table ecp_product.t_product_faq
(
    TENANT_ID          int          null comment '租户号',
    REVISION           int          null comment '乐观锁',
    CREATED_BY         varchar(90)  null comment '创建人',
    CREATED_TIME       datetime     null comment '创建时间',
    UPDATED_BY         varchar(90)  null comment '更新人',
    UPDATED_TIME       datetime     null comment '更新时间',
    IS_DELETED         int          not null comment '逻辑删除',
    id                 bigint auto_increment comment '主键'
        primary key,
    faq_code           varchar(90)  not null comment 'QA编码',
    question           varchar(255) not null comment '问题',
    brand_code         varchar(90)  not null comment '品牌code;品牌code',
    question_type_code bigint       not null comment '问题类型code',
    category_level_1   varchar(90)  not null comment '一级商品分类编码;一级商品分类编码',
    category_level_2   varchar(90)  not null comment '二级商品分类编码;二级商品分类编码',
    question_answer    text         not null comment '问题答案',
    business_code      varchar(100) not null comment '业务线code',
    fulfilment_type    varchar(100) null comment '履约方式',
    constraint t_product_faq_faq_code_IDX
        unique (faq_code)
)
    charset = utf8mb3
    row_format = DYNAMIC;

create index t_product_faq_brand_code_IDX
    on ecp_product.t_product_faq (brand_code);

create table ecp_product.t_product_faq_type
(
    id            bigint auto_increment comment '主键'
        primary key,
    business_code varchar(90) not null comment '业务线编码;业务线编码',
    type_name     varchar(90) not null comment '问题类型;问题类型编码',
    tenant_id     int         null comment '租户号',
    created_by    varchar(90) not null comment '创建人',
    created_time  datetime    null comment '创建时间',
    updated_by    varchar(90) null comment '更新人',
    updated_time  datetime    null comment '更新时间',
    is_deleted    int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int         null comment '乐观锁',
    type_code     bigint      not null comment '编码code',
    constraint t_product_faq_type_type_code_IDX
        unique (type_code)
)
    comment 't_product_faq_type' charset = utf8mb3
                                 row_format = DYNAMIC;

create index t_product_faq_type_business_code_IDX
    on ecp_product.t_product_faq_type (business_code);

create table ecp_product.t_product_modify_log
(
    id                     bigint auto_increment comment '主键'
        primary key,
    product_code           varchar(90) null comment '订单号;订单号',
    modify_module          varchar(90) not null comment '修改模块;修改模块',
    modify_field_count     int         null comment '修改字段数量;修改字段数量',
    modify_field_old_value text        null comment '修改前字段值;涉及修改字段旧值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleOld","rule_desc":"descOld"}',
    modify_field_new_value text        null comment '修改后字段值;涉及修改字段新值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleNew","rule_desc":"descNew"}',
    operate_time           datetime    null comment '修改时间;修改时间',
    operate_user           varchar(90) null comment '修改人账号;修改人账号',
    tenant_id              int         not null comment '租户号',
    created_by             varchar(90) not null comment '创建人',
    created_time           datetime    not null comment '创建时间',
    updated_by             varchar(90) null comment '更新人',
    updated_time           datetime    null comment '更新时间',
    is_deleted             int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int         not null comment '乐观锁'
)
    comment 't_order_modify_detail_log';

create table ecp_product.t_product_modify_log_bak
(
    id               bigint auto_increment comment '主键'
        primary key,
    tenant_id        int          null comment '租户号',
    created_by       varchar(90)  null comment '创建人',
    created_time     datetime     null comment '创建时间',
    updated_by       varchar(90)  null comment '更新人',
    updated_time     datetime     null comment '更新时间',
    is_deleted       int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int          null comment '乐观锁',
    product_code     varchar(90)  not null comment '商品编码;商品编码',
    product_name     varchar(255) not null comment '商品名称;商品名称，冗余字段',
    modify_page      int          not null comment '修改页面;修改页面 1：列表页 2：详情页',
    operate_type     int          not null comment '操作类型;操作类型 1:编辑商品 2：上架商品 3：下架商品 4：停用商品 5：启用商品',
    modify_user_id   varchar(90)  not null comment '修改人用户id;修改人ID',
    modify_user_name varchar(90)  not null comment '修改人;修改人账号'
)
    comment 't_product_modify_log' charset = utf8mb3
                                   row_format = DYNAMIC;

create index t_product_modify_log_product_code_IDX
    on ecp_product.t_product_modify_log_bak (product_code);

create table ecp_product.t_product_policy
(
    id                    bigint auto_increment comment '主键'
        primary key,
    policy_code           varchar(90)  not null comment '商品服务条款code;条款ID',
    policy_name           varchar(200) not null comment '条款名称;条款名称',
    brand_code            varchar(90)  not null comment '品牌code;品牌编码',
    category_code_level_1 varchar(90)  not null comment '一级商品分类;一级商品分类编码',
    category_code_level_2 varchar(90)  not null comment '二级商品分类;二级商品分类编码',
    policy_content        text         not null comment '条款内容;条款内容',
    business_code         varchar(100) not null comment '业务线编码',
    TENANT_ID             int          null comment '租户号',
    REVISION              int          null comment '乐观锁',
    CREATED_BY            varchar(90)  null comment '创建人',
    CREATED_TIME          datetime     null comment '创建时间',
    UPDATED_BY            varchar(90)  null comment '更新人',
    UPDATED_TIME          datetime     null comment '更新时间',
    IS_DELETED            int          not null comment '逻辑删除',
    fulfilment_type       varchar(100) null comment '履约方式',
    constraint t_product_policy_policy_code_IDX
        unique (policy_code)
)
    comment 't_product_policy' charset = utf8mb3
                               row_format = DYNAMIC;

create index t_product_policy_brand_code_IDX
    on ecp_product.t_product_policy (brand_code);

create table ecp_product.t_product_relation_info
(
    TENANT_ID     int         null comment '租户号',
    REVISION      int         null comment '乐观锁',
    CREATED_BY    varchar(90) null comment '创建人',
    CREATED_TIME  datetime    null comment '创建时间',
    UPDATED_BY    varchar(90) null comment '更新人',
    UPDATED_TIME  datetime    null comment '更新时间',
    IS_DELETED    int         not null comment '逻辑删除',
    id            bigint auto_increment comment '主键'
        primary key,
    relation_code varchar(90) not null comment '常见问题编码;常见问题编码',
    relation_type varchar(90) not null comment '关系类型;商品关系类型，FAQ问题，POLICY购买条款',
    product_code  varchar(90) not null comment '商品编码;商品编码',
    faq_code      varchar(90) null comment 'FAQ编码;FAQ编码，可为空',
    policy_code   varchar(90) null comment 'POLICY编码;POLICY编码，可为空'
)
    comment 't_product_relation_info' charset = utf8mb3
                                      row_format = DYNAMIC;

create index t_product_relation_info_faq_code_IDX
    on ecp_product.t_product_relation_info (faq_code);

create index t_product_relation_info_policy_code_IDX
    on ecp_product.t_product_relation_info (policy_code);

create index t_product_relation_info_product_code_IDX
    on ecp_product.t_product_relation_info (product_code);

create table ecp_product.t_product_schedule_on_shelf_detail
(
    TENANT_ID             int           null comment '租户号',
    REVISION              int           null comment '乐观锁',
    CREATED_BY            varchar(90)   null comment '创建人',
    CREATED_TIME          datetime      null comment '创建时间',
    UPDATED_BY            varchar(90)   null comment '更新人',
    UPDATED_TIME          datetime      null comment '更新时间',
    IS_DELETED            int default 0 null comment '逻辑删除',
    id                    bigint        not null comment '主键'
        primary key,
    product_code          varchar(90)   not null comment '商品编码',
    schedule_onshelf_time datetime      null comment '定时上架时间',
    onshelf_status        varchar(90)   null comment '定时上架状态 (成功：SUCCESS, 失败：FAILURE, 待上架：WAITING) ',
    fail_msg              varchar(255)  null comment '失败原因'
)
    charset = utf8mb3;

create table ecp_product.t_product_series
(
    tenant_id       varchar(90) null comment '租户号',
    revision        int         null comment '乐观锁',
    created_by      varchar(90) null comment '创建人',
    created_time    datetime    null comment '创建时间',
    updated_by      varchar(90) null comment '更新人',
    updated_time    datetime    null comment '更新时间',
    id              bigint auto_increment comment '主键'
        primary key,
    car_series_code varchar(90) null comment '车系编码',
    car_series_name varchar(90) null comment '车系名称',
    is_deleted      int         not null comment '是否删除;逻辑删除，0否 1是'
)
    comment '商品车系表';

create table ecp_product.t_product_sku
(
    TENANT_ID         int          null comment '租户号',
    REVISION          int          null comment '乐观锁',
    CREATED_BY        varchar(90)  null comment '创建人',
    CREATED_TIME      datetime     null comment '创建时间',
    UPDATED_BY        varchar(90)  null comment '更新人',
    UPDATED_TIME      datetime     null comment '更新时间',
    IS_DELETED        int          not null comment '逻辑删除',
    id                bigint auto_increment comment '主键'
        primary key,
    product_sku_code  varchar(90)  not null comment 'sku编码;SKU编码',
    product_spu_code  varchar(90)  not null comment '商品编码;商品SPU编码',
    attribute_values  varchar(900) null comment 'sku属性值;stock组合属性, attribute_name_code:attribute_value_code, 多个属性值用，隔开',
    sku_quantity      int          null comment 'sku库存数量;库存数量',
    sale_price        bigint       null comment '销售价格;销售价格，单位分',
    market_price      bigint       null comment '市场价（划线价）;销售价格，单位分',
    order_unit_limit  bigint       null comment '单个订单限制购买数量;单个订单限制购买数量',
    sort              int          null comment '排序;排序',
    status            tinyint      null comment '上下架状态 0下架 1上架',
    model_code        varchar(100) null comment '金蝶SKU编码/卡券模板编码',
    sale_points_price int          null comment '现金+积分方式：销售金额,单位分',
    sale_points       int          null comment '现金+积分方式：积分',
    weight            int          null comment '重量，单位是g',
    constraint t_product_sku_product_sku_code_IDX
        unique (product_sku_code)
)
    charset = utf8mb3
    row_format = DYNAMIC;

create index t_product_sku_product_spu_code_IDX
    on ecp_product.t_product_sku (product_spu_code);

create table ecp_product.t_product_snapshot
(
    id                         bigint auto_increment comment '主键'
        primary key,
    product_version_code       varchar(90)    not null comment '商品快照版本code;product_version_code',
    product_code               varchar(90)    not null comment '商品编码;商品编码',
    product_sku_code           varchar(90)    not null comment '商品SKU编码;商品SKU编码',
    product_name               varchar(90)    not null comment '商品名称',
    product_image_url          varchar(900)   not null comment '商品主图URL;商品主图URL',
    product_sale_price         int            not null comment '商品售价;商品售价',
    product_market_price       int            null comment '商品原价;商品原价',
    brand_code                 varchar(90)    not null comment '品牌code;品牌code',
    brand_name                 varchar(255)   not null comment '品牌名;brand_name',
    attribute_values           varchar(255)   not null comment '商品属性;商品属性 KEY:VALUE;',
    category_code_level_1_name varchar(255)   null comment '一级分类名;一级分类名',
    category_code_level_1      varchar(255)   null comment '一级分类code;一级分类code',
    category_code_level_2_name varchar(255)   null comment '二级分类名;二级分类名',
    category_code_level_2      varchar(255)   null comment '二级分类code;二级分类编码',
    tax_rate                   decimal(10, 2) null comment '商品税率',
    tenant_id                  int            not null comment '租户号',
    created_by                 varchar(90)    not null comment '创建人',
    created_time               datetime       not null comment '创建时间',
    updated_by                 varchar(90)    null comment '更新人',
    updated_time               datetime       null comment '更新时间',
    is_deleted                 int            not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision                   int            not null comment '乐观锁',
    feight_template_code       varchar(100)   null comment '运费模板编码',
    payment_type               int default 1  null comment '支付方式（1：现金，2：现金+积分）',
    car_series_code            varchar(100)   null comment '车系编码',
    car_series_name            varchar(100)   null comment '车系名称',
    merchant_account_no        varchar(100)   null comment '商户号',
    model_code                 varchar(100)   null comment '金蝶SKU编码/卡券模板编码',
    sale_points_price          int            null comment '现金+积分方式：销售金额,单位分',
    sale_points                int            null comment '现金+积分方式：积分',
    weight                     int            null comment '重量，单位是g',
    category_code_level_3      varchar(255)   null comment '三级分类编码',
    category_code_level_3_name varchar(255)   null comment '三级分类名称',
    category_json              varchar(1000)  null comment '分类Json',
    merchant_account_id        bigint         null comment '商户id',
    merchant_account_name      varchar(100)   null comment '商户名称',
    tax_code                   varchar(100)   null comment '税务编码'
)
    comment 't_product_snapshot' charset = utf8mb3
                                 row_format = DYNAMIC;

create index t_product_snapshot_product_sku_code_IDX
    on ecp_product.t_product_snapshot (product_sku_code);

create table ecp_product.t_product_spu_category
(
    tenant_id             varchar(90) null comment '租户号',
    revision              int         null comment '乐观锁',
    created_by            varchar(90) null comment '创建人',
    created_time          datetime    null comment '创建时间',
    updated_by            varchar(90) null comment '更新人',
    updated_time          datetime    null comment '更新时间',
    id                    bigint auto_increment comment '主键'
        primary key,
    product_code          varchar(90) not null comment '商品编码;商品编码',
    category_code_level_1 varchar(90) null comment '一级分类code;一级分类编码',
    category_code_level_2 varchar(90) null comment '二级分类code;二级分类编码',
    category_code_level_3 varchar(90) null comment '三级分类code;三级分类编码',
    is_deleted            int         not null comment '是否删除;逻辑删除，0否 1是'
)
    comment '商品-分类关系表';

create table ecp_product.t_product_spu_info
(
    TENANT_ID             varchar(90)   null comment '租户号',
    REVISION              int           null comment '乐观锁',
    CREATED_BY            varchar(90)   null comment '创建人',
    CREATED_TIME          datetime      null comment '创建时间',
    UPDATED_BY            varchar(90)   null comment '更新人',
    UPDATED_TIME          datetime      null comment '更新时间',
    id                    bigint auto_increment comment '主键'
        primary key,
    product_code          varchar(90)   not null comment '商品编码;商品编码',
    brand_code            varchar(90)   null comment '品牌code;品牌编码',
    category_code_level_1 varchar(90)   null comment '一级分类code;一级分类编码',
    category_code_level_2 varchar(90)   null comment '二级分类code;二级分类编码',
    product_name          varchar(255)  not null comment '商品名称;商品名称',
    sales_unit            varchar(90)   null comment '销售单位code;销售单位',
    stock_limit_flag      int           not null comment '是否库存限制',
    market_price          bigint        null comment '市场价(划线价)',
    sale_price            bigint        not null comment '商品售价',
    sale_count            bigint        not null comment '销售数量',
    favor_count           bigint        not null comment '收藏数量',
    visit_count           bigint        not null comment '访问数量',
    keywords              varchar(255)  null comment '关键词;关键词，未来用于搜索使用',
    coefficient           int           null comment '商品推荐系数;推荐系数推荐系数，由商户系数，商品销量，点击率等共同计算',
    customer_buy_limit    int           null comment '单个客户可购买数量;单个客户购买数量限制',
    last_onshelf_time     datetime      null comment '最后一次上架时间;最后一次上架时间',
    last_offshelf_time    datetime      null comment '最后一次下架时间;最后一次下架时间',
    schedule_onshelf_time datetime      null comment '定时上架时间;定时上架时间(最后一次)',
    last_modify_user_code varchar(90)   not null comment '最后修改人;最后修改人编码',
    shelf_status          int           not null comment '商品上下架状态;上下架状态，0草稿 1未上架 2已上架 3已下架',
    tax_rate_id           bigint        null comment '商品税率，如0.06代表6%',
    enable_status         int           not null comment '商品停用状态;启用状态，0停用 1启用',
    is_deleted            int           not null comment '是否删除;逻辑删除，0否 1是',
    downtime              datetime      null comment '停用时间',
    fulfilment_type       tinyint       null comment '商品履约类型 0:组合商品履约； 1：远程车控Remote Service；2：PIVI Subscription Service； 3: 实物商品；',
    business_code         varchar(90)   not null comment '业务线编码',
    product_type          tinyint       not null comment '商品类型：1：普通商品 2:捆绑商品',
    product_intro         varchar(255)  null comment '商品简介',
    recommond_spu_code    varchar(90)   null,
    gift_address_switch   tinyint       null comment '是否开启收礼物 0：否 1：是',
    feight_template_code  varchar(100)  null comment '运费模板编码',
    payment_type          int default 1 null comment '支付方式（1：现金，2：现金+积分）',
    car_series_code       varchar(100)  null comment '车系编码',
    merchant_account_id   bigint        null comment '商户id',
    sale_points_price     int           null comment '现金+积分方式：销售金额,单位分',
    sale_points           int           null comment '现金+积分方式：积分',
    constraint t_product_spu_info_product_code_IDX
        unique (product_code)
)
    comment 't_product_spu_info' charset = utf8mb3
                                 row_format = DYNAMIC;

create index t_product_spu_info_brand_code_IDX
    on ecp_product.t_product_spu_info (brand_code);

create table ecp_product.t_product_tax
(
    id            bigint auto_increment comment '主键'
        primary key,
    tax_id        bigint         not null comment '税率ID;税率ID，雪花算法',
    tax_rate      decimal(12, 2) not null comment '税率值;税率，精度为小数点后两位',
    tax_code      varchar(90)    null comment '税务编码;税务编码',
    tenant_id     int            not null comment '租户号',
    created_by    varchar(90)    not null comment '创建人',
    created_time  datetime       not null comment '创建时间',
    updated_by    varchar(90)    null comment '更新人',
    updated_time  datetime       null comment '更新时间',
    is_deleted    int            not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int            not null comment '乐观锁',
    business_code varchar(90)    null comment '业务线编码',
    constraint t_product_tax_tax_id_IDX
        unique (tax_id)
)
    comment 't_product_tax' charset = utf8mb3;

create table ecp_product.t_product_unit
(
    id            bigint auto_increment comment '主键'
        primary key,
    tenant_id     int         null comment '租户号',
    created_by    varchar(90) null comment '创建人',
    created_time  datetime    null comment '创建时间',
    updated_by    varchar(90) null comment '更新人',
    updated_time  datetime    null comment '更新时间',
    is_deleted    int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int         null comment '乐观锁',
    business_code varchar(90) not null comment '业务线CODE',
    business_name varchar(90) null comment '业务线名称',
    unit_code     varchar(90) not null comment '单位编码;单位编码',
    unit_value    varchar(90) not null comment '单位值;单位值'
)
    comment 't_product_unit' charset = utf8mb3
                             row_format = DYNAMIC;

create index t_product_unit_unit_code_IDX
    on ecp_product.t_product_unit (unit_code);

create table ecp_payment.t_refund_payment_order
(
    id                bigint auto_increment comment '主键'
        primary key,
    app_no            varchar(90)  not null comment '应用编号;应用编号',
    order_no          varchar(90)  not null comment '订单编号;订单编号',
    sub_order_code    varchar(90)  null comment '子订单号，当订单号为父单号时，记录哪笔子单退款，当订单号为子单时，其为子单号',
    trade_no          varchar(90)  not null comment '交易流水号;交易流水号',
    apply_no          varchar(90)  not null comment '退款申请单号;退款申请单号，平台生成，全局唯一',
    channel_code      varchar(100) not null comment '渠道编码',
    refund_amount     int          not null comment '退款金额;退款金额单位分',
    description       varchar(255) null comment '退款原因',
    notify_url        varchar(255) not null comment '回调地址URL;退款结果回调地址URL',
    refund_type       varchar(90)  not null comment '退款类型;线上ONLINE 线下OFFLINE',
    operate_user_name varchar(90)  null comment '操作人名称;操作人姓名',
    operate_user_code varchar(90)  null comment '操作人编码;操作人编码',
    refund_no         varchar(90)  null comment '退款单号;退款单号，支付商返回',
    refund_state      varchar(90)  not null comment '退款状态;初始化INIT
交易处理中 PENDING
交易成功 SUCCESS
交易失败 FAI',
    merchant_no       varchar(90)  null comment '商户号;商户号',
    tenant_id         int          not null comment '租户号',
    created_by        varchar(90)  not null comment '创建人',
    created_time      datetime     not null comment '创建时间',
    updated_by        varchar(90)  null comment '更新人',
    updated_time      datetime     null comment '更新时间',
    is_deleted        int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int          not null comment '乐观锁'
)
    comment 't_refund_payment_order' charset = utf8mb3
                                     row_format = DYNAMIC;

create index idx_apply_no_order_no_trade_no
    on ecp_payment.t_refund_payment_order (apply_no, order_no, trade_no);

create table ecp_payment.t_refund_request_order
(
    id                bigint auto_increment comment '主键'
        primary key,
    jlr_id            varchar(90)   not null comment '用户JLRID;用户JLRID',
    order_no          varchar(90)   not null comment '原单号;原单号',
    sub_order_code    varchar(90)   not null comment '子订单号;子订单号，当订单号为父单号时，记录哪笔子单退款，当订单号为子单时，其为子单号',
    refund_order_code varchar(90)   not null comment '退单号;退单号',
    pay_apply_no      varchar(90)   not null comment '交易流水号;交易流水号',
    refund_apply_no   varchar(90)   not null comment '退单流水号;支付中心',
    refund_amount     int           not null comment '退款金额;退款金额，单位分',
    submit_time       datetime      not null comment '申请提交时间;申请提交时间',
    refund_state      int           not null comment '退款状态;初始化：INIT交易处理中： PENDING交易成功 ：SUCCESS交易失败 ：FAI',
    finish_time       datetime      null comment '退款完成时间;退款完成时间',
    operate_user_name varchar(90)   null comment '操作人姓名;操作人姓名',
    operate_user_code varchar(90)   null comment '操作人编码;操作人编码',
    description       varchar(255)  null comment '退款原因;退款原因',
    tenant_id         int           not null comment '租户号',
    created_by        varchar(90)   not null comment '创建人',
    created_time      datetime      not null comment '创建时间',
    updated_by        varchar(90)   null comment '更新人',
    updated_time      datetime      null comment '更新时间',
    is_deleted        int           not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int           not null comment '乐观锁',
    refund_type       int default 0 null comment '退款类型 0-普通退款 1-分账退款'
)
    comment 't_refund_request_order' charset = utf8mb3;

create table ecp_subscription.t_remote_original_data
(
    id           bigint auto_increment comment '主键'
        primary key,
    data_no      varchar(90)  not null comment '数据批次号;数据批次号，以时间存储，20240812',
    raw_json     json         not null comment '原始数据的json;TSDP原始数据json',
    status       int          not null comment '处理状态;数据状态:1待处理 2处理成功 3处理失败',
    fail_type    varchar(90)  null comment '失败类型;处理失败类型: RAW_ERROR：数据解析错误DP_FAIL：DP数据不匹配 SAVE_FAIL：系统保存异常',
    fail_desc    varchar(255) null comment '失败详情;处理失败的描述',
    miss_count   int          not null comment '失败处理次数;处理失败次数默认0，超过5次不在处理',
    tenant_id    int          not null comment '租户号',
    created_by   varchar(90)  not null comment '创建人',
    created_time datetime     not null comment '创建时间',
    updated_by   varchar(90)  null comment '更新人',
    updated_time datetime     null comment '更新时间',
    is_deleted   int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int          not null comment '乐观锁'
)
    comment 't_remote_original_data';

create table ecp_product.t_remote_package
(
    id            bigint auto_increment comment '主键'
        primary key,
    business_code varchar(90)  not null comment '业务线code;业务线编码VCS',
    package_code  varchar(255) not null comment '服务包编码;服务包编码',
    created_user  varchar(90)  not null comment '上传用户;上传用户名',
    tenant_id     int          not null comment '租户号',
    created_by    varchar(90)  not null comment '创建人',
    created_time  datetime     not null comment '创建时间',
    updated_by    varchar(90)  null comment '更新人',
    updated_time  datetime     null comment '更新时间',
    is_deleted    int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int          null comment '乐观锁'
)
    comment 't_remote_package' charset = utf8mb3
                               row_format = DYNAMIC;

create table ecp_subscription.t_remote_package
(
    id               int auto_increment comment '主键'
        primary key,
    package_code     varchar(255) not null comment '服务包编码;服务包编码',
    created_user     varchar(90)  not null comment '上传用户;上传用户名',
    tenant_id        int          not null comment '租户号',
    created_by       varchar(90)  not null comment '创建人',
    created_time     datetime     not null comment '创建时间',
    updated_by       varchar(90)  null comment '更新人',
    updated_time     datetime     null comment '更新时间',
    is_deleted       int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int          null comment '乐观锁',
    car_system_model varchar(90)  not null comment '车机型号'
)
    comment 't_remote_package' charset = utf8mb3
                               row_format = DYNAMIC;

create index idx_pkcode
    on ecp_subscription.t_remote_package (package_code);

create index idx_rp_package_tenant_model_deleted
    on ecp_subscription.t_remote_package (package_code, tenant_id, car_system_model, is_deleted);

create table ecp_subscription.t_remote_renew_batch_records
(
    id                 bigint auto_increment comment '主键'
        primary key,
    batch_no           bigint       not null comment '批次号;批次号，雪花算法ID',
    upload_file        varchar(255) not null comment '上传原始文件S3文件URL;上传原始文件S3文件URL',
    verify_result      int          null comment '校验结果;校验结果;校验结果 0：不通过 1：通过',
    deal_status        int          null comment '处理状态;处理状态 0：待处理  1：处理中 2：已处理',
    verify_result_file varchar(255) null comment '校验结果文件;对于失败的校验结果S3文件URL',
    operator           varchar(90)  not null comment '操作人账号',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁'
)
    comment 't_remote_renew_batch_records';

create table ecp_subscription.t_remote_renew_detail_records
(
    id                 bigint auto_increment comment '主键'
        primary key,
    batch_no           bigint       not null comment '批次号;批次编号，单条修改记录拥有唯一的雪花算法生成的id; 对于批量修改就是批次号',
    car_vin            varchar(90)  null comment 'VIN;VIN',
    modify_before_date datetime     null comment '修改前过期日;修改前过期日',
    modify_after_date  datetime     not null comment '修改后过期日;修改后过期日',
    modify_status      int          not null comment '修改状态;修改状态：1：进行中 2：修改成功 3：修改失败',
    error_desc         varchar(255) null comment '错误信息描述;错误信息描述',
    data_source        int          not null comment '数据来源;数据来源 1单次修改 2批量修改',
    operator           varchar(90)  not null comment '操作人账号',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁'
)
    comment 't_remote_renew_detail_records';

create table ecp_report.t_report_download_task
(
    id                bigint auto_increment comment '主键'
        primary key,
    task_code         varchar(90)  not null comment '任务编码;任务编码',
    task_type         int          not null comment '任务类型;任务类型，1：商品销售明细报表 2：商品退单明细报表 3：通知日志报表',
    request_param     varchar(900) not null comment '请求参数;请求参数，json格式的请求参数，记录当前任务的条件筛选，例子：
{"orderStatus":"1",orderCreateTime:"2023-10-10",orderEndTime:"2023-10-10"}',
    task_submit_time  datetime     not null comment '任务发起时间;任务发起时间',
    task_finish_time  datetime     null comment '任务结束时间;任务结束时间',
    download_file_url varchar(255) null comment '下载文件URL;下载文件URL',
    task_status       int          not null comment '任务状态;任务状态，1生成中 2已生成 3已取消 4生成失败',
    tenant_id         int          not null comment '租户号',
    created_by        varchar(90)  not null comment '创建人',
    created_time      datetime     not null comment '创建时间',
    updated_by        varchar(90)  null comment '更新人',
    updated_time      datetime     null comment '更新时间',
    is_deleted        int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int          not null comment '乐观锁'
)
    comment 't_report_download_task' charset = utf8mb3
                                     row_format = DYNAMIC;

create table ecp_subscription.t_series_brand_mapping_data
(
    id              bigint auto_increment comment '主键'
        primary key,
    series_code     varchar(90)  not null comment '车型系列编码;车型系列编码',
    dp_series_name  varchar(90)  not null comment '车型系列中文名;车型系列中文名',
    series_name     varchar(100) null comment '车型系列名称',
    brand_name_view varchar(90)  null comment '品牌显示名;品牌显示名',
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁'
)
    comment 't_series_brand_mapping_data' charset = utf8mb3
                                          row_format = DYNAMIC;

create table ecp_order.t_shopping_car
(
    id                 bigint auto_increment comment '主键'
        primary key,
    consumer_code      varchar(90)  not null comment '用户编码;用户编码',
    cart_code          varchar(255) not null comment '购物车code;购物车code，雪花算法',
    original_fee_total bigint       not null comment '购物车原价;购物车原价，单位分',
    discount_fee_total bigint       not null comment '购物车折扣价;购物车折扣价，单位分',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          null comment '乐观锁',
    constraint t_shopping_car_consumer_code_IDX
        unique (consumer_code)
)
    comment 't_shopping_car' row_format = DYNAMIC;

create table ecp_order.t_shopping_car_item
(
    id               bigint auto_increment comment '主键'
        primary key,
    consumer_code    varchar(90) not null comment '用户编码;用户编码',
    incontrol_id     varchar(90) null comment 'ICR',
    cart_code        varchar(90) not null comment '购物车code;购物车code',
    cart_item_code   varchar(90) not null comment '购物车商品编码;购物车商品编码',
    cart_item_type   int         not null comment '商品履约类型 0:组合商品履约； 1：远程车控Remote Service；2：PIVI Subscription Service； 3: 实物商品；',
    product_code     varchar(90) not null comment '商品编码;商品编码',
    product_sku_code varchar(90) not null comment '商品SKU编码;商品SKU编码',
    quantity         int         not null comment '商品数量;商品数量',
    series_code      varchar(90) null comment '车型编码;车型编码',
    series_name      varchar(90) null comment '车型名称',
    brand_code       varchar(90) null comment '品牌code',
    car_vin          varchar(90) null comment '车辆VIN码;车辆VIN码',
    tenant_id        int         not null comment '租户号',
    created_by       varchar(90) not null comment '创建人',
    created_time     datetime    not null comment '创建时间',
    updated_by       varchar(90) null comment '更新人',
    updated_time     datetime    null comment '更新时间',
    is_deleted       int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int         not null comment '乐观锁'
)
    comment 't_shopping_car_item' row_format = DYNAMIC;

create index t_shopping_car_item_cart_code_IDX
    on ecp_order.t_shopping_car_item (cart_code, product_code, series_code, car_vin);

create index t_shopping_car_item_cart_item_code_IDX
    on ecp_order.t_shopping_car_item (cart_item_code);

create index t_shopping_car_item_consumer_code_IDX
    on ecp_order.t_shopping_car_item (consumer_code, brand_code);

create table ecp_notification.t_short_link_click_total
(
    id           bigint auto_increment comment '自增主键id'
        primary key,
    url_code     varchar(90) not null comment '短链编码',
    click_total  bigint      not null comment '点击总数',
    tenant_id    int         not null comment '租户号',
    created_by   varchar(90) not null comment '创建人',
    created_time datetime    not null comment '创建时间',
    updated_by   varchar(90) null comment '更新人',
    updated_time datetime    null comment '更新时间',
    is_deleted   int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int         not null comment '乐观锁'
)
    comment '短链点击总数表';

create table ecp_notification.t_short_link_click_user
(
    id           bigint auto_increment comment '自增主键id'
        primary key,
    url_code     varchar(90) not null comment '短链编码',
    jlr_id       varchar(90) null comment '用户全局唯一ID',
    phone        varchar(90) null comment '手机号',
    click_count  int         not null comment '点击次数',
    tenant_id    int         not null comment '租户号',
    created_by   varchar(90) not null comment '创建人',
    created_time datetime    not null comment '创建时间',
    updated_by   varchar(90) null comment '更新人',
    updated_time datetime    null comment '更新时间',
    is_deleted   int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int         not null comment '乐观锁'
)
    comment '短链点击用户记录表';

create table ecp_notification.t_short_link_create_record
(
    id             bigint auto_increment comment '自增主键id'
        primary key,
    config_id      bigint       not null comment '雪花算法生成',
    url_code       varchar(90)  not null comment '短链编码',
    operator       varchar(90)  not null comment '操作人',
    brand_code     varchar(90)  not null comment '品牌code: LAN、JAG',
    base_url       varchar(256) not null comment '跳转的基础路径',
    params         json         null comment '跳转短链需要的参数',
    config_content varchar(256) null comment '配置内容',
    short_link     varchar(900) null comment '生成的短链',
    expiry_date    datetime     null comment '失效时间',
    remark         varchar(900) null comment '发送消息，包含了发送的参数',
    tenant_id      int          not null comment '租户号',
    created_by     varchar(90)  not null comment '创建人',
    created_time   datetime     not null comment '创建时间',
    updated_by     varchar(90)  null comment '更新人',
    updated_time   datetime     null comment '更新时间',
    is_deleted     int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int          not null comment '乐观锁'
)
    comment '短链生成记录表';

create table ecp_notification.t_sms_report_resp
(
    id           bigint auto_increment comment '自增主键id'
        primary key,
    msg_id       varchar(90)  null comment '短信编号',
    phone        varchar(20)  null comment '下行手机号码',
    status       varchar(10)  null comment '短信发送结果：0-成功；1-接口处理失败；2-运营商网关失败',
    description  varchar(255) null comment 'status为1时的错误描述',
    wg_code      varchar(255) null comment 'status为2时的运营商网关原始错误码',
    send_time    varchar(50)  null comment '短信发送时间，格式yyyy-MM-dd HH:mm:ss',
    sms_count    int          null comment '长短信条数',
    sms_index    int          null comment '长短信第几条标示',
    tenant_id    int          not null comment '租户号',
    created_by   varchar(90)  not null comment '创建人',
    created_time datetime     not null comment '创建时间',
    updated_by   varchar(90)  null comment '更新人',
    updated_time datetime     null comment '更新时间',
    is_deleted   int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int          not null comment '乐观锁'
)
    comment '短信状态报告表';

create table ecp_subscription.t_subscription_service
(
    id                  bigint auto_increment comment '主键'
        primary key,
    subscription_id     varchar(90)  null comment '订阅服务ID，雪花算法ID',
    incontrol_id        varchar(90)  null comment 'incontrol ID',
    car_vin             varchar(90)  not null comment '车架号;车架号',
    service_name        varchar(90)  not null comment '服务名;服务名',
    service_package     varchar(255) not null comment '服务包名;服务包名',
    expiry_date         datetime     not null comment '过期时间;过期时间',
    expire_date_utc0    datetime     null comment '过期时间UTC+0 , 如 2024/7/30 08:00:00',
    service_type        int          not null comment '服务包类型，1：Remote Service  2:非Remote Service 3：PIVI Subscription Service',
    jlr_subscription_id bigint       null comment 'APPD订阅码',
    iccid               varchar(100) null comment '联通ICCID',
    tenant_id           int          not null comment '租户号',
    created_by          varchar(90)  not null comment '创建人',
    created_time        datetime     not null comment '创建时间',
    updated_by          varchar(90)  null comment '更新人',
    updated_time        datetime     null comment '更新时间',
    is_deleted          int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision            int          not null comment '乐观锁'
)
    comment 't_subscription_service' charset = utf8mb3
                                     row_format = DYNAMIC;

create index idx_codepkg
    on ecp_subscription.t_subscription_service (service_package);

create index idx_icr
    on ecp_subscription.t_subscription_service (incontrol_id);

create index idx_remote_expiry
    on ecp_subscription.t_subscription_service (expiry_date);

create index idx_ss_vin_tenant_type_deleted
    on ecp_subscription.t_subscription_service (car_vin, tenant_id, service_type, is_deleted);

create index idx_vin
    on ecp_subscription.t_subscription_service (car_vin);

create table ecp_subscription.t_subscription_service_log
(
    id                    bigint auto_increment comment '主键'
        primary key,
    subscription_id       varchar(90)  not null comment '订阅服务ID;订阅服务ID',
    fufilment_id          varchar(90)  not null comment '履约ID;履约ID，为哪一次履约刷新服务',
    rollback_fufilment_id varchar(90)  null comment '回滚履约ID',
    refresh_before_date   datetime     not null comment '服务更新前时间;服务更新前时间',
    refresh_after_date    datetime     null comment '服务更新后时间;服务更新后时间',
    service_name          varchar(100) null comment '服务名',
    modify_type           tinyint      null comment '更新类型 1：订单履约更新 2：手动续费更新',
    tenant_id             int          not null comment '租户号',
    created_by            varchar(90)  not null comment '创建人',
    created_time          datetime     not null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int          not null comment '乐观锁'
)
    comment 't_subscription_service_log' charset = utf8mb3
                                         row_format = DYNAMIC;

create table ecp_system.t_system_business
(
    id            int auto_increment comment '主键'
        primary key,
    business_code varchar(90) not null comment '业务线编码;业务线CODE，BS+3位数字递增，唯一',
    business_name varchar(90) not null comment '业务线名称;业务线名称',
    parent_code   varchar(90) null comment '父业务线编码;父业务线编码，用于控制层级',
    sort          int         null comment '排序;排序',
    lead_user     varchar(90) null comment '负责人;负责人',
    phone         varchar(90) null comment '联系电话;联系电话',
    email         varchar(90) null comment '邮件;邮件',
    status        int         not null comment '状态;部门状态（0:正常 1停用）',
    tenant_id     int         not null comment '租户号',
    created_by    varchar(90) not null comment '创建人',
    created_time  datetime    not null comment '创建时间',
    updated_by    varchar(90) null comment '更新人',
    updated_time  datetime    null comment '更新时间',
    is_deleted    int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int         not null comment '乐观锁'
)
    comment 't_system_business' collate = utf8mb4_general_ci;

create table ecp_system.t_system_dict_data
(
    id           bigint auto_increment comment '字典编码'
        primary key,
    sort         int          default 0                 not null comment '字典排序',
    label        varchar(100) default ''                not null comment '字典标签',
    value        varchar(100) default ''                not null comment '字典键值',
    dict_type    varchar(100) default ''                not null comment '字典类型',
    status       tinyint      default 0                 not null comment '状态（0正常 1停用）',
    color_type   varchar(100) default ''                null comment '颜色类型',
    css_class    varchar(100) default ''                null comment 'css 样式',
    remark       varchar(500)                           null comment '备注',
    created_by   varchar(64)  default ''                not null comment '创建人',
    created_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by   varchar(64)  default ''                null comment '更新人',
    updated_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted   bit          default b'0'              not null comment '逻辑删除',
    revision     bigint       default 1                 not null comment '版本号'
)
    comment '字典数据表' collate = utf8mb4_unicode_ci;

create table ecp_system.t_system_dict_type
(
    id           bigint auto_increment comment '字典主键'
        primary key,
    name         varchar(100) default ''                not null comment '字典名称',
    type         varchar(100) default ''                not null comment '字典类型',
    status       tinyint      default 0                 not null comment '状态（0正常 1停用）',
    remark       varchar(500)                           null comment '备注',
    created_by   varchar(64)  default ''                not null comment '创建人',
    created_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by   varchar(64)  default ''                null comment '更新人',
    updated_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted   bit          default b'0'              not null comment '逻辑删除',
    deleted_time datetime                               null comment '删除时间',
    revision     bigint       default 1                 not null comment '版本号',
    constraint dict_type
        unique (type)
)
    comment '字典类型表' collate = utf8mb4_unicode_ci;

create table ecp_system.t_system_login_log
(
    id           int auto_increment comment '主键'
        primary key,
    log_type     int          not null comment '日志类型;日志类型',
    trace_id     varchar(90)  not null comment '链路追踪编号;链路追踪编号',
    user_id      int          not null comment '用户编号;用户编号',
    user_type    int          not null comment '用户类型;用户类型',
    user_name    varchar(90)  not null comment '用户账号;用户账号',
    result       int          not null comment '登录结果;登录结果',
    user_ip      varchar(90)  not null comment '登录IP;登录IP',
    user_agent   varchar(255) not null comment '浏览器;浏览器',
    tenant_id    int          not null comment '租户号',
    created_by   varchar(90)  not null comment '创建人',
    created_time datetime     not null comment '创建时间',
    updated_by   varchar(90)  null comment '更新人',
    updated_time datetime     null comment '更新时间',
    is_deleted   int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int          not null comment '乐观锁'
)
    comment 't_system_login_log' collate = utf8mb4_general_ci;

create table ecp_system.t_system_maintenance_config
(
    id             bigint auto_increment comment '主键'
        primary key,
    is_enable      int                                     not null comment '系统启用;是否启用 0 停用 1启用',
    remark         varchar(255) collate utf8mb4_general_ci not null comment '备注信息;备注信息',
    tips_image_url varchar(255) collate utf8mb4_general_ci not null comment '维护提示图片;维护提示图片',
    tenant_id      int                                     not null comment '租户号',
    created_by     varchar(90) collate utf8mb4_general_ci  not null comment '创建人',
    created_time   datetime                                not null comment '创建时间',
    updated_by     varchar(90) collate utf8mb4_general_ci  null comment '更新人',
    updated_time   datetime                                null comment '更新时间',
    is_deleted     int                                     not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int                                     not null comment '乐观锁'
)
    comment 't_system_maintanance_config';

create table ecp_system.t_system_maintenance_modify_log
(
    id                     bigint auto_increment comment '主键'
        primary key,
    modify_module          varchar(90) collate utf8mb4_general_ci not null comment '修改模块;修改模块',
    modify_field_count     int                                    null comment '修改字段数量;修改字段数量',
    modify_field_old_value text collate utf8mb4_general_ci        null comment '修改前字段值;涉及修改字段旧值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleOld","rule_desc":"descOld"}',
    modify_field_new_value text collate utf8mb4_general_ci        null comment '修改后字段值;涉及修改字段新值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleNew","rule_desc":"descNew"}',
    operate_time           datetime                               null comment '修改时间;修改时间',
    operate_user           varchar(90) collate utf8mb4_general_ci null comment '修改人账号;修改人账号',
    tenant_id              int                                    not null comment '租户号',
    created_by             varchar(90) collate utf8mb4_general_ci not null comment '创建人',
    created_time           datetime                               not null comment '创建时间',
    updated_by             varchar(90) collate utf8mb4_general_ci null comment '更新人',
    updated_time           datetime                               null comment '更新时间',
    is_deleted             int                                    not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int                                    not null comment '乐观锁'
)
    comment 't_order_modify_detail_log';

create table ecp_system.t_system_menu
(
    id             int auto_increment comment '主键'
        primary key,
    menu_name      varchar(90)  not null comment '菜单名称;菜单名称',
    menu_type      int          not null comment '菜单类型;菜单类型',
    sort           int          not null comment '排序',
    permission     varchar(255) not null comment '权限标识;权限标识system:product:add',
    parent_menu_id varchar(255) not null comment '父菜单id;父菜单id',
    path           varchar(255) null comment '路由地址;路由地址',
    menu_icon      varchar(255) null comment '菜单图标;菜单图标URL',
    component_name varchar(255) null comment '组件名称;组件名称',
    component      varchar(255) null comment '组件路径;组件路径',
    status         int          not null comment '菜单状态;菜单状态',
    visible        int          not null comment '可见状态;菜单是否可见状态，0：否 1：是',
    keep_alive     int          not null comment '是否缓存;前端是否缓存菜单，0：否 1：是',
    always_show    int          null comment '总是可见;总是可见用于控制当无子菜单时是否展示，0：不可见 1：可见',
    tenant_id      int          not null comment '租户号',
    revision       int          not null comment '乐观锁',
    created_by     varchar(90)  not null comment '创建人',
    created_time   datetime     not null comment '创建时间',
    updated_by     varchar(90)  null comment '更新人',
    updated_time   datetime     null comment '更新时间',
    is_deleted     int          not null comment '逻辑删除'
);

create table ecp_system.t_system_menu_back
(
    id             int auto_increment comment '主键'
        primary key,
    menu_name      varchar(90)  not null comment '菜单名称;菜单名称',
    menu_type      int          not null comment '菜单类型;菜单类型',
    sort           int          not null comment '排序',
    permission     varchar(255) not null comment '权限标识;权限标识system:product:add',
    parent_menu_id varchar(255) not null comment '父菜单id;父菜单id',
    path           varchar(255) null comment '路由地址;路由地址',
    menu_icon      varchar(255) null comment '菜单图标;菜单图标URL',
    component_name varchar(255) null comment '组件名称;组件名称',
    component      varchar(255) null comment '组件路径;组件路径',
    status         int          not null comment '菜单状态;菜单状态',
    visible        int          not null comment '可见状态;菜单是否可见状态，0：否 1：是',
    keep_alive     int          not null comment '是否缓存;前端是否缓存菜单，0：否 1：是',
    always_show    int          null comment '总是可见;总是可见用于控制当无子菜单时是否展示，0：不可见 1：可见',
    tenant_id      int          not null comment '租户号',
    revision       int          not null comment '乐观锁',
    created_by     varchar(90)  not null comment '创建人',
    created_time   datetime     not null comment '创建时间',
    updated_by     varchar(90)  null comment '更新人',
    updated_time   datetime     null comment '更新时间',
    is_deleted     int          not null comment '逻辑删除'
)
    collate = utf8mb4_general_ci
    row_format = DYNAMIC;

create table ecp_system.t_system_oauth2_access_token
(
    id            int auto_increment comment '主键'
        primary key,
    user_id       int          not null comment '用户编号;用户编号',
    user_type     int          not null comment '用户类型;用户类型',
    access_token  varchar(255) not null comment '访问令牌;访问令牌',
    refresh_token varchar(255) not null comment '刷新令牌;刷新令牌',
    client_id     varchar(255) not null comment '客户端编号;客户端编号',
    scopes        varchar(255) null comment '授权范围;授权范围',
    expires_time  datetime     not null comment '过期时间;过期时间',
    tenant_id     int          not null comment '租户号',
    created_by    varchar(90)  not null comment '创建人',
    created_time  datetime     not null comment '创建时间',
    updated_by    varchar(90)  null comment '更新人',
    updated_time  datetime     null comment '更新时间',
    is_deleted    int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int          not null comment '乐观锁',
    user_name     varchar(90)  not null comment '用户名'
)
    comment 't_system_oauth2_access_token' collate = utf8mb4_general_ci;

create table ecp_system.t_system_oauth2_client
(
    id                             bigint auto_increment comment '编号'
        primary key,
    client_id                      varchar(255)                          not null comment '客户端编号',
    secret                         varchar(255)                          not null comment '客户端密钥',
    name                           varchar(255)                          not null comment '应用名',
    logo                           varchar(255)                          not null comment '应用图标',
    description                    varchar(255)                          null comment '应用描述',
    status                         tinyint                               not null comment '状态',
    access_token_validity_seconds  int                                   not null comment '访问令牌的有效期',
    refresh_token_validity_seconds int                                   not null comment '刷新令牌的有效期',
    redirect_uris                  varchar(255)                          not null comment '可重定向的 URI 地址',
    authorized_grant_types         varchar(255)                          not null comment '授权类型',
    scopes                         varchar(255)                          null comment '授权范围',
    auto_approve_scopes            varchar(255)                          null comment '自动通过的授权范围',
    authorities                    varchar(255)                          null comment '权限',
    resource_ids                   varchar(255)                          null comment '资源',
    additional_information         varchar(4096)                         null comment '附加信息',
    created_by                     varchar(64) default ''                not null comment '鍒涘缓鑰',
    created_time                   datetime    default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by                     varchar(64) default ''                null comment '鏇存柊鑰',
    updated_time                   datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted                     bit         default b'0'              not null comment '鏄?惁鍒犻櫎',
    revision                       bigint      default 1                 not null comment '鐗堟湰鍙'
)
    comment 'OAuth2 客户端表' collate = utf8mb4_unicode_ci;

create table ecp_system.t_system_oauth2_refresh_token
(
    id            int auto_increment comment '主键'
        primary key,
    user_id       int          not null comment '用户编号;用户编号',
    refresh_token varchar(255) not null comment '刷新令牌;刷新令牌',
    user_type     int          not null comment '用户类型;用户类型',
    client_id     varchar(255) not null comment '客户端编号;客户端编号',
    scopes        varchar(255) null comment '授权范围;授权范围',
    expires_time  datetime     not null comment '过期时间;过期时间',
    tenant_id     int          not null comment '租户号',
    created_by    varchar(90)  not null comment '创建人',
    created_time  datetime     not null comment '创建时间',
    updated_by    varchar(90)  null comment '更新人',
    updated_time  datetime     null comment '更新时间',
    is_deleted    int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int          not null comment '乐观锁',
    user_name     varchar(90)  not null comment '用户名'
)
    collate = utf8mb4_general_ci;

create table ecp_system.t_system_operate_log
(
    id               bigint auto_increment comment '日志主键'
        primary key,
    trace_id         varchar(64)   default ''                not null comment '链路追踪编号',
    user_id          bigint                                  not null comment '用户编号',
    user_type        tinyint       default 0                 not null comment '用户类型',
    module           varchar(50)                             not null comment '模块标题',
    name             varchar(50)                             not null comment '操作名',
    type             bigint        default 0                 not null comment '操作分类',
    content          varchar(2000) default ''                not null comment '操作内容',
    exts             varchar(512)  default ''                not null comment '拓展字段',
    request_method   varchar(16)   default ''                null comment '请求方法名',
    request_url      varchar(255)  default ''                null comment '请求地址',
    user_ip          varchar(50)                             null comment '用户 IP',
    user_agent       varchar(200)                            null comment '浏览器 UA',
    java_method      varchar(512)  default ''                not null comment 'Java 方法名',
    java_method_args varchar(8000) default ''                null comment 'Java 方法的参数',
    start_time       datetime                                not null comment '操作时间',
    duration         int                                     not null comment '执行时长',
    result_code      int           default 0                 not null comment '结果码',
    result_msg       varchar(512)  default ''                null comment '结果提示',
    result_data      varchar(4000) default ''                null comment '结果数据',
    created_by       varchar(64)   default ''                not null comment '鍒涘缓鑰',
    created_time     datetime      default CURRENT_TIMESTAMP not null comment '鍒涘缓鏃堕棿',
    updated_by       varchar(64)   default ''                null comment '鏇存柊鑰',
    updated_time     datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '鏇存柊鏃堕棿',
    is_deleted       bit           default b'0'              not null comment '鏄?惁鍒犻櫎',
    tenant_id        bigint        default 0                 not null comment '租户编号',
    revision         bigint        default 1                 not null comment '鐗堟湰鍙'
)
    comment '操作日志记录' collate = utf8mb4_unicode_ci;

create table ecp_system.t_system_role
(
    id                        int auto_increment comment '主键'
        primary key,
    role_name                 varchar(90)  not null comment '角色名;角色名，唯一',
    role_code                 varchar(90)  not null comment '角色编码;角色编码，R+4位数字，递增唯一',
    sort                      int          null comment '排序',
    status                    int          not null comment '角色状态;状态 0：开启 1：关闭',
    create_type               int          not null comment '角色类型;角色类型 1：系统内置角色 2：自定义角色',
    remark                    varchar(255) null comment '备注信息;角色描述',
    data_scope                int          not null comment '数据范围;数据权限范围，1：全部数据权限 2：指定业务线数据权限 3：本人业务线数据权限 4：本人业务线及以下 5：仅本人数据权限',
    data_scope_business_codes varchar(255) null comment '指定业务线数据权限数组;数据范围(指定业务线)，多个业务线用，隔开',
    tenant_id                 int          not null comment '租户号',
    revision                  int          not null comment '乐观锁',
    created_by                varchar(90)  not null comment '创建人',
    created_time              datetime     not null comment '创建时间',
    updated_by                varchar(90)  null comment '更新人',
    updated_time              datetime     null comment '更新时间',
    is_deleted                int          not null comment '逻辑删除',
    type_code                 varchar(90)  not null comment '角色类型编码；超级管理员：super_admin，租户管理员：tenant_admin'
)
    collate = utf8mb4_general_ci;

create table ecp_system.t_system_role_menu
(
    id           int auto_increment comment '主键'
        primary key,
    role_id      varchar(90) not null comment '角色code',
    menu_id      varchar(90) not null comment '菜单code',
    tenant_id    int         not null comment '租户号',
    revision     int         not null comment '乐观锁',
    created_by   varchar(90) not null comment '创建人',
    created_time datetime    not null comment '创建时间',
    updated_by   varchar(90) null comment '更新人',
    updated_time datetime    null comment '更新时间',
    is_deleted   int         not null comment '逻辑删除'
)
    collate = utf8mb4_general_ci;

create table ecp_system.t_system_tenant
(
    id             int auto_increment comment '主键'
        primary key,
    tenant_code    varchar(90)  not null comment '租户编码;租户编码',
    tenant_name    varchar(90)  not null comment '租户名;租户名',
    contact_name   varchar(90)  null comment '联系人',
    contact_mobile varchar(90)  null comment '联系电话',
    status         int          not null comment '状态;租户状态（0正常 1停用）',
    domain         varchar(255) null comment '绑定域名',
    package_id     int          not null comment '租户套餐id;租户套餐编号',
    expire_time    datetime     null comment '过期时间',
    account_count  int          not null comment '账户数量;账号数量',
    created_by     varchar(90)  null comment '创建人',
    created_time   datetime     null comment '创建时间',
    updated_by     varchar(90)  null comment '更新人',
    updated_time   datetime     null comment '更新时间',
    is_deleted     int          not null comment '逻辑删除;逻辑删除',
    revision       int          null comment '乐观锁'
)
    comment 't_system_tenant' collate = utf8mb4_general_ci;

create table ecp_system.t_system_tenant_package
(
    id           int auto_increment comment '主键'
        primary key,
    package_name varchar(90)  not null comment '套餐名称;套餐名称',
    status       int          not null comment '状态;套餐状态（0正常 1停用）',
    remark       varchar(900) null comment '备注信息;备注',
    menu_ids     varchar(900) not null comment '关联菜单编码;关联的菜单编号',
    tenant_id    int          not null comment '租户号',
    created_by   varchar(90)  not null comment '创建人',
    created_time datetime     not null comment '创建时间',
    updated_by   varchar(90)  null comment '更新人',
    updated_time datetime     null comment '更新时间',
    is_deleted   int          not null comment '逻辑删除;逻辑删除 0否 1是',
    revision     int          not null comment '乐观锁'
)
    comment 't_system_tenant_package' collate = utf8mb4_general_ci;

create table ecp_system.t_system_user
(
    id                   int auto_increment comment '主键'
        primary key,
    user_name            varchar(90)   not null comment '用户账户;用户账户名/CDSID，不能修改，唯一',
    password             varchar(255)  not null comment '账户密码;用户密码，加密存储',
    nick_name            varchar(90)   not null comment '用户昵称;用户昵称',
    remark               varchar(255)  null comment '备注信息;备注信息',
    business_code        varchar(90)   null comment '业务线code;业务线编码',
    post_ids             varchar(255)  null comment '岗位编号数组;岗位编码数组',
    email                varchar(90)   null comment '邮箱地址;邮箱地址',
    mobile               varchar(255)  null comment '手机号;手机号',
    sex                  varchar(255)  null comment '性别;性别 0：女 1：男 2：未知',
    avatar               varchar(900)  null comment '头像;头像URL',
    user_identity_type   varchar(90)   not null comment '用户身份类型;，EXTERNAL:外部用户 INTERNAL：内部用户',
    status               int           not null comment '状态;状态 0：正常 1：停用',
    last_login_time      datetime      null comment '最后登录时间;最后登陆时间',
    last_login_ip        varchar(255)  null comment '最后登录ip;最后登录ip',
    last_login_role      varchar(90)   null comment '最后一次登录角色code;最后登录角色',
    tenant_id            int           not null comment '租户号',
    revision             int           null comment '乐观锁',
    created_by           varchar(90)   null comment '创建人',
    created_time         datetime      null comment '创建时间',
    updated_by           varchar(90)   null comment '更新人',
    updated_time         datetime      null comment '更新时间',
    is_deleted           int           not null comment '逻辑删除',
    pwd_last_modify_time datetime      not null comment '密码最后修改时间',
    pwd_error_times      tinyint       null comment '密码连续错误次数',
    login_count          int default 0 not null comment '用户登录次数',
    dept_id              int           null comment '部门ID'
)
    collate = utf8mb4_general_ci;

create table ecp_system.t_system_user_pwd_log
(
    id              bigint auto_increment comment '主键'
        primary key,
    tenant_id       int          not null comment '租户号',
    created_by      varchar(90)  not null comment '创建人',
    created_time    datetime     not null comment '创建时间',
    updated_by      varchar(90)  null comment '更新人',
    updated_time    datetime     null comment '更新时间',
    is_deleted      int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision        int          not null comment '乐观锁',
    user_id         int          not null comment '用户ID;用户userId',
    pwd_modify      varchar(255) not null comment '当前修改密码;修改密码内容密文',
    pwd_modify_time datetime     not null comment '密码修改时间;密码修改时间'
)
    comment 't_system_user_pwd_log' collate = utf8mb4_general_ci;

create table ecp_system.t_system_user_role
(
    id           int auto_increment comment '主键'
        primary key,
    user_id      int         not null comment '用户code;用户id',
    role_id      int         not null comment '角色code;角色id',
    tenant_id    int         not null comment '租户号',
    revision     int         not null comment '乐观锁',
    created_by   varchar(90) not null comment '创建人',
    created_time datetime    not null comment '创建时间',
    updated_by   varchar(90) null comment '更新人',
    updated_time datetime    null comment '更新时间',
    is_deleted   int         not null comment '逻辑删除'
)
    collate = utf8mb4_general_ci;

create table ecp_notification.t_task_modify_log
(
    id                     bigint auto_increment comment '主键'
        primary key,
    task_code              varchar(90) null comment '模板编码',
    modify_module          varchar(90) not null comment '修改模块;修改模块',
    modify_field_count     int         null comment '修改字段数量;修改字段数量',
    modify_field_old_value text        null comment '修改前字段值;涉及修改字段旧值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleOld","rule_desc":"descOld"}',
    modify_field_new_value text        null comment '修改后字段值;涉及修改字段新值，用于存储规则修改字段的json，json内采用key：value的方式存储，如{"rule_name":"testRuleNew","rule_desc":"descNew"}',
    operate_time           datetime    null comment '修改时间;修改时间',
    operate_user           varchar(90) null comment '修改人账号;修改人账号',
    tenant_id              int         not null comment '租户号',
    created_by             varchar(90) not null comment '创建人',
    created_time           datetime    not null comment '创建时间',
    updated_by             varchar(90) null comment '更新人',
    updated_time           datetime    null comment '更新时间',
    is_deleted             int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision               int         not null comment '乐观锁'
)
    comment 't_order_modify_detail_log';

create table ecp_notification.t_template_modify_log
(
    id             bigint auto_increment comment '主键'
        primary key,
    template_code  varchar(90) not null comment '模板编码;模板编码',
    modify_content varchar(90) not null comment '编辑内容;编辑内容',
    modify_user    varchar(90) not null comment '编辑人;操作人',
    modify_time    datetime    not null comment '编辑时间;编辑时间',
    tenant_id      int         not null comment '租户号',
    created_by     varchar(90) not null comment '创建人',
    created_time   datetime    not null comment '创建时间',
    updated_by     varchar(90) null comment '更新人',
    updated_time   datetime    null comment '更新时间',
    is_deleted     int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int         not null comment '乐观锁'
)
    comment 't_template_modify_log' charset = utf8mb3
                                    row_format = DYNAMIC;

create index idx_template_modify_log
    on ecp_notification.t_template_modify_log (template_code, modify_time);

create table ecp_subscription.t_unicom_rnr
(
    id               bigint auto_increment
        primary key,
    vin              varchar(90)  null,
    iccid            varchar(90)  null,
    real_name_flag   varchar(90)  null,
    dms_invoice_date datetime     null,
    error_msg        text         null,
    tenant_id        varchar(255) null
);

create index t_unicom_rnr_id_IDX
    on ecp_subscription.t_unicom_rnr (iccid);

create table ecp_subscription.t_unicom_rnr_batch_records
(
    id                 bigint auto_increment comment '主键'
        primary key,
    batch_no           bigint       not null comment '批次号;批量查询批次号',
    upload_file_s3_url varchar(255) not null comment '上传文件S3地址;上传文件S3地址',
    operator           varchar(90)  not null comment '操作人;操作人',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁'
)
    comment 't_unicom_rnr_batch_records';

create table ecp_subscription.t_unicom_rnr_query_records
(
    id                    bigint auto_increment comment '主键'
        primary key,
    batch_no              bigint       not null comment '批次号;批次号',
    car_vin               varchar(90)  not null comment 'VIN;VIN',
    query_status          int          not null comment '查询状态;查询状态 1：查询中 2：查询成功 3：查询失败',
    iccid                 varchar(90)  null comment 'ICCID;VIN对应ICCID',
    real_name_flag        int          null comment '实名状态;实名状态 0:否 1:是',
    card_state            varchar(90)  null comment '卡状态;open：开机 stop：停机 cancel：销户',
    failed_type           int          null comment '查询失败原因;失败原因 1：VIN格式校验错误  2：ICCID查询失败  3：联通查询失败',
    error_desc            varchar(255) null comment '失败原因描述',
    real_name_update_time datetime     null comment '最新实名时间',
    tenant_id             int          not null comment '租户号',
    created_by            varchar(90)  not null comment '创建人',
    created_time          datetime     not null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int          not null comment '乐观锁'
)
    comment 't_unicom_rnr_query_records';

create index t_unicom_rnr_query_records_batch_no_index
    on ecp_subscription.t_unicom_rnr_query_records (batch_no);

create table ecp_subscription.t_unicom_todo_order
(
    id                bigint auto_increment comment '主键'
        primary key,
    iccid             varchar(90) charset utf8mb3  not null comment 'ICCID;ICCID',
    request_type      varchar(90) charset utf8mb3  not null comment '请求类型;order：订购 cancel：退订 change：变更',
    ext_book_id       varchar(90) charset utf8mb3  not null comment '订购ID;订购ID',
    product_id        varchar(255) charset utf8mb3 not null comment '产品ID',
    book_status       varchar(255) charset utf8mb3 not null comment '订购状态',
    active_time       varchar(255) charset utf8mb3 null comment '激活时间;激活时间yyyyMMddHHmmSS',
    expire_time       varchar(255) charset utf8mb3 null comment '到期时间;到期时间yyyyMMddHHmmSS',
    status            int                          not null comment '当前处理状态;状态 0：待处理 1：处理完成 2：处理失败',
    request_count     int                          not null comment '处理次数;处理次数，默认0，大于3次不再处理，日志告警',
    valid_ext_book_id varchar(90) charset utf8mb3  not null comment '待校验订购ID;待校验订购ID',
    valid_book_status varchar(90) charset utf8mb3  not null comment '需要校验的订购状态;active：生效 cancel：作废',
    tenant_id         int                          not null comment '租户号',
    created_by        varchar(90) charset utf8mb3  not null comment '创建人',
    created_time      datetime                     not null comment '创建时间',
    updated_by        varchar(90) charset utf8mb3  null comment '更新人',
    updated_time      datetime                     null comment '更新时间',
    is_deleted        int                          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int                          not null comment '乐观锁'
)
    comment 't_unicom_todo_order' collate = utf8mb4_general_ci;

create table ecp_subscription.t_vcs_fufilment_statistic
(
    id                bigint auto_increment comment '主键'
        primary key,
    incontrol_id      varchar(90) not null comment '用户ICR账号;用户ICR账号',
    series_code       varchar(90) not null comment '车型编码;车型编码',
    series_name       varchar(90) not null comment '车型名称;车型名称',
    brand_code        varchar(90) null comment '品牌code',
    vcs_service_count int         not null comment '订阅服务数量;订阅服务数量',
    tenant_id         int         not null comment '租户号',
    created_by        varchar(90) not null comment '创建人',
    created_time      datetime    not null comment '创建时间',
    updated_by        varchar(90) null comment '更新人',
    updated_time      datetime    null comment '更新时间',
    is_deleted        int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int         not null comment '乐观锁'
)
    comment 't_vcs_fufilment_statistic' charset = utf8mb3
                                        row_format = DYNAMIC;

create index idx_icr
    on ecp_subscription.t_vcs_fufilment_statistic (incontrol_id);

create table ecp_subscription.t_vcs_order_fufilment
(
    id                 bigint auto_increment comment '主键'
        primary key,
    fufilment_id       varchar(90)  not null comment '履约号ID;履约号ID，雪花算法',
    order_code         varchar(90)  not null comment '订单号;订单号',
    vcs_order_code     varchar(90)  not null comment 'VCS订单编码',
    order_item_code    varchar(90)  not null comment '订单item编码;订单item编码',
    car_vin            varchar(90)  not null comment '车辆VIN;车辆VIN',
    service_type       tinyint      not null comment '服务履约类型：1：远程车控Remote Service；2：PIVI Subscription Service；',
    service_begin_date datetime     not null comment '服务起始时间;服务起始时间',
    service_end_date   datetime     not null comment '服务结束时间;服务结束时间',
    service_status     int          not null comment '服务状态;服务状态，1：激活中 2：已激活  3：激活失败',
    order_item_remark  varchar(255) null comment '订单item项备注信息;订单item项备注信息',
    brand_code         varchar(90)  null comment '品牌code',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁',
    constraint t_vcs_order_fufilment_vcs_order_code_IDX
        unique (vcs_order_code, order_item_code),
    constraint unidx_fufilmentid
        unique (fufilment_id)
)
    comment 't_vcs_order_fufilment' charset = utf8mb3
                                    row_format = DYNAMIC;

create index idx_vin
    on ecp_subscription.t_vcs_order_fufilment (car_vin);

create table ecp_subscription.t_vcs_order_fufilment_call
(
    id                    bigint auto_increment comment '主键'
        primary key,
    fufilment_id          varchar(90)  not null comment '履约号ID;履约号ID',
    service_package       varchar(90)  not null comment '服务包编码;服务包编码',
    service_name          varchar(90)  not null comment '服务名;服务名',
    request_param         json         not null comment '请求参数JSON;请求参数JSON',
    request_result        json         not null comment '请求响应JSON;请求响应JSON',
    activation_status     int          not null comment '激活接口调用状态;接口调用状态 0：接口调用失败；1：接口调用成功；',
    activation_failed_msg varchar(900) null comment '接口调用失败MSG;接口调用失败MSG',
    tenant_id             int          not null comment '租户号',
    created_by            varchar(90)  not null comment '创建人',
    created_time          datetime     not null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int          not null comment '乐观锁'
)
    comment 't_vcs_order_fufilment_call' charset = utf8mb3
                                         row_format = DYNAMIC;

create table ecp_subscription.t_vcs_order_fufilment_records
(
    id                bigint auto_increment comment '主键'
        primary key,
    fufilment_id      varchar(90) not null comment '履约号ID;履约号ID',
    service_package   varchar(90) not null comment '服务包编码;服务包编码，ONLINE PACKGEDATA PLANNAVAGATION',
    service_name      varchar(90) not null comment '服务名;服务名',
    activation_status tinyint     not null comment '服务激活状态1：激活中 2：已激活  3：激活失败4：业务激活关闭失败',
    retry_times       tinyint     null comment '重试次数，最多重试3次，达到阈值后activation_status改为4',
    activation_method int         not null comment '激活方式;激活方式 1：接口激活 2：手动激活',
    expire_date       datetime    null comment '服务过期时间;服务过期时间',
    tenant_id         int         not null comment '租户号',
    created_by        varchar(90) not null comment '创建人',
    created_time      datetime    not null comment '创建时间',
    updated_by        varchar(90) null comment '更新人',
    updated_time      datetime    null comment '更新时间',
    is_deleted        int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int         not null comment '乐观锁'
)
    comment 't_vcs_order_fufilment_records' charset = utf8mb3;

create table ecp_order.t_vcs_order_info
(
    id                 bigint auto_increment comment '主键'
        primary key,
    vcs_order_code     varchar(90)  not null comment 'VCS订单编码;VCS订单编码，雪花算法',
    order_code         varchar(90)  not null comment '订单编码;订单编码',
    order_item_code    varchar(90)  not null comment '订单明细编号;订单明细编号，by商品维度记录履约订单',
    consumer_code      varchar(90)  not null comment '用户编码;用户编码',
    incontrol_id       varchar(90)  null comment 'incontrol账号;incontrol账号',
    car_vin            varchar(90)  not null comment '用户车辆车架号;用户车辆车架号',
    series_code        varchar(100) null comment '品牌code',
    series_name        varchar(100) null comment '品牌名',
    service_begin_date datetime     null comment '服务开始时间;服务开始时间，计算得出',
    service_end_date   datetime     null comment '服务结束时间;服务结束时间，计算得出',
    car_vin_md5        varchar(90)  null comment '用户车辆车架号MD5',
    car_vin_view       varchar(90)  null comment '用户车架号明文;用户车架号明文',
    incontrol_id_md5   varchar(90)  null comment 'incontrol账号MD5',
    incontrol_id_mix   varchar(90)  null comment 'incontrol账号半隐藏',
    car_vin_mix        varchar(90)  null comment '用户车辆车架号半隐藏',
    service_type       tinyint      not null comment '服务履约类型：1：远程车控Remote Service；2：PIVI Subscription Service；',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁',
    constraint t_vcs_order_info_order_code_IDX
        unique (order_code, order_item_code),
    constraint t_vcs_order_info_vcs_order_code_IDX
        unique (vcs_order_code)
)
    comment 't_vcs_order_info' row_format = DYNAMIC;

create index t_vcs_order_info_car_vin_md5_IDX
    on ecp_order.t_vcs_order_info (car_vin_md5);

create index t_vcs_order_info_consumer_code_IDX
    on ecp_order.t_vcs_order_info (consumer_code, series_code);

create index t_vcs_order_info_incontrol_id_md5_IDX
    on ecp_order.t_vcs_order_info (incontrol_id_md5);

create table ecp_subscription.t_vcs_order_rollback
(
    id                    bigint auto_increment comment '主键'
        primary key,
    rollback_fufilment_id varchar(90) not null comment '回滚履约ID;回滚履约ID',
    vcs_order_code        varchar(90) not null comment 'VCS订单号',
    fufilment_id          varchar(90) not null comment '履约ID;履约ID',
    refund_order_code     varchar(90) not null comment '退单订单号;退单订单号',
    service_status        int         not null comment '服务状态;服务状态，  1:激活关闭中 2：激活关闭',
    service_end_date      datetime    null comment '服务请求结束时间;服务请求结束时间',
    tenant_id             int         not null comment '租户号',
    created_by            varchar(90) not null comment '创建人',
    created_time          datetime    not null comment '创建时间',
    updated_by            varchar(90) null comment '更新人',
    updated_time          datetime    null comment '更新时间',
    is_deleted            int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int         not null comment '乐观锁',
    constraint t_vcs_order_rollback_UN
        unique (refund_order_code, vcs_order_code),
    constraint uni_rolfulilmentid
        unique (rollback_fufilment_id)
)
    charset = utf8mb3
    row_format = DYNAMIC;

create index idx_fufilmentid
    on ecp_subscription.t_vcs_order_rollback (fufilment_id);

create table ecp_subscription.t_vcs_order_rollback_records
(
    id                    bigint auto_increment comment '主键'
        primary key,
    fufilment_id          varchar(90) null comment '履约ID',
    rollback_fufilment_id varchar(90) not null comment '回滚履约ID;履约号ID',
    service_package       varchar(90) not null comment '服务包编码;服务包编码，ONLINE PACKGEDATA PLANNAVAGATION',
    service_name          varchar(90) not null comment '服务名;服务名',
    activation_status     int         not null comment '激活状态;接口调用状态，  1:激活关闭中 2：激活关闭成功  3：激活关闭失败',
    activation_method     int         not null comment '激活方式;激活方式 1：接口激活',
    expire_date           datetime    null comment '服务过期时间;指定服务回滚时间',
    tenant_id             int         not null comment '租户号',
    created_by            varchar(90) not null comment '创建人',
    created_time          datetime    not null comment '创建时间',
    updated_by            varchar(90) null comment '更新人',
    updated_time          datetime    null comment '更新时间',
    is_deleted            int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int         not null comment '乐观锁'
)
    comment 't_vcs_order_rollback_records';

create table ecp_order.t_vcs_order_statistic
(
    id            bigint auto_increment comment '主键'
        primary key,
    consumer_code varchar(90) not null comment '用户编码;用户编码',
    series_name   varchar(90) not null comment '品牌名;品牌名',
    series_code   varchar(90) not null comment '品牌code;品牌code',
    order_count   int         not null comment '订单数量;订单数量',
    brand_code    varchar(90) null comment '品牌code',
    tenant_id     int         not null comment '租户号',
    created_by    varchar(90) not null comment '创建人',
    created_time  datetime    not null comment '创建时间',
    updated_by    varchar(90) null comment '更新人',
    updated_time  datetime    null comment '更新时间',
    is_deleted    int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int         not null comment '乐观锁'
)
    comment 't_vcs_order_statistic' row_format = DYNAMIC;

create index t_vcs_order_statistic_consumer_code_IDX
    on ecp_order.t_vcs_order_statistic (consumer_code, brand_code, series_code);

create table ecp_subscription.t_vehicle_black_list
(
    id                 bigint auto_increment comment '主键'
        primary key,
    car_vin            varchar(90) not null comment '车辆VIN;车辆VIN',
    amap_activate_flag int         not null comment '高德激活标志位;高德激活标志位, 0:否 1：是'
)
    comment 't_vehicle_black_list' charset = utf8mb3
                                   row_format = DYNAMIC;

create table ecp_subscription.t_vehicle_config_group
(
    id           bigint auto_increment comment '主键'
        primary key,
    config_code  varchar(90) not null comment '配置编码;配置编码',
    group_id     varchar(90) not null comment '分组ID;分组ID
APPD_CANNOT_BUY:不能买APPD的分组',
    tenant_id    int         not null comment '租户号',
    created_by   varchar(90) not null comment '创建人',
    created_time datetime    not null comment '创建时间',
    updated_by   varchar(90) null comment '更新人',
    updated_time datetime    null comment '更新时间',
    is_deleted   int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int         not null comment '乐观锁'
)
    comment 't_vehicle_config_group';

create table ecp_subscription.t_vehicle_dms
(
    id           bigint auto_increment comment '主键'
        primary key,
    car_vin      varchar(255) not null comment '车辆VIN码;车辆VIN码',
    invoice_date varchar(255) not null comment '发票日期;发票日期',
    constraint t_vehicle_dms_carvin_unique
        unique (car_vin)
)
    comment 't_vehicle_dms' charset = utf8mb3;

create table ecp_subscription.t_vehicle_esim
(
    id       bigint auto_increment comment '主键'
        primary key,
    car_vin  varchar(90) not null comment '车辆VIN码;车辆VIN码',
    esimccid varchar(90) not null comment '车机SIM卡ID;车机SIM卡ID'
)
    comment 't_vehicle_esim' charset = utf8mb3;

create table ecp_subscription.t_vehicle_mobile
(
    id            bigint auto_increment comment '主键'
        primary key,
    car_vin       varchar(90) not null comment 'VIN;VIN',
    phone_encrypt varchar(90) not null comment '加密手机号;加密手机号',
    tenant_id     int         not null comment '租户号',
    created_by    varchar(90) not null comment '创建人',
    created_time  datetime    not null comment '创建时间',
    updated_by    varchar(90) null comment '更新人',
    updated_time  datetime    null comment '更新时间',
    is_deleted    int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision      int         not null comment '乐观锁'
)
    charset = utf8mb3
    row_format = DYNAMIC;

create table ecp_product.t_vehicle_model_master_data
(
    id               int auto_increment comment '主键'
        primary key,
    car_system_model varchar(90) not null comment '车机型号;车机型号 PIVI',
    series_code      varchar(90) not null comment '车型编码;车型编码',
    series_name      varchar(90) null comment '车型名称;车型名称',
    config_code      varchar(90) null comment '配置编码;配置编码',
    config_name      varchar(90) null comment '配置名称;配置名称',
    model_year       varchar(90) not null comment '型号年款;型号年款',
    production_en    varchar(90) null comment '产品名称EN;产品名称EN',
    hob_en           varchar(90) null comment 'House of Brand 英文描述描述;House of Brand 英文描述描述',
    brand_code       varchar(90) null comment '品牌code;品牌code',
    brand_name       varchar(90) not null comment '品牌名字;品牌名字',
    tenant_id        int         not null comment '租户号',
    created_by       varchar(90) not null comment '创建人',
    created_time     datetime    not null comment '创建时间',
    updated_by       varchar(90) null comment '更新人',
    updated_time     datetime    null comment '更新时间',
    is_deleted       int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int         not null comment '乐观锁'
)
    comment 't_vehicle_model_master_data' charset = utf8mb3
                                          row_format = DYNAMIC;

create table ecp_subscription.t_vehicle_model_master_data
(
    id               int auto_increment comment '主键'
        primary key,
    car_system_model varchar(90) not null comment '车机型号;车机型号 PIVI',
    series_code      varchar(90) not null comment '车型编码;车型编码',
    series_name      varchar(90) null comment '车型名称;车型名称',
    config_code      varchar(90) null comment '配置编码;配置编码',
    config_name      varchar(90) null comment '配置名称;配置名称',
    model_year       varchar(90) not null comment '型号年款;型号年款',
    production_en    varchar(90) null comment '产品名称EN;产品名称EN',
    hob_en           varchar(90) null comment 'House of Brand 英文描述描述;House of Brand 英文描述描述',
    brand_code       varchar(90) null comment '品牌code;品牌code',
    brand_name       varchar(90) null comment '品牌名字;品牌名字',
    tenant_id        int         not null comment '租户号',
    created_by       varchar(90) not null comment '创建人',
    created_time     datetime    not null comment '创建时间',
    updated_by       varchar(90) null comment '更新人',
    updated_time     datetime    null comment '更新时间',
    is_deleted       int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int         not null comment '乐观锁'
)
    comment 't_vehicle_model_master_data' charset = utf8mb3
                                          row_format = DYNAMIC;

create table ecp_subscription.t_vin_appd_temp
(
    id               bigint auto_increment comment '主键'
        primary key,
    data_id          bigint      not null comment '数据id;雪花算法ID',
    car_vin          varchar(90) not null comment 'VIN;VIN',
    dms_invoice_date datetime    null comment '发票日期',
    vin_match_result int         null comment 'VIN处理结果;VIN处理结果是否入库ECP    0：不需要入库  1：待入库 2：成功入库'
)
    comment 't_vin_appd_temp';

create table ecp_subscription.t_vin_expiry_monthly_detail
(
    id               bigint auto_increment comment '主键'
        primary key,
    job_id           bigint       not null comment '任务ID;雪花算法ID',
    job_date         datetime     not null comment 'job日期;job日期YYYY-MM-DD',
    car_vin          varchar(90)  null comment 'VIN;VIN',
    query_status     int          not null comment '查询状态;查询状态 1：查询中 2：查询成功 3：查询失败',
    iccid            varchar(90)  null comment 'ICCID;VIN对应ICCID',
    real_name_flag   int          null comment '实名状态;实名状态 0:否 1:是',
    card_state       varchar(90)  null comment '卡状态;open：开机 stop：停机 cancel：销户',
    failed_type      int          null comment '查询失败原因;失败原因 1：VIN格式校验错误  2：ICCID查询失败  3：联通查询失败',
    error_desc       varchar(255) null comment '失败原因描述',
    service_type     varchar(90)  not null comment '服务类型;REMOTE、Subscription、BOTH',
    expiry_date      datetime     not null comment '过期时间;过期时间',
    imported_en      varchar(90)  null comment '是否进口;是否进口车，LOCAL、IMPORTED',
    brand            varchar(90)  null comment '车品牌;车品牌',
    series_name      varchar(90)  null comment '车型名称;车型名称',
    tenant_id        int          not null comment '租户号',
    created_by       varchar(90)  not null comment '创建人',
    created_time     datetime     not null comment '创建时间',
    updated_by       varchar(90)  null comment '更新人',
    updated_time     datetime     null comment '更新时间',
    is_deleted       int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision         int          not null comment '乐观锁',
    car_system_model varchar(90)  null comment '车型车机'
)
    comment 't_vin_expiry_monthly_detail';

create index idx_job_tenant_status_expiry
    on ecp_subscription.t_vin_expiry_monthly_detail (job_id, tenant_id, is_deleted, query_status, expiry_date);

create table ecp_subscription.t_vin_expiry_monthly_records
(
    id             bigint auto_increment comment '主键'
        primary key,
    job_id         bigint       not null comment '任务ID;雪花算法ID',
    job_date       datetime     not null comment 'job日期;job日期YYYY-MM-DD',
    job_param      varchar(255) null comment 'JOB指定月份，yyyy-mm',
    begin_date     datetime     not null comment '统计开始时间;统计开始时间',
    end_date       datetime     not null comment '统计结束时间;统计结束时间',
    job_month      varchar(90)  not null comment '统计月份;统计月份',
    job_year       varchar(90)  not null comment '统计年份;统计年份',
    report_s3_file varchar(300) null comment '报告S3文件地址;报告S3文件地址',
    tenant_id      int          not null comment '租户号',
    created_by     varchar(90)  not null comment '创建人',
    created_time   datetime     not null comment '创建时间',
    updated_by     varchar(90)  null comment '更新人',
    updated_time   datetime     null comment '更新时间',
    is_deleted     int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision       int          not null comment '乐观锁',
    constraint uk_year_month
        unique (job_year, job_month)
)
    comment 't_vin_expiry_monthly_records';

create table ecp_subscription.t_vin_expiry_report_data
(
    id           bigint auto_increment comment '主键'
        primary key,
    car_vin      varchar(90) not null comment '车辆VIN;车辆VIN',
    expiry_date  datetime    not null comment '过期日;过期日',
    service_type varchar(90) not null comment '过期服务;PIVIREMOTEBOTH',
    imported_en  varchar(90) not null comment '是否进口车;是否进口车，LOCALIMPORTED',
    brand        varchar(90) not null comment '车品牌;车品牌',
    series_name  varchar(90) not null comment '车型名称;车型名称',
    tenant_id    int         not null comment '租户号',
    created_by   varchar(90) not null comment '创建人',
    created_time datetime    not null comment '创建时间',
    updated_by   varchar(90) null comment '更新人',
    updated_time datetime    null comment '更新时间',
    is_deleted   int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int         not null comment '乐观锁'
)
    comment 't_vin_expiry_report_data';

create table ecp_subscription.t_vin_initial_query
(
    id           bigint auto_increment comment '主键'
        primary key,
    query_no     varchar(90)  not null comment '查询编号;BU+时间戳',
    operator     varchar(90)  not null comment '操作人;操作人账号',
    file_s3_url  varchar(255) not null comment 'S3文件地址;S3文件地址',
    tenant_id    int          not null comment '租户号',
    created_by   varchar(90)  not null comment '创建人',
    created_time datetime     not null comment '创建时间',
    updated_by   varchar(90)  null comment '更新人',
    updated_time datetime     null comment '更新时间',
    is_deleted   int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision     int          not null comment '乐观锁'
)
    comment 't_vin_initial_query';

create table ecp_subscription.t_vin_initial_temp
(
    id                 bigint auto_increment comment '主键'
        primary key,
    data_id            bigint      not null comment '数据id;雪花算法ID',
    car_vin            varchar(90) not null comment 'VIN;VIN',
    dms                int         not null comment '是否有发票日期;0：无 1：有',
    sota_result        int         null comment 'sota查询结果;sota查询结果 1：成功 2：无数据返回 3：ICCID为空',
    appd_result        int         null comment 'appd查询结果;appd查询结果 1：成功 2：系统异常 3：请求异常 4：未找到该VIN 5：到期日不存在 6：JLR Subscription ID不存在；',
    cu_result          int         null comment '联通查询结果;cu查询结果 1：成功 2：系统异常 3：请求异常 4：未找到该VIN的ICCID；',
    amap_result        int         null comment 'amap查询结果;amap查询结果 1：成功 2：系统异常 3：未找到该VIN 4：到期日不存在；',
    dp_result          int         null comment 'DP查询结果;dp查询结果 1：成功 2：系统异常 3：对应的配置信息为空',
    pivi_config_result int         null comment 'PIVI车机匹配结果;PIVI车机匹配结果 1：PIVI 2：接口异常 3：非PIVI车机',
    vin_match_result   int         null comment 'VIN处理结果;VIN处理结果是否入库ECP    0：不需要入库  1：待入库 2：成功入库',
    special_vin_config varchar(90) null comment '特殊车型;特殊车型',
    sync_status        int         not null comment '同步状态;同步过期日期状态 0：未同步 1：同步成功 2：APPD同步失败 3：CU同步失败 4：均同步失败'
)
    comment 't_vin_initial_temp';

create table ecp_subscription.t_vin_service_query_batch_records
(
    id                 bigint auto_increment comment '主键'
        primary key,
    batch_no           bigint       not null comment '批次号;批量查询批次号',
    upload_file_s3_url varchar(255) not null comment '上传文件S3地址;上传文件S3地址',
    operator           varchar(90)  not null comment '操作人;操作人',
    tenant_id          int          not null comment '租户号',
    created_by         varchar(90)  not null comment '创建人',
    created_time       datetime     not null comment '创建时间',
    updated_by         varchar(90)  null comment '更新人',
    updated_time       datetime     null comment '更新时间',
    is_deleted         int          not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision           int          not null comment '乐观锁'
)
    comment 't_vin_service_query_batch_record';

create table ecp_subscription.t_vin_service_query_records
(
    id                  bigint auto_increment comment '主键'
        primary key,
    batch_no            bigint       not null comment '批次号;批次号',
    car_vin             varchar(90)  not null comment 'VIN;VIN',
    query_status        int          not null comment '查询状态;查询状态 1：查询中 2：查询成功 3：查询失败',
    series_name         varchar(90)  not null comment '车辆名称;车辆名称',
    brand_code          varchar(255) null comment '品牌CODE;品牌CODE; LANJAG',
    brand_name          varchar(90)  null comment '品牌名;车型品牌',
    hob_en              varchar(90)  null comment 'House of Brand 英文描述描述;House of Brand 英文描述描述',
    production_en       varchar(90)  null comment '产品类型EN;产品类型EN',
    config_code         varchar(90)  null comment '配置编码;配置编码',
    config_name         varchar(255) null comment '配置名称;配置名称',
    model_year          varchar(90)  not null comment '车辆年款;车辆年款',
    car_system_model    varchar(90)  null comment '车机型号;车机型号，PIVI，通过计算获得',
    remote_service_date datetime     null comment '远程车控服务到期日;远程车控服务到期日',
    online_service_date datetime     null comment '在线服务到期日;在线服务到期日',
    online_pack_date    varchar(90)  null comment '信息娱乐到期日;信息娱乐到期日',
    amap_date           varchar(90)  null comment '实时交通信息到日期;实时交通信息',
    data_plan_date      varchar(90)  null comment '网络流量到日期;网络流量到日期',
    error_desc          varchar(255) null comment '失败原因描述',
    revision            int          not null comment '乐观锁',
    tenant_id           int          not null comment '租户号',
    created_by          varchar(90)  not null comment '创建人',
    created_time        datetime     not null comment '创建时间',
    updated_by          varchar(90)  null comment '更新人',
    updated_time        datetime     null comment '更新时间',
    is_deleted          int          not null comment '是否删除;逻辑删除字段，0：否 1：是'
)
    comment 't_vin_service_query_records';

create table ecp_virtual.t_virtual_coupon_info
(
    id                    bigint auto_increment comment '主键'
        primary key,
    coupon_model_classify int          null comment '卡券模版类型 (枚举类型 1-兑换券;2-代金券;3-折扣券;4-优惠券;5-补签卡)',
    coupon_model_code     varchar(255) null comment '卡券模版 CODE',
    coupon_model_name     varchar(255) null comment '卡券模版名称',
    coupon_create_time    datetime     null comment '卡券模版创建时间',
    coupon_update_time    datetime     null comment '卡券模版更新时间',
    coupon_deliver_type   int          null comment '发卡方式: 1-领取; 2-售卖',
    coupon_deliver_system varchar(50)  null comment '领取/售卖卡券的业务端: EC-EC 商城; CRC-CRC; community-community 社区',
    use_packages          varchar(255) null comment '使用范围文字描述',
    rule_type             int          null comment '满减类型: 1-满金额减; 2-满数量减',
    trigger_money         decimal      null comment '触发满金额和使用现金券减少时的金额要求 (满减券)',
    trigger_num           int          null comment '触发满数量减时的数量要求 (满减券)',
    trigger_amount        decimal      null comment '满减券的优惠金额',
    money                 decimal      null comment '代金券的金额',
    discount_percent      int          null comment '折扣券的折扣率',
    coupon_img_link       varchar(255) null comment '卡券封面链接',
    coupon_deliver_num    int          null comment '卡券库存数量',
    status                int          null comment '上下架状态: 1-上架; 2-下架',
    valid_type            int          null comment '有效方式: 1-固定日期; 2-领取后生效; 3-永不失效',
    valid_start_time      datetime     null comment '有效开始时间',
    valid_end_time        datetime     null comment '有效结束时间',
    valid_days            int          null comment '固定有效时间 (天)',
    coupon_owner_type     int          null comment '卡券归属: 1-JlrId; 2-Vin',
    last_sync_info_id     bigint       null comment '对应的记录同步的信息的 ID',
    tenant_id             int          null comment '租户号',
    created_by            varchar(90)  null comment '创建人',
    created_time          datetime     null comment '创建时间',
    updated_by            varchar(90)  null comment '更新人',
    updated_time          datetime     null comment '更新时间',
    is_deleted            int          null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision              int          null comment '乐观锁',
    constraint idx_coupon_model_code
        unique (coupon_model_code) comment 'CouponModelCode 唯一索引'
)
    comment '卡券信息表';

create table ecp_virtual.t_virtual_coupon_sync_info
(
    id                bigint auto_increment comment '主键 ID'
        primary key,
    sync_begin_page   int         null comment '同步起始页数',
    sync_begin_time   datetime    null comment '同步起始时间',
    current_sync_page int         null comment '当前同步页数',
    sync_end_time     datetime    null comment '同步结束时间',
    sync_status       int         not null comment '同步状态: 1-同步中;2-同步结束;3-同步异常;4-手动终止',
    tenant_id         int         not null comment '租户号',
    created_by        varchar(90) not null comment '创建人',
    created_time      datetime    not null comment '创建时间',
    updated_by        varchar(90) null comment '更新人',
    updated_time      datetime    null comment '更新时间',
    is_deleted        int         not null comment '是否删除;逻辑删除字段，0：否 1：是',
    revision          int         not null comment '乐观锁'
);

create table ecp_xxl_job.xxl_job_group
(
    id           int auto_increment
        primary key,
    app_name     varchar(64)       not null comment '执行器AppName',
    title        varchar(12)       not null comment '执行器名称',
    address_type tinyint default 0 not null comment '执行器地址类型：0=自动注册、1=手动录入',
    address_list text              null comment '执行器地址列表，多地址逗号分隔',
    update_time  datetime          null
)
    charset = utf8mb4;

create table ecp_xxl_job.xxl_job_info
(
    id                        int auto_increment
        primary key,
    job_group                 int                              not null comment '执行器主键ID',
    job_desc                  varchar(255)                     not null,
    add_time                  datetime                         null,
    update_time               datetime                         null,
    author                    varchar(64)                      null comment '作者',
    alarm_email               varchar(255)                     null comment '报警邮件',
    schedule_type             varchar(50) default 'NONE'       not null comment '调度类型',
    schedule_conf             varchar(128)                     null comment '调度配置，值含义取决于调度类型',
    misfire_strategy          varchar(50) default 'DO_NOTHING' not null comment '调度过期策略',
    executor_route_strategy   varchar(50)                      null comment '执行器路由策略',
    executor_handler          varchar(255)                     null comment '执行器任务handler',
    executor_param            varchar(512)                     null comment '执行器任务参数',
    executor_block_strategy   varchar(50)                      null comment '阻塞处理策略',
    executor_timeout          int         default 0            not null comment '任务执行超时时间，单位秒',
    executor_fail_retry_count int         default 0            not null comment '失败重试次数',
    glue_type                 varchar(50)                      not null comment 'GLUE类型',
    glue_source               mediumtext                       null comment 'GLUE源代码',
    glue_remark               varchar(128)                     null comment 'GLUE备注',
    glue_updatetime           datetime                         null comment 'GLUE更新时间',
    child_jobid               varchar(255)                     null comment '子任务ID，多个逗号分隔',
    trigger_status            tinyint     default 0            not null comment '调度状态：0-停止，1-运行',
    trigger_last_time         bigint      default 0            not null comment '上次调度时间',
    trigger_next_time         bigint      default 0            not null comment '下次调度时间'
)
    charset = utf8mb4;

create table ecp_xxl_job.xxl_job_lock
(
    lock_name varchar(50) not null comment '锁名称'
        primary key
)
    charset = utf8mb4;

create table ecp_xxl_job.xxl_job_log
(
    id                        bigint auto_increment
        primary key,
    job_group                 int               not null comment '执行器主键ID',
    job_id                    int               not null comment '任务，主键ID',
    executor_address          varchar(255)      null comment '执行器地址，本次执行的地址',
    executor_handler          varchar(255)      null comment '执行器任务handler',
    executor_param            varchar(512)      null comment '执行器任务参数',
    executor_sharding_param   varchar(20)       null comment '执行器任务分片参数，格式如 1/2',
    executor_fail_retry_count int     default 0 not null comment '失败重试次数',
    trigger_time              datetime          null comment '调度-时间',
    trigger_code              int               not null comment '调度-结果',
    trigger_msg               text              null comment '调度-日志',
    handle_time               datetime          null comment '执行-时间',
    handle_code               int               not null comment '执行-状态',
    handle_msg                text              null comment '执行-日志',
    alarm_status              tinyint default 0 not null comment '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败'
)
    charset = utf8mb4;

create index I_handle_code
    on ecp_xxl_job.xxl_job_log (handle_code);

create index I_trigger_time
    on ecp_xxl_job.xxl_job_log (trigger_time);

create table ecp_xxl_job.xxl_job_log_report
(
    id            int auto_increment
        primary key,
    trigger_day   datetime      null comment '调度-时间',
    running_count int default 0 not null comment '运行中-日志数量',
    suc_count     int default 0 not null comment '执行成功-日志数量',
    fail_count    int default 0 not null comment '执行失败-日志数量',
    update_time   datetime      null,
    constraint i_trigger_day
        unique (trigger_day)
)
    charset = utf8mb4;

create table ecp_xxl_job.xxl_job_logglue
(
    id          int auto_increment
        primary key,
    job_id      int          not null comment '任务，主键ID',
    glue_type   varchar(50)  null comment 'GLUE类型',
    glue_source mediumtext   null comment 'GLUE源代码',
    glue_remark varchar(128) not null comment 'GLUE备注',
    add_time    datetime     null,
    update_time datetime     null
)
    charset = utf8mb4;

create table ecp_xxl_job.xxl_job_registry
(
    id             int auto_increment
        primary key,
    registry_group varchar(50)  not null,
    registry_key   varchar(255) not null,
    registry_value varchar(255) not null,
    update_time    datetime     null
)
    charset = utf8mb4;

create index i_g_k_v
    on ecp_xxl_job.xxl_job_registry (registry_group, registry_key, registry_value);

create table ecp_xxl_job.xxl_job_user
(
    id         int auto_increment
        primary key,
    username   varchar(50)  not null comment '账号',
    password   varchar(50)  not null comment '密码',
    role       tinyint      not null comment '角色：0-普通用户、1-管理员',
    permission varchar(255) null comment '权限：执行器ID列表，多个逗号分割',
    constraint i_username
        unique (username)
)
    charset = utf8mb4;

create
definer = root@localhost procedure ecp_notification.InsertNotificationHistoryData()
BEGIN
  DECLARE i INT DEFAULT 1;
  DECLARE max_rows INT DEFAULT 100;

  WHILE i <= max_rows DO
    INSERT INTO t_notification_history (
      task_code,
      task_send_time,
      send_total_count,
      send_success_count,
      send_fail_count,
      tenant_id,
      created_by,
      created_time,
      updated_by,
      updated_time,
      is_deleted,
      revision
    ) VALUES (
      CONCAT('task_code_', i), -- 假设生成任务编码的方式
      NOW(), -- 或者指定一个定时发送时间CRON表达式对应的日期时间
      FLOOR(RAND()*(100 - 1) + 1), -- 随机生成总推送条数
      FLOOR(RAND()*(send_total_count/2)), -- 根据总推送条数随机生成成功条数，这里假设最多一半为成功
      send_total_count - send_success_count, -- 失败条数等于总条数减去成功条数
      1, -- 所有记录的tenant_id都为1
      'admin', -- 创建人
      NOW(), -- 创建时间
      'admin', -- 更新人（这里为了简化都设为admin，实际可以动态生成）
      NOW(), -- 更新时间
      0, -- 默认未删除
      1 -- 初始化乐观锁版本号
    );

    SET i = i + 1;
END WHILE;
END;

create
definer = root@localhost procedure ecp_order.InsertOrderInfo(IN start_order_code varchar(90), IN insert_count int)
BEGIN
	DECLARE current_order_code VARCHAR(90);
    DECLARE base_prefix VARCHAR(50);
    DECLARE numeric_part BIGINT;
    DECLARE i INT DEFAULT 1;

    -- 提取前缀和补全数字部分
    SET base_prefix = LEFT(start_order_code, LENGTH(start_order_code) - 6); -- 前缀部分
    SET numeric_part = CAST(RIGHT(start_order_code, 6) AS UNSIGNED); -- 补全数字部分

    -- 循环插入数据
    WHILE i <= insert_count DO
        -- 拼接新的 order_code
        SET current_order_code = CONCAT(base_prefix, LPAD(numeric_part, 6, '0'));

        -- 插入数据
INSERT INTO ecp_order.t_order_info (
    consumer_code, order_code, original_fee_total_amount, fee_total_amount, cost_amount,
    discount_total_amount, exclude_tax_amount, tax_amount, freight_amount, parent_order_code,
    order_status, payment_status, payment_time, order_time, order_channel, order_type,
    wx_nick_name, wx_phone, wx_phone_mix, wx_phone_md5, customer_remark, contact_phone,
    contact_phone_mix, contact_phone_md5, operator_remark, brand_code, business_code,
    refund_status, order_close_reason, gift_address, tenant_id, created_by, created_time,
    updated_by, updated_time, is_deleted, revision, completed_time, point_amount,
    independent_status, closed_time, logistics_status, coupon_status
) VALUES (
             '435266652280590336', current_order_code, 10, 10, 10, 0, 10, 0, 0, current_order_code,
             4, 0, NULL, '2025-03-21 02:31:52', 2, 1, '', 'KrT5PMruOi6cJ7jhAqkYew==:CAb8PlT9f3PRp6LtgoJYJA==',
             '173****4044', '2b36600d595368e830c8eb7e8708a17b', '', 'xInSbVUoDf2bMgt5gFwETQ==:8vKQvn/oYzmaPyq34y8t+A==',
             '173****4044', '2b36600d595368e830c8eb7e8708a17b', '', '', 'BUSINESS:001', 0, '', 0, 1,
             'system', '2025-03-21 02:31:52', '', '2025-03-21 02:46:52', 0, 2, NULL, NULL, 0,
             '2025-03-21 02:46:52', NULL, NULL
         );

-- 更新补全数字部分
SET numeric_part = numeric_part + 1;
        SET i = i + 1;
END WHILE;
END;

