2025-07-27 01:07:06,470 INFO [capacityManagement] start correct usage

2025-07-27 01:07:06,478 INFO [capacityManagement] end correct usage, cost: 9.28E-4s

2025-07-27 01:07:07,864 WARN clearHistoryConfig get scheduled

2025-07-27 01:07:07,868 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-27 01:07:07,869 WARN clearConfigHistory, getBeforeStamp:2025-06-27 01:07:07.0, pageSize:1000

2025-07-27 01:07:07,872 WARN history config cleaner successfully

2025-07-27 04:40:40,168 INFO [capacityManagement] start correct usage

2025-07-27 04:40:40,176 INFO [capacityManagement] end correct usage, cost: 0.001858333s

2025-07-27 04:40:41,563 WARN clearHistoryConfig get scheduled

2025-07-27 04:40:41,564 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-27 04:40:41,565 WARN clearConfigHistory, getBeforeStamp:2025-06-27 04:40:41.0, pageSize:1000

2025-07-27 04:40:41,570 WARN history config cleaner successfully

2025-07-27 07:53:00,682 INFO [capacityManagement] start correct usage

2025-07-27 07:53:00,689 INFO [capacityManagement] end correct usage, cost: 0.003880708s

2025-07-27 07:53:02,076 WARN clearHistoryConfig get scheduled

2025-07-27 07:53:02,077 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-27 07:53:02,079 WARN clearConfigHistory, getBeforeStamp:2025-06-27 07:53:02.0, pageSize:1000

2025-07-27 07:53:02,086 WARN history config cleaner successfully

2025-07-27 11:05:18,162 INFO [capacityManagement] start correct usage

2025-07-27 11:05:18,168 INFO [capacityManagement] end correct usage, cost: 0.003153583s

2025-07-27 11:05:19,561 WARN clearHistoryConfig get scheduled

2025-07-27 11:05:19,562 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-27 11:05:19,564 WARN clearConfigHistory, getBeforeStamp:2025-06-27 11:05:19.0, pageSize:1000

2025-07-27 11:05:19,570 WARN history config cleaner successfully

2025-07-27 14:32:24,926 INFO [capacityManagement] start correct usage

2025-07-27 14:32:24,931 INFO [capacityManagement] end correct usage, cost: 0.004311125s

2025-07-27 14:32:26,327 WARN clearHistoryConfig get scheduled

2025-07-27 14:32:26,328 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-27 14:32:26,330 WARN clearConfigHistory, getBeforeStamp:2025-06-27 14:32:26.0, pageSize:1000

2025-07-27 14:32:26,335 WARN history config cleaner successfully

2025-07-27 17:57:37,386 INFO [capacityManagement] start correct usage

2025-07-27 17:57:37,392 INFO [capacityManagement] end correct usage, cost: 0.002989875s

2025-07-27 17:57:38,788 WARN clearHistoryConfig get scheduled

2025-07-27 17:57:38,789 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-27 17:57:38,791 WARN clearConfigHistory, getBeforeStamp:2025-06-27 17:57:38.0, pageSize:1000

2025-07-27 17:57:38,795 WARN history config cleaner successfully

2025-07-27 21:16:35,810 INFO [capacityManagement] start correct usage

2025-07-27 21:16:35,814 INFO [capacityManagement] end correct usage, cost: 0.00209775s

2025-07-27 21:16:37,215 WARN clearHistoryConfig get scheduled

2025-07-27 21:16:37,216 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-27 21:16:37,218 WARN clearConfigHistory, getBeforeStamp:2025-06-27 21:16:37.0, pageSize:1000

2025-07-27 21:16:37,223 WARN history config cleaner successfully

