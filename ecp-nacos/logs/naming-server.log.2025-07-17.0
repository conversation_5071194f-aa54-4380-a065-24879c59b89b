2025-07-17 15:32:49,285 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.CmdbSelectorContextBuilder) contextType(CMDB) successfully.

2025-07-17 15:32:49,285 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.NoneSelectorContextBuilder) contextType(NONE) successfully.

2025-07-17 15:32:49,285 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.LabelSelector) type(label) contextType(CMDB) successfully.

2025-07-17 15:32:49,285 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.NoneSelector) type(none) contextType(NONE) successfully.

2025-07-17 15:32:50,679 INFO Load instance extension handler []

2025-07-17 15:36:31,375 INFO Client connection 1752737791167_192.168.65.1_55229 connect

2025-07-17 15:36:31,746 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=0}, 1752737791167_192.168.65.1_55229

2025-07-17 15:36:35,242 INFO Client connection 1752737791167_192.168.65.1_55229 disconnect, remove instances and subscribers

2025-07-17 15:36:44,014 INFO Client connection 1752737803978_192.168.65.1_18790 connect

2025-07-17 15:36:44,332 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=2}, 1752737803978_192.168.65.1_18790

2025-07-17 15:43:09,962 INFO Client connection 1752738189957_192.168.65.1_44180 connect

2025-07-17 15:43:10,074 INFO Client connection 1752737803978_192.168.65.1_18790 disconnect, remove instances and subscribers

2025-07-17 15:43:12,952 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=4}, 1752738189957_192.168.65.1_44180

2025-07-17 15:43:19,212 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=5}, 1752738189957_192.168.65.1_44180

2025-07-17 15:43:19,532 INFO Client connection 1752738189957_192.168.65.1_44180 disconnect, remove instances and subscribers

2025-07-17 15:43:27,810 INFO Client connection 1752738207774_192.168.65.1_48911 connect

2025-07-17 15:43:28,138 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=6}, 1752738207774_192.168.65.1_48911

2025-07-17 15:44:25,972 INFO Client connection 1752738207774_192.168.65.1_48911 disconnect, remove instances and subscribers

2025-07-17 15:44:36,366 INFO Client connection 1752738276354_192.168.65.1_27159 connect

2025-07-17 15:44:38,069 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=8}, 1752738276354_192.168.65.1_27159

2025-07-17 15:47:03,996 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=9}, 1752738276354_192.168.65.1_27159

2025-07-17 15:47:04,325 INFO Client connection 1752738276354_192.168.65.1_27159 disconnect, remove instances and subscribers

2025-07-17 15:47:12,824 INFO Client connection 1752738432788_192.168.65.1_18175 connect

2025-07-17 15:47:13,146 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=10}, 1752738432788_192.168.65.1_18175

2025-07-17 15:47:34,614 INFO Client connection 1752738454606_192.168.65.1_40963 connect

2025-07-17 15:47:43,068 INFO Client connection 1752738432788_192.168.65.1_18175 disconnect, remove instances and subscribers

2025-07-17 15:47:50,190 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=12}, 1752738454606_192.168.65.1_40963

2025-07-17 15:48:16,172 INFO Client connection 1752738454606_192.168.65.1_40963 disconnect, remove instances and subscribers

2025-07-17 15:48:26,129 INFO Client connection 1752738506120_192.168.65.1_59559 connect

2025-07-17 15:48:29,127 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=14}, 1752738506120_192.168.65.1_59559

2025-07-17 15:49:41,473 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=15}, 1752738506120_192.168.65.1_59559

2025-07-17 15:49:41,801 INFO Client connection 1752738506120_192.168.65.1_59559 disconnect, remove instances and subscribers

2025-07-17 15:51:19,878 WARN namespace : public, [DEFAULT_GROUP@@ecp-system-service] services are automatically cleaned

2025-07-17 16:03:49,430 INFO Client connection 1752739429391_192.168.65.1_45462 connect

2025-07-17 16:03:49,877 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=0}, 1752739429391_192.168.65.1_45462

2025-07-17 16:08:37,650 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=1}, 1752739429391_192.168.65.1_45462

2025-07-17 16:08:37,984 INFO Client connection 1752739429391_192.168.65.1_45462 disconnect, remove instances and subscribers

2025-07-17 16:10:19,873 WARN namespace : public, [DEFAULT_GROUP@@ecp-system-service] services are automatically cleaned

2025-07-17 16:10:25,900 INFO Client connection 1752739825862_192.168.65.1_37683 connect

2025-07-17 16:10:26,341 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=0}, 1752739825862_192.168.65.1_37683

2025-07-17 16:10:53,615 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='ecp-system-service', ephemeral=true, revision=1}, 1752739825862_192.168.65.1_37683

2025-07-17 16:10:53,943 INFO Client connection 1752739825862_192.168.65.1_37683 disconnect, remove instances and subscribers

2025-07-17 16:12:19,875 WARN namespace : public, [DEFAULT_GROUP@@ecp-system-service] services are automatically cleaned

