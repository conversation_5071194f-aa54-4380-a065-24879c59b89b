2025-07-25 04:41:05,526 INFO [capacityManagement] start correct usage

2025-07-25 04:41:05,530 INFO [capacityManagement] end correct usage, cost: 9.7275E-4s

2025-07-25 04:41:06,645 WARN clearHistoryConfig get scheduled

2025-07-25 04:41:06,647 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 04:41:06,648 WARN clearConfigHistory, getBeforeStamp:2025-06-25 04:41:06.0, pageSize:1000

2025-07-25 04:41:06,654 WARN history config cleaner successfully

2025-07-25 08:30:23,928 INFO [capacityManagement] start correct usage

2025-07-25 08:30:23,932 INFO [capacityManagement] end correct usage, cost: 0.001506625s

2025-07-25 08:30:25,059 WARN clearHistoryConfig get scheduled

2025-07-25 08:30:25,060 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 08:30:25,062 WARN clearConfigHistory, getBeforeStamp:2025-06-25 08:30:25.0, pageSize:1000

2025-07-25 08:30:25,065 WARN history config cleaner successfully

2025-07-25 11:33:19,333 INFO [capacityManagement] start correct usage

2025-07-25 11:33:19,338 INFO [capacityManagement] end correct usage, cost: 0.002972083s

2025-07-25 11:33:20,465 WARN clearHistoryConfig get scheduled

2025-07-25 11:33:20,467 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 11:33:20,469 WARN clearConfigHistory, getBeforeStamp:2025-06-25 11:33:20.0, pageSize:1000

2025-07-25 11:33:20,476 WARN history config cleaner successfully

2025-07-25 12:37:27,738 INFO [capacityManagement] start correct usage

2025-07-25 12:37:27,744 INFO [capacityManagement] end correct usage, cost: 0.002374792s

2025-07-25 12:37:28,876 WARN clearHistoryConfig get scheduled

2025-07-25 12:37:28,876 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 12:37:28,878 WARN clearConfigHistory, getBeforeStamp:2025-06-25 12:37:28.0, pageSize:1000

2025-07-25 12:37:28,884 WARN history config cleaner successfully

2025-07-25 12:47:27,750 INFO [capacityManagement] start correct usage

2025-07-25 12:47:27,754 INFO [capacityManagement] end correct usage, cost: 0.001903333s

2025-07-25 12:47:28,890 WARN clearHistoryConfig get scheduled

2025-07-25 12:47:28,890 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 12:47:28,892 WARN clearConfigHistory, getBeforeStamp:2025-06-25 12:47:28.0, pageSize:1000

2025-07-25 12:47:28,895 WARN history config cleaner successfully

2025-07-25 12:57:27,760 INFO [capacityManagement] start correct usage

2025-07-25 12:57:27,765 INFO [capacityManagement] end correct usage, cost: 0.002265708s

2025-07-25 12:57:28,901 WARN clearHistoryConfig get scheduled

2025-07-25 12:57:28,902 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 12:57:28,904 WARN clearConfigHistory, getBeforeStamp:2025-06-25 12:57:28.0, pageSize:1000

2025-07-25 12:57:28,909 WARN history config cleaner successfully

2025-07-25 13:07:27,771 INFO [capacityManagement] start correct usage

2025-07-25 13:07:27,774 INFO [capacityManagement] end correct usage, cost: 0.001720125s

2025-07-25 13:07:28,916 WARN clearHistoryConfig get scheduled

2025-07-25 13:07:28,916 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 13:07:28,917 WARN clearConfigHistory, getBeforeStamp:2025-06-25 13:07:28.0, pageSize:1000

2025-07-25 13:07:28,921 WARN history config cleaner successfully

2025-07-25 13:17:27,779 INFO [capacityManagement] start correct usage

2025-07-25 13:17:27,784 INFO [capacityManagement] end correct usage, cost: 0.001957458s

2025-07-25 13:17:28,927 WARN clearHistoryConfig get scheduled

2025-07-25 13:17:28,928 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 13:17:28,930 WARN clearConfigHistory, getBeforeStamp:2025-06-25 13:17:28.0, pageSize:1000

2025-07-25 13:17:28,935 WARN history config cleaner successfully

2025-07-25 13:43:29,005 INFO [capacityManagement] start correct usage

2025-07-25 13:43:29,009 INFO [capacityManagement] end correct usage, cost: 0.002169708s

2025-07-25 13:43:30,162 WARN clearHistoryConfig get scheduled

2025-07-25 13:43:30,163 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 13:43:30,164 WARN clearConfigHistory, getBeforeStamp:2025-06-25 13:43:30.0, pageSize:1000

2025-07-25 13:43:30,169 WARN history config cleaner successfully

2025-07-25 13:53:29,015 INFO [capacityManagement] start correct usage

2025-07-25 13:53:29,020 INFO [capacityManagement] end correct usage, cost: 0.002142875s

2025-07-25 13:53:30,175 WARN clearHistoryConfig get scheduled

2025-07-25 13:53:30,176 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 13:53:30,177 WARN clearConfigHistory, getBeforeStamp:2025-06-25 13:53:30.0, pageSize:1000

2025-07-25 13:53:30,181 WARN history config cleaner successfully

2025-07-25 14:03:29,026 INFO [capacityManagement] start correct usage

2025-07-25 14:03:29,031 INFO [capacityManagement] end correct usage, cost: 0.002128875s

2025-07-25 14:03:30,186 WARN clearHistoryConfig get scheduled

2025-07-25 14:03:30,186 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 14:03:30,187 WARN clearConfigHistory, getBeforeStamp:2025-06-25 14:03:30.0, pageSize:1000

2025-07-25 14:03:30,204 WARN history config cleaner successfully

2025-07-25 14:13:29,037 INFO [capacityManagement] start correct usage

2025-07-25 14:13:29,042 INFO [capacityManagement] end correct usage, cost: 0.002635917s

2025-07-25 14:13:30,213 WARN clearHistoryConfig get scheduled

2025-07-25 14:13:30,214 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 14:13:30,216 WARN clearConfigHistory, getBeforeStamp:2025-06-25 14:13:30.0, pageSize:1000

2025-07-25 14:13:30,222 WARN history config cleaner successfully

2025-07-25 14:23:29,048 INFO [capacityManagement] start correct usage

2025-07-25 14:23:29,053 INFO [capacityManagement] end correct usage, cost: 0.002287542s

2025-07-25 14:23:30,227 WARN clearHistoryConfig get scheduled

2025-07-25 14:23:30,228 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 14:23:30,230 WARN clearConfigHistory, getBeforeStamp:2025-06-25 14:23:30.0, pageSize:1000

2025-07-25 14:23:30,241 WARN history config cleaner successfully

2025-07-25 14:33:29,058 INFO [capacityManagement] start correct usage

2025-07-25 14:33:29,064 INFO [capacityManagement] end correct usage, cost: 0.002258584s

2025-07-25 14:33:30,247 WARN clearHistoryConfig get scheduled

2025-07-25 14:33:30,248 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 14:33:30,249 WARN clearConfigHistory, getBeforeStamp:2025-06-25 14:33:30.0, pageSize:1000

2025-07-25 14:33:30,255 WARN history config cleaner successfully

2025-07-25 14:43:29,068 INFO [capacityManagement] start correct usage

2025-07-25 14:43:29,074 INFO [capacityManagement] end correct usage, cost: 0.002884666s

2025-07-25 14:43:30,260 WARN clearHistoryConfig get scheduled

2025-07-25 14:43:30,261 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 14:43:30,263 WARN clearConfigHistory, getBeforeStamp:2025-06-25 14:43:30.0, pageSize:1000

2025-07-25 14:43:30,266 WARN history config cleaner successfully

2025-07-25 14:53:29,049 INFO [capacityManagement] start correct usage

2025-07-25 14:53:29,054 INFO [capacityManagement] end correct usage, cost: 0.002505833s

2025-07-25 14:53:30,243 WARN clearHistoryConfig get scheduled

2025-07-25 14:53:30,244 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 14:53:30,245 WARN clearConfigHistory, getBeforeStamp:2025-06-25 14:53:30.0, pageSize:1000

2025-07-25 14:53:30,251 WARN history config cleaner successfully

2025-07-25 15:03:29,058 INFO [capacityManagement] start correct usage

2025-07-25 15:03:29,061 INFO [capacityManagement] end correct usage, cost: 0.001030958s

2025-07-25 15:03:30,256 WARN clearHistoryConfig get scheduled

2025-07-25 15:03:30,258 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 15:03:30,259 WARN clearConfigHistory, getBeforeStamp:2025-06-25 15:03:30.0, pageSize:1000

2025-07-25 15:03:30,265 WARN history config cleaner successfully

2025-07-25 15:13:29,064 INFO [capacityManagement] start correct usage

2025-07-25 15:13:29,069 INFO [capacityManagement] end correct usage, cost: 0.003038291s

2025-07-25 15:13:30,267 WARN clearHistoryConfig get scheduled

2025-07-25 15:13:30,268 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 15:13:30,268 WARN clearConfigHistory, getBeforeStamp:2025-06-25 15:13:30.0, pageSize:1000

2025-07-25 15:13:30,274 WARN history config cleaner successfully

2025-07-25 15:23:29,072 INFO [capacityManagement] start correct usage

2025-07-25 15:23:29,081 INFO [capacityManagement] end correct usage, cost: 0.005223708s

2025-07-25 15:23:30,277 WARN clearHistoryConfig get scheduled

2025-07-25 15:23:30,278 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 15:23:30,279 WARN clearConfigHistory, getBeforeStamp:2025-06-25 15:23:30.0, pageSize:1000

2025-07-25 15:23:30,293 WARN history config cleaner successfully

2025-07-25 15:33:29,085 INFO [capacityManagement] start correct usage

2025-07-25 15:33:29,090 INFO [capacityManagement] end correct usage, cost: 0.002378166s

2025-07-25 15:33:30,300 WARN clearHistoryConfig get scheduled

2025-07-25 15:33:30,301 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 15:33:30,302 WARN clearConfigHistory, getBeforeStamp:2025-06-25 15:33:30.0, pageSize:1000

2025-07-25 15:33:30,307 WARN history config cleaner successfully

2025-07-25 16:00:43,597 INFO [capacityManagement] start correct usage

2025-07-25 16:00:43,600 INFO [capacityManagement] end correct usage, cost: 0.001853417s

2025-07-25 16:00:44,814 WARN clearHistoryConfig get scheduled

2025-07-25 16:00:44,815 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 16:00:44,816 WARN clearConfigHistory, getBeforeStamp:2025-06-25 16:00:44.0, pageSize:1000

2025-07-25 16:00:44,825 WARN history config cleaner successfully

2025-07-25 16:10:43,603 INFO [capacityManagement] start correct usage

2025-07-25 16:10:43,606 INFO [capacityManagement] end correct usage, cost: 0.001812125s

2025-07-25 16:10:44,829 WARN clearHistoryConfig get scheduled

2025-07-25 16:10:44,829 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 16:10:44,831 WARN clearConfigHistory, getBeforeStamp:2025-06-25 16:10:44.0, pageSize:1000

2025-07-25 16:10:44,853 WARN history config cleaner successfully

2025-07-25 16:20:43,610 INFO [capacityManagement] start correct usage

2025-07-25 16:20:43,615 INFO [capacityManagement] end correct usage, cost: 0.002838042s

2025-07-25 16:20:44,858 WARN clearHistoryConfig get scheduled

2025-07-25 16:20:44,859 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 16:20:44,860 WARN clearConfigHistory, getBeforeStamp:2025-06-25 16:20:44.0, pageSize:1000

2025-07-25 16:20:44,866 WARN history config cleaner successfully

2025-07-25 16:30:43,618 INFO [capacityManagement] start correct usage

2025-07-25 16:30:43,623 INFO [capacityManagement] end correct usage, cost: 0.002417958s

2025-07-25 16:30:44,869 WARN clearHistoryConfig get scheduled

2025-07-25 16:30:44,870 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 16:30:44,872 WARN clearConfigHistory, getBeforeStamp:2025-06-25 16:30:44.0, pageSize:1000

2025-07-25 16:30:44,879 WARN history config cleaner successfully

2025-07-25 16:40:43,626 INFO [capacityManagement] start correct usage

2025-07-25 16:40:43,630 INFO [capacityManagement] end correct usage, cost: 0.001095416s

2025-07-25 16:40:44,883 WARN clearHistoryConfig get scheduled

2025-07-25 16:40:44,884 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 16:40:44,885 WARN clearConfigHistory, getBeforeStamp:2025-06-25 16:40:44.0, pageSize:1000

2025-07-25 16:40:44,891 WARN history config cleaner successfully

2025-07-25 16:50:43,634 INFO [capacityManagement] start correct usage

2025-07-25 16:50:43,640 INFO [capacityManagement] end correct usage, cost: 0.002905709s

2025-07-25 16:50:44,900 WARN clearHistoryConfig get scheduled

2025-07-25 16:50:44,901 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 16:50:44,903 WARN clearConfigHistory, getBeforeStamp:2025-06-25 16:50:44.0, pageSize:1000

2025-07-25 16:50:44,908 WARN history config cleaner successfully

2025-07-25 17:00:43,644 INFO [capacityManagement] start correct usage

2025-07-25 17:00:43,649 INFO [capacityManagement] end correct usage, cost: 0.002125167s

2025-07-25 17:00:44,912 WARN clearHistoryConfig get scheduled

2025-07-25 17:00:44,913 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 17:00:44,915 WARN clearConfigHistory, getBeforeStamp:2025-06-25 17:00:44.0, pageSize:1000

2025-07-25 17:00:44,921 WARN history config cleaner successfully

2025-07-25 17:10:43,653 INFO [capacityManagement] start correct usage

2025-07-25 17:10:43,658 INFO [capacityManagement] end correct usage, cost: 0.002728334s

2025-07-25 17:10:44,926 WARN clearHistoryConfig get scheduled

2025-07-25 17:10:44,927 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 17:10:44,928 WARN clearConfigHistory, getBeforeStamp:2025-06-25 17:10:44.0, pageSize:1000

2025-07-25 17:10:44,932 WARN history config cleaner successfully

2025-07-25 17:20:43,662 INFO [capacityManagement] start correct usage

2025-07-25 17:20:43,666 INFO [capacityManagement] end correct usage, cost: 0.00174525s

2025-07-25 17:20:44,939 WARN clearHistoryConfig get scheduled

2025-07-25 17:20:44,940 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 17:20:44,941 WARN clearConfigHistory, getBeforeStamp:2025-06-25 17:20:44.0, pageSize:1000

2025-07-25 17:20:44,948 WARN history config cleaner successfully

2025-07-25 17:30:43,669 INFO [capacityManagement] start correct usage

2025-07-25 17:30:43,675 INFO [capacityManagement] end correct usage, cost: 0.002604083s

2025-07-25 17:30:44,950 WARN clearHistoryConfig get scheduled

2025-07-25 17:30:44,951 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 17:30:44,952 WARN clearConfigHistory, getBeforeStamp:2025-06-25 17:30:44.0, pageSize:1000

2025-07-25 17:30:44,958 WARN history config cleaner successfully

2025-07-25 17:40:43,679 INFO [capacityManagement] start correct usage

2025-07-25 17:40:43,684 INFO [capacityManagement] end correct usage, cost: 0.002164459s

2025-07-25 17:40:44,964 WARN clearHistoryConfig get scheduled

2025-07-25 17:40:44,966 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 17:40:44,967 WARN clearConfigHistory, getBeforeStamp:2025-06-25 17:40:44.0, pageSize:1000

2025-07-25 17:40:44,978 WARN history config cleaner successfully

2025-07-25 17:50:43,688 INFO [capacityManagement] start correct usage

2025-07-25 17:50:43,691 INFO [capacityManagement] end correct usage, cost: 0.002246542s

2025-07-25 17:50:44,985 WARN clearHistoryConfig get scheduled

2025-07-25 17:50:44,986 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 17:50:44,987 WARN clearConfigHistory, getBeforeStamp:2025-06-25 17:50:44.0, pageSize:1000

2025-07-25 17:50:44,995 WARN history config cleaner successfully

2025-07-25 18:00:43,695 INFO [capacityManagement] start correct usage

2025-07-25 18:00:43,699 INFO [capacityManagement] end correct usage, cost: 0.001570166s

2025-07-25 18:00:45,001 WARN clearHistoryConfig get scheduled

2025-07-25 18:00:45,002 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 18:00:45,004 WARN clearConfigHistory, getBeforeStamp:2025-06-25 18:00:45.0, pageSize:1000

2025-07-25 18:00:45,009 WARN history config cleaner successfully

2025-07-25 18:10:43,703 INFO [capacityManagement] start correct usage

2025-07-25 18:10:43,709 INFO [capacityManagement] end correct usage, cost: 0.002808125s

2025-07-25 18:10:45,015 WARN clearHistoryConfig get scheduled

2025-07-25 18:10:45,016 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 18:10:45,019 WARN clearConfigHistory, getBeforeStamp:2025-06-25 18:10:45.0, pageSize:1000

2025-07-25 18:10:45,027 WARN history config cleaner successfully

2025-07-25 18:23:07,368 INFO [capacityManagement] start correct usage

2025-07-25 18:23:07,372 INFO [capacityManagement] end correct usage, cost: 0.002013042s

2025-07-25 18:23:08,688 WARN clearHistoryConfig get scheduled

2025-07-25 18:23:08,688 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 18:23:08,690 WARN clearConfigHistory, getBeforeStamp:2025-06-25 18:23:08.0, pageSize:1000

2025-07-25 18:23:08,699 WARN history config cleaner successfully

2025-07-25 22:39:38,334 INFO [capacityManagement] start correct usage

2025-07-25 22:39:38,339 INFO [capacityManagement] end correct usage, cost: 0.002134084s

2025-07-25 22:39:39,668 WARN clearHistoryConfig get scheduled

2025-07-25 22:39:39,669 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-25 22:39:39,671 WARN clearConfigHistory, getBeforeStamp:2025-06-25 22:39:39.0, pageSize:1000

2025-07-25 22:39:39,677 WARN history config cleaner successfully

