2025-07-30 01:04:13,241 INFO [capacityManagement] start correct usage

2025-07-30 01:04:13,247 INFO [capacityManagement] end correct usage, cost: 5.89792E-4s

2025-07-30 01:04:15,029 WARN clearHistoryConfig get scheduled

2025-07-30 01:04:15,032 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-30 01:04:15,033 WARN clearConfigHistory, getBeforeStamp:2025-06-30 01:04:15.0, pageSize:1000

2025-07-30 01:04:15,034 WARN history config cleaner successfully

2025-07-30 04:18:19,558 INFO [capacityManagement] start correct usage

2025-07-30 04:18:19,560 INFO [capacityManagement] end correct usage, cost: 8.13709E-4s

2025-07-30 04:18:21,352 WARN clearHistoryConfig get scheduled

2025-07-30 04:18:21,353 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-30 04:18:21,354 WARN clearConfigHistory, getBeforeStamp:2025-06-30 04:18:21.0, pageSize:1000

2025-07-30 04:18:21,356 WARN history config cleaner successfully

2025-07-30 07:41:52,108 INFO [capacityManagement] start correct usage

2025-07-30 07:41:52,110 INFO [capacityManagement] end correct usage, cost: 5.6625E-4s

2025-07-30 07:41:53,900 WARN clearHistoryConfig get scheduled

2025-07-30 07:41:53,901 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-30 07:41:53,902 WARN clearConfigHistory, getBeforeStamp:2025-06-30 07:41:53.0, pageSize:1000

2025-07-30 07:41:53,904 WARN history config cleaner successfully

2025-07-30 09:48:41,654 INFO [capacityManagement] start correct usage

2025-07-30 09:48:41,667 INFO [capacityManagement] end correct usage, cost: 0.003090125s

2025-07-30 09:48:43,447 WARN clearHistoryConfig get scheduled

2025-07-30 09:48:43,448 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-30 09:48:43,451 WARN clearConfigHistory, getBeforeStamp:2025-06-30 09:48:43.0, pageSize:1000

2025-07-30 09:48:43,456 WARN history config cleaner successfully

