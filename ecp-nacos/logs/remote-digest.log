2025-07-22 10:42:32,744 INFO Connection transportTerminated,connectionId = 1753087588027_192.168.65.1_43883 

2025-07-22 10:42:32,753 INFO [1753087588027_192.168.65.1_43883]client disconnected,clear config listen context

2025-07-22 10:45:15,282 INFO Connection transportReady,connectionId = 1753152315282_192.168.65.1_20714 

2025-07-22 10:45:29,187 INFO Connection transportTerminated,connectionId = 1753152315282_192.168.65.1_20714 

2025-07-22 10:45:29,188 WARN [1753152315282_192.168.65.1_20714] connection  close bi stream exception  : {}

java.lang.IllegalStateException: call already closed
	at com.google.common.base.Preconditions.checkState(Preconditions.java:512)
	at io.grpc.internal.ServerCallImpl.closeInternal(ServerCallImpl.java:218)
	at io.grpc.internal.ServerCallImpl.close(ServerCallImpl.java:213)
	at io.grpc.stub.ServerCalls$ServerCallStreamObserverImpl.onCompleted(ServerCalls.java:395)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.closeBiStream(GrpcConnection.java:205)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.close(GrpcConnection.java:190)
	at com.alibaba.nacos.core.remote.ConnectionManager.unregister(ConnectionManager.java:158)
	at com.alibaba.nacos.core.remote.grpc.AddressTransportFilter.transportTerminated(AddressTransportFilter.java:77)
	at io.grpc.internal.ServerImpl$ServerTransportListenerImpl.transportTerminated(ServerImpl.java:456)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.notifyTerminated(NettyServerTransport.java:213)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.access$100(NettyServerTransport.java:51)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:147)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:140)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$CloseFuture.setClosed(AbstractChannel.java:1164)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.doClose0(AbstractChannel.java:755)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:731)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:620)
	at io.grpc.netty.shaded.io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.shutdownInput(AbstractEpollChannel.java:522)
	at io.grpc.netty.shaded.io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:823)
	at io.grpc.netty.shaded.io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:509)
	at io.grpc.netty.shaded.io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:407)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-07-22 10:45:29,189 INFO [1753152315282_192.168.65.1_20714]client disconnected,clear config listen context

2025-07-22 10:45:36,593 INFO Connection transportReady,connectionId = 1753152336593_192.168.65.1_53484 

2025-07-22 10:46:34,559 INFO Connection transportReady,connectionId = 1753152394559_192.168.65.1_22921 

2025-07-22 10:46:42,347 INFO Connection transportTerminated,connectionId = 1753152336593_192.168.65.1_53484 

2025-07-22 10:46:42,349 WARN [1753152336593_192.168.65.1_53484] connection  close bi stream exception  : {}

java.lang.IllegalStateException: call already closed
	at com.google.common.base.Preconditions.checkState(Preconditions.java:512)
	at io.grpc.internal.ServerCallImpl.closeInternal(ServerCallImpl.java:218)
	at io.grpc.internal.ServerCallImpl.close(ServerCallImpl.java:213)
	at io.grpc.stub.ServerCalls$ServerCallStreamObserverImpl.onCompleted(ServerCalls.java:395)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.closeBiStream(GrpcConnection.java:205)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.close(GrpcConnection.java:190)
	at com.alibaba.nacos.core.remote.ConnectionManager.unregister(ConnectionManager.java:158)
	at com.alibaba.nacos.core.remote.grpc.AddressTransportFilter.transportTerminated(AddressTransportFilter.java:77)
	at io.grpc.internal.ServerImpl$ServerTransportListenerImpl.transportTerminated(ServerImpl.java:456)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.notifyTerminated(NettyServerTransport.java:213)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.access$100(NettyServerTransport.java:51)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:147)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:140)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$CloseFuture.setClosed(AbstractChannel.java:1164)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.doClose0(AbstractChannel.java:755)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:731)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:620)
	at io.grpc.netty.shaded.io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.shutdownInput(AbstractEpollChannel.java:522)
	at io.grpc.netty.shaded.io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:823)
	at io.grpc.netty.shaded.io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:509)
	at io.grpc.netty.shaded.io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:407)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-07-22 10:46:42,350 INFO [1753152336593_192.168.65.1_53484]client disconnected,clear config listen context

2025-07-22 10:46:49,676 INFO Connection transportReady,connectionId = 1753152409676_192.168.65.1_26838 

2025-07-22 10:52:00,020 INFO [1753152409676_192.168.65.1_26838]client disconnected,clear config listen context

2025-07-22 10:52:19,181 INFO Connection transportTerminated,connectionId = 1753152409676_192.168.65.1_26838 

2025-07-22 10:52:19,190 INFO Connection transportReady,connectionId = 1753152739190_192.168.65.1_43928 

2025-07-22 10:52:47,068 INFO [1753152739190_192.168.65.1_43928]client disconnected,clear config listen context

2025-07-22 10:53:02,821 INFO Connection transportTerminated,connectionId = 1753152739190_192.168.65.1_43928 

2025-07-22 10:53:02,834 INFO Connection transportReady,connectionId = 1753152782834_192.168.65.1_19029 

2025-07-22 10:53:32,481 INFO Connection transportReady,connectionId = 1753152812481_192.168.65.1_39928 

2025-07-22 10:53:32,590 INFO Connection transportTerminated,connectionId = 1753152782834_192.168.65.1_19029 

2025-07-22 10:53:32,592 WARN [1753152782834_192.168.65.1_19029] connection  close bi stream exception  : {}

java.lang.IllegalStateException: call already closed
	at com.google.common.base.Preconditions.checkState(Preconditions.java:512)
	at io.grpc.internal.ServerCallImpl.closeInternal(ServerCallImpl.java:218)
	at io.grpc.internal.ServerCallImpl.close(ServerCallImpl.java:213)
	at io.grpc.stub.ServerCalls$ServerCallStreamObserverImpl.onCompleted(ServerCalls.java:395)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.closeBiStream(GrpcConnection.java:205)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.close(GrpcConnection.java:190)
	at com.alibaba.nacos.core.remote.ConnectionManager.unregister(ConnectionManager.java:158)
	at com.alibaba.nacos.core.remote.grpc.AddressTransportFilter.transportTerminated(AddressTransportFilter.java:77)
	at io.grpc.internal.ServerImpl$ServerTransportListenerImpl.transportTerminated(ServerImpl.java:456)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.notifyTerminated(NettyServerTransport.java:213)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.access$100(NettyServerTransport.java:51)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:147)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:140)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$CloseFuture.setClosed(AbstractChannel.java:1164)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.doClose0(AbstractChannel.java:755)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:731)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:620)
	at io.grpc.netty.shaded.io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.shutdownInput(AbstractEpollChannel.java:522)
	at io.grpc.netty.shaded.io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:823)
	at io.grpc.netty.shaded.io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:509)
	at io.grpc.netty.shaded.io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:407)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-07-22 10:53:32,593 INFO [1753152782834_192.168.65.1_19029]client disconnected,clear config listen context

2025-07-22 11:02:22,668 INFO [1753152812481_192.168.65.1_39928]client disconnected,clear config listen context

2025-07-22 11:02:26,406 INFO Connection transportTerminated,connectionId = 1753152812481_192.168.65.1_39928 

2025-07-22 11:02:26,421 INFO Connection transportReady,connectionId = 1753153346421_192.168.65.1_48198 

2025-07-22 11:02:26,422 INFO Connection transportReady,connectionId = 1753153346422_192.168.65.1_58326 

2025-07-22 11:02:32,938 INFO Connection transportTerminated,connectionId = 1753153346422_192.168.65.1_58326 

2025-07-22 11:02:43,119 WARN [grpc] Invalid connection Id ,connection [1753153346421_192.168.65.1_48198] is un registered ,

2025-07-22 11:02:51,737 INFO Connection transportReady,connectionId = 1753153371737_192.168.65.1_31678 

2025-07-22 11:02:55,565 INFO Connection transportTerminated,connectionId = 1753153371737_192.168.65.1_31678 

2025-07-22 11:03:24,182 INFO Connection transportReady,connectionId = 1753153404182_192.168.65.1_62328 

2025-07-22 11:03:27,478 INFO Connection transportTerminated,connectionId = 1753153404182_192.168.65.1_62328 

2025-07-22 11:03:27,981 INFO Connection transportReady,connectionId = 1753153407981_192.168.65.1_17305 

2025-07-22 11:03:28,092 INFO Connection transportTerminated,connectionId = 1753153346421_192.168.65.1_48198 

2025-07-22 11:03:28,099 INFO Connection transportReady,connectionId = 1753153408099_192.168.65.1_59346 

2025-07-22 11:03:28,212 INFO Connection transportTerminated,connectionId = 1753153407981_192.168.65.1_17305 

2025-07-22 11:03:28,213 INFO [1753153407981_192.168.65.1_17305]client disconnected,clear config listen context

2025-07-22 11:05:20,354 INFO Connection transportReady,connectionId = 1753153520354_192.168.65.1_23930 

2025-07-22 11:05:42,234 INFO Connection transportTerminated,connectionId = 1753153408099_192.168.65.1_59346 

2025-07-22 11:05:42,236 INFO [1753153408099_192.168.65.1_59346]client disconnected,clear config listen context

2025-07-22 11:05:42,237 WARN Ack receive on a outdated request ,connection id=1753153520354_192.168.65.1_23930,requestId=66 

2025-07-22 17:25:04,020 INFO Connection transportTerminated,connectionId = 1753153520354_192.168.65.1_23930 

2025-07-22 17:25:04,023 INFO [1753153520354_192.168.65.1_23930]client disconnected,clear config listen context

2025-07-22 18:25:43,473 INFO Connection transportTerminated,connectionId = 1753152394559_192.168.65.1_22921 

2025-07-22 18:25:43,476 INFO [1753152394559_192.168.65.1_22921]client disconnected,clear config listen context

