2025-07-29 02:51:36,444 INFO [capacityManagement] start correct usage

2025-07-29 02:51:36,453 INFO [capacityManagement] end correct usage, cost: 0.001177791s

2025-07-29 02:51:38,008 WARN clearHistoryConfig get scheduled

2025-07-29 02:51:38,011 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 02:51:38,011 WARN clearConfigHistory, getBeforeStamp:2025-06-29 02:51:38.0, pageSize:1000

2025-07-29 02:51:38,014 WARN history config cleaner successfully

2025-07-29 05:56:29,336 INFO [capacityManagement] start correct usage

2025-07-29 05:56:29,338 INFO [capacityManagement] end correct usage, cost: 0.001835666s

2025-07-29 05:56:30,895 WARN clearHistoryConfig get scheduled

2025-07-29 05:56:30,896 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 05:56:30,898 WARN clearConfigHistory, getBeforeStamp:2025-06-29 05:56:30.0, pageSize:1000

2025-07-29 05:56:30,902 WARN history config cleaner successfully

2025-07-29 09:36:41,306 INFO [capacityManagement] start correct usage

2025-07-29 09:36:41,310 INFO [capacityManagement] end correct usage, cost: 0.002025708s

2025-07-29 09:36:42,875 WARN clearHistoryConfig get scheduled

2025-07-29 09:36:42,876 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 09:36:42,879 WARN clearConfigHistory, getBeforeStamp:2025-06-29 09:36:42.0, pageSize:1000

2025-07-29 09:36:42,894 WARN history config cleaner successfully

2025-07-29 09:46:41,316 INFO [capacityManagement] start correct usage

2025-07-29 09:46:41,320 INFO [capacityManagement] end correct usage, cost: 0.002144s

2025-07-29 09:46:42,901 WARN clearHistoryConfig get scheduled

2025-07-29 09:46:42,903 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 09:46:42,905 WARN clearConfigHistory, getBeforeStamp:2025-06-29 09:46:42.0, pageSize:1000

2025-07-29 09:46:42,912 WARN history config cleaner successfully

2025-07-29 09:56:41,327 INFO [capacityManagement] start correct usage

2025-07-29 09:56:41,332 INFO [capacityManagement] end correct usage, cost: 0.001709875s

2025-07-29 09:56:42,920 WARN clearHistoryConfig get scheduled

2025-07-29 09:56:42,921 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 09:56:42,922 WARN clearConfigHistory, getBeforeStamp:2025-06-29 09:56:42.0, pageSize:1000

2025-07-29 09:56:42,940 WARN history config cleaner successfully

2025-07-29 10:06:41,338 INFO [capacityManagement] start correct usage

2025-07-29 10:06:41,342 INFO [capacityManagement] end correct usage, cost: 0.002596209s

2025-07-29 10:06:42,947 WARN clearHistoryConfig get scheduled

2025-07-29 10:06:42,948 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 10:06:42,950 WARN clearConfigHistory, getBeforeStamp:2025-06-29 10:06:42.0, pageSize:1000

2025-07-29 10:06:42,957 WARN history config cleaner successfully

2025-07-29 10:16:41,348 INFO [capacityManagement] start correct usage

2025-07-29 10:16:41,352 INFO [capacityManagement] end correct usage, cost: 0.00185225s

2025-07-29 10:16:42,964 WARN clearHistoryConfig get scheduled

2025-07-29 10:16:42,964 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 10:16:42,965 WARN clearConfigHistory, getBeforeStamp:2025-06-29 10:16:42.0, pageSize:1000

2025-07-29 10:16:42,969 WARN history config cleaner successfully

2025-07-29 10:26:41,359 INFO [capacityManagement] start correct usage

2025-07-29 10:26:41,364 INFO [capacityManagement] end correct usage, cost: 0.002513083s

2025-07-29 10:26:42,974 WARN clearHistoryConfig get scheduled

2025-07-29 10:26:42,975 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 10:26:42,977 WARN clearConfigHistory, getBeforeStamp:2025-06-29 10:26:42.0, pageSize:1000

2025-07-29 10:26:42,985 WARN history config cleaner successfully

2025-07-29 10:44:32,629 INFO [capacityManagement] start correct usage

2025-07-29 10:44:32,633 INFO [capacityManagement] end correct usage, cost: 0.001582666s

2025-07-29 10:44:34,254 WARN clearHistoryConfig get scheduled

2025-07-29 10:44:34,256 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 10:44:34,258 WARN clearConfigHistory, getBeforeStamp:2025-06-29 10:44:34.0, pageSize:1000

2025-07-29 10:44:34,265 WARN history config cleaner successfully

2025-07-29 10:54:32,641 INFO [capacityManagement] start correct usage

2025-07-29 10:54:32,647 INFO [capacityManagement] end correct usage, cost: 0.002386875s

2025-07-29 10:54:34,271 WARN clearHistoryConfig get scheduled

2025-07-29 10:54:34,272 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 10:54:34,274 WARN clearConfigHistory, getBeforeStamp:2025-06-29 10:54:34.0, pageSize:1000

2025-07-29 10:54:34,281 WARN history config cleaner successfully

2025-07-29 11:04:32,651 INFO [capacityManagement] start correct usage

2025-07-29 11:04:32,664 INFO [capacityManagement] end correct usage, cost: 0.008860083s

2025-07-29 11:04:34,289 WARN clearHistoryConfig get scheduled

2025-07-29 11:04:34,290 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 11:04:34,292 WARN clearConfigHistory, getBeforeStamp:2025-06-29 11:04:34.0, pageSize:1000

2025-07-29 11:04:34,300 WARN history config cleaner successfully

2025-07-29 11:14:32,675 INFO [capacityManagement] start correct usage

2025-07-29 11:14:32,681 INFO [capacityManagement] end correct usage, cost: 0.002482375s

2025-07-29 11:14:34,308 WARN clearHistoryConfig get scheduled

2025-07-29 11:14:34,309 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 11:14:34,311 WARN clearConfigHistory, getBeforeStamp:2025-06-29 11:14:34.0, pageSize:1000

2025-07-29 11:14:34,319 WARN history config cleaner successfully

2025-07-29 12:04:49,264 INFO [capacityManagement] start correct usage

2025-07-29 12:04:49,269 INFO [capacityManagement] end correct usage, cost: 0.003852875s

2025-07-29 12:04:50,899 WARN clearHistoryConfig get scheduled

2025-07-29 12:04:50,900 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 12:04:50,901 WARN clearConfigHistory, getBeforeStamp:2025-06-29 12:04:50.0, pageSize:1000

2025-07-29 12:04:50,907 WARN history config cleaner successfully

2025-07-29 12:14:49,276 INFO [capacityManagement] start correct usage

2025-07-29 12:14:49,281 INFO [capacityManagement] end correct usage, cost: 0.002477125s

2025-07-29 12:14:50,913 WARN clearHistoryConfig get scheduled

2025-07-29 12:14:50,914 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 12:14:50,915 WARN clearConfigHistory, getBeforeStamp:2025-06-29 12:14:50.0, pageSize:1000

2025-07-29 12:14:50,920 WARN history config cleaner successfully

2025-07-29 12:18:53,010 WARN HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m455ms907µs819ns).

2025-07-29 12:24:49,290 INFO [capacityManagement] start correct usage

2025-07-29 12:24:49,293 INFO [capacityManagement] end correct usage, cost: 0.001752875s

2025-07-29 12:24:50,925 WARN clearHistoryConfig get scheduled

2025-07-29 12:24:50,926 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 12:24:50,927 WARN clearConfigHistory, getBeforeStamp:2025-06-29 12:24:50.0, pageSize:1000

2025-07-29 12:24:50,937 WARN history config cleaner successfully

2025-07-29 12:34:49,310 INFO [capacityManagement] start correct usage

2025-07-29 12:34:49,315 INFO [capacityManagement] end correct usage, cost: 0.001591542s

2025-07-29 12:34:50,950 WARN clearHistoryConfig get scheduled

2025-07-29 12:34:50,950 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 12:34:50,951 WARN clearConfigHistory, getBeforeStamp:2025-06-29 12:34:50.0, pageSize:1000

2025-07-29 12:34:50,955 WARN history config cleaner successfully

2025-07-29 12:44:49,322 INFO [capacityManagement] start correct usage

2025-07-29 12:44:49,324 INFO [capacityManagement] end correct usage, cost: 6.17375E-4s

2025-07-29 12:44:50,970 WARN clearHistoryConfig get scheduled

2025-07-29 12:44:50,970 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 12:44:50,971 WARN clearConfigHistory, getBeforeStamp:2025-06-29 12:44:50.0, pageSize:1000

2025-07-29 12:44:50,975 WARN history config cleaner successfully

2025-07-29 12:54:49,332 INFO [capacityManagement] start correct usage

2025-07-29 12:54:49,338 INFO [capacityManagement] end correct usage, cost: 0.002200291s

2025-07-29 12:54:50,981 WARN clearHistoryConfig get scheduled

2025-07-29 12:54:50,983 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 12:54:50,984 WARN clearConfigHistory, getBeforeStamp:2025-06-29 12:54:50.0, pageSize:1000

2025-07-29 12:54:50,990 WARN history config cleaner successfully

2025-07-29 13:04:49,348 INFO [capacityManagement] start correct usage

2025-07-29 13:04:49,353 INFO [capacityManagement] end correct usage, cost: 0.001819875s

2025-07-29 13:04:51,000 WARN clearHistoryConfig get scheduled

2025-07-29 13:04:51,001 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 13:04:51,003 WARN clearConfigHistory, getBeforeStamp:2025-06-29 13:04:51.0, pageSize:1000

2025-07-29 13:04:51,007 WARN history config cleaner successfully

2025-07-29 13:14:49,361 INFO [capacityManagement] start correct usage

2025-07-29 13:14:49,365 INFO [capacityManagement] end correct usage, cost: 0.001745375s

2025-07-29 13:14:51,014 WARN clearHistoryConfig get scheduled

2025-07-29 13:14:51,015 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 13:14:51,017 WARN clearConfigHistory, getBeforeStamp:2025-06-29 13:14:51.0, pageSize:1000

2025-07-29 13:14:51,026 WARN history config cleaner successfully

2025-07-29 13:24:49,261 INFO [capacityManagement] start correct usage

2025-07-29 13:24:49,266 INFO [capacityManagement] end correct usage, cost: 0.0021455s

2025-07-29 13:24:50,919 WARN clearHistoryConfig get scheduled

2025-07-29 13:24:50,919 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 13:24:50,920 WARN clearConfigHistory, getBeforeStamp:2025-06-29 13:24:50.0, pageSize:1000

2025-07-29 13:24:50,923 WARN history config cleaner successfully

2025-07-29 13:34:49,268 INFO [capacityManagement] start correct usage

2025-07-29 13:34:49,271 INFO [capacityManagement] end correct usage, cost: 0.00116025s

2025-07-29 13:34:50,924 WARN clearHistoryConfig get scheduled

2025-07-29 13:34:50,925 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 13:34:50,926 WARN clearConfigHistory, getBeforeStamp:2025-06-29 13:34:50.0, pageSize:1000

2025-07-29 13:34:50,930 WARN history config cleaner successfully

2025-07-29 13:44:49,276 INFO [capacityManagement] start correct usage

2025-07-29 13:44:49,280 INFO [capacityManagement] end correct usage, cost: 0.001628833s

2025-07-29 13:44:50,932 WARN clearHistoryConfig get scheduled

2025-07-29 13:44:50,932 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 13:44:50,933 WARN clearConfigHistory, getBeforeStamp:2025-06-29 13:44:50.0, pageSize:1000

2025-07-29 13:44:50,939 WARN history config cleaner successfully

2025-07-29 13:54:49,283 INFO [capacityManagement] start correct usage

2025-07-29 13:54:49,286 INFO [capacityManagement] end correct usage, cost: 0.001423834s

2025-07-29 13:54:50,942 WARN clearHistoryConfig get scheduled

2025-07-29 13:54:50,942 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 13:54:50,944 WARN clearConfigHistory, getBeforeStamp:2025-06-29 13:54:50.0, pageSize:1000

2025-07-29 13:54:50,950 WARN history config cleaner successfully

2025-07-29 14:04:49,287 INFO [capacityManagement] start correct usage

2025-07-29 14:04:49,292 INFO [capacityManagement] end correct usage, cost: 0.002476209s

2025-07-29 14:04:50,950 WARN clearHistoryConfig get scheduled

2025-07-29 14:04:50,951 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 14:04:50,952 WARN clearConfigHistory, getBeforeStamp:2025-06-29 14:04:50.0, pageSize:1000

2025-07-29 14:04:50,964 WARN history config cleaner successfully

2025-07-29 14:14:49,295 INFO [capacityManagement] start correct usage

2025-07-29 14:14:49,300 INFO [capacityManagement] end correct usage, cost: 0.002284167s

2025-07-29 14:14:50,968 WARN clearHistoryConfig get scheduled

2025-07-29 14:14:50,969 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 14:14:50,971 WARN clearConfigHistory, getBeforeStamp:2025-06-29 14:14:50.0, pageSize:1000

2025-07-29 14:14:50,977 WARN history config cleaner successfully

2025-07-29 14:37:02,297 INFO [capacityManagement] start correct usage

2025-07-29 14:37:02,301 INFO [capacityManagement] end correct usage, cost: 0.002719209s

2025-07-29 14:37:03,975 WARN clearHistoryConfig get scheduled

2025-07-29 14:37:03,976 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 14:37:03,978 WARN clearConfigHistory, getBeforeStamp:2025-06-29 14:37:03.0, pageSize:1000

2025-07-29 14:37:03,985 WARN history config cleaner successfully

2025-07-29 14:47:02,303 INFO [capacityManagement] start correct usage

2025-07-29 14:47:02,308 INFO [capacityManagement] end correct usage, cost: 0.002500375s

2025-07-29 14:47:03,987 WARN clearHistoryConfig get scheduled

2025-07-29 14:47:03,988 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 14:47:03,989 WARN clearConfigHistory, getBeforeStamp:2025-06-29 14:47:03.0, pageSize:1000

2025-07-29 14:47:03,997 WARN history config cleaner successfully

2025-07-29 14:57:02,310 INFO [capacityManagement] start correct usage

2025-07-29 14:57:02,314 INFO [capacityManagement] end correct usage, cost: 0.001960709s

2025-07-29 14:57:04,001 WARN clearHistoryConfig get scheduled

2025-07-29 14:57:04,001 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 14:57:04,003 WARN clearConfigHistory, getBeforeStamp:2025-06-29 14:57:04.0, pageSize:1000

2025-07-29 14:57:04,008 WARN history config cleaner successfully

2025-07-29 15:07:02,291 INFO [capacityManagement] start correct usage

2025-07-29 15:07:02,297 INFO [capacityManagement] end correct usage, cost: 0.002975209s

2025-07-29 15:07:03,984 WARN clearHistoryConfig get scheduled

2025-07-29 15:07:03,985 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 15:07:03,987 WARN clearConfigHistory, getBeforeStamp:2025-06-29 15:07:03.0, pageSize:1000

2025-07-29 15:07:03,993 WARN history config cleaner successfully

2025-07-29 15:17:02,296 INFO [capacityManagement] start correct usage

2025-07-29 15:17:02,299 INFO [capacityManagement] end correct usage, cost: 0.001406667s

2025-07-29 15:17:03,994 WARN clearHistoryConfig get scheduled

2025-07-29 15:17:03,995 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 15:17:03,996 WARN clearConfigHistory, getBeforeStamp:2025-06-29 15:17:03.0, pageSize:1000

2025-07-29 15:17:04,000 WARN history config cleaner successfully

2025-07-29 15:27:02,301 INFO [capacityManagement] start correct usage

2025-07-29 15:27:02,304 INFO [capacityManagement] end correct usage, cost: 0.001032875s

2025-07-29 15:27:04,000 WARN clearHistoryConfig get scheduled

2025-07-29 15:27:04,001 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 15:27:04,002 WARN clearConfigHistory, getBeforeStamp:2025-06-29 15:27:04.0, pageSize:1000

2025-07-29 15:27:04,005 WARN history config cleaner successfully

2025-07-29 15:37:02,306 INFO [capacityManagement] start correct usage

2025-07-29 15:37:02,311 INFO [capacityManagement] end correct usage, cost: 0.001713958s

2025-07-29 15:37:04,003 WARN clearHistoryConfig get scheduled

2025-07-29 15:37:04,004 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 15:37:04,005 WARN clearConfigHistory, getBeforeStamp:2025-06-29 15:37:04.0, pageSize:1000

2025-07-29 15:37:04,011 WARN history config cleaner successfully

2025-07-29 15:47:02,309 INFO [capacityManagement] start correct usage

2025-07-29 15:47:02,312 INFO [capacityManagement] end correct usage, cost: 0.001184208s

2025-07-29 15:47:04,012 WARN clearHistoryConfig get scheduled

2025-07-29 15:47:04,013 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 15:47:04,014 WARN clearConfigHistory, getBeforeStamp:2025-06-29 15:47:04.0, pageSize:1000

2025-07-29 15:47:04,018 WARN history config cleaner successfully

2025-07-29 15:57:02,312 INFO [capacityManagement] start correct usage

2025-07-29 15:57:02,314 INFO [capacityManagement] end correct usage, cost: 0.001192125s

2025-07-29 15:57:04,020 WARN clearHistoryConfig get scheduled

2025-07-29 15:57:04,020 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 15:57:04,022 WARN clearConfigHistory, getBeforeStamp:2025-06-29 15:57:04.0, pageSize:1000

2025-07-29 15:57:04,032 WARN history config cleaner successfully

2025-07-29 16:07:02,317 INFO [capacityManagement] start correct usage

2025-07-29 16:07:02,320 INFO [capacityManagement] end correct usage, cost: 0.001479792s

2025-07-29 16:07:04,031 WARN clearHistoryConfig get scheduled

2025-07-29 16:07:04,032 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 16:07:04,033 WARN clearConfigHistory, getBeforeStamp:2025-06-29 16:07:04.0, pageSize:1000

2025-07-29 16:07:04,038 WARN history config cleaner successfully

2025-07-29 16:17:02,321 INFO [capacityManagement] start correct usage

2025-07-29 16:17:02,325 INFO [capacityManagement] end correct usage, cost: 0.001904709s

2025-07-29 16:17:04,042 WARN clearHistoryConfig get scheduled

2025-07-29 16:17:04,043 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 16:17:04,045 WARN clearConfigHistory, getBeforeStamp:2025-06-29 16:17:04.0, pageSize:1000

2025-07-29 16:17:04,050 WARN history config cleaner successfully

2025-07-29 16:27:02,326 INFO [capacityManagement] start correct usage

2025-07-29 16:27:02,331 INFO [capacityManagement] end correct usage, cost: 0.00186025s

2025-07-29 16:27:04,052 WARN clearHistoryConfig get scheduled

2025-07-29 16:27:04,054 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 16:27:04,055 WARN clearConfigHistory, getBeforeStamp:2025-06-29 16:27:04.0, pageSize:1000

2025-07-29 16:27:04,061 WARN history config cleaner successfully

2025-07-29 16:37:02,330 INFO [capacityManagement] start correct usage

2025-07-29 16:37:02,335 INFO [capacityManagement] end correct usage, cost: 0.001721708s

2025-07-29 16:37:04,060 WARN clearHistoryConfig get scheduled

2025-07-29 16:37:04,061 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 16:37:04,061 WARN clearConfigHistory, getBeforeStamp:2025-06-29 16:37:04.0, pageSize:1000

2025-07-29 16:37:04,066 WARN history config cleaner successfully

2025-07-29 16:47:02,335 INFO [capacityManagement] start correct usage

2025-07-29 16:47:02,339 INFO [capacityManagement] end correct usage, cost: 0.0018555s

2025-07-29 16:47:04,066 WARN clearHistoryConfig get scheduled

2025-07-29 16:47:04,067 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 16:47:04,068 WARN clearConfigHistory, getBeforeStamp:2025-06-29 16:47:04.0, pageSize:1000

2025-07-29 16:47:04,074 WARN history config cleaner successfully

2025-07-29 16:57:02,342 INFO [capacityManagement] start correct usage

2025-07-29 16:57:02,348 INFO [capacityManagement] end correct usage, cost: 0.002447333s

2025-07-29 16:57:04,076 WARN clearHistoryConfig get scheduled

2025-07-29 16:57:04,076 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 16:57:04,077 WARN clearConfigHistory, getBeforeStamp:2025-06-29 16:57:04.0, pageSize:1000

2025-07-29 16:57:04,079 WARN history config cleaner successfully

2025-07-29 17:07:02,347 INFO [capacityManagement] start correct usage

2025-07-29 17:07:02,353 INFO [capacityManagement] end correct usage, cost: 0.003594542s

2025-07-29 17:07:04,078 WARN clearHistoryConfig get scheduled

2025-07-29 17:07:04,079 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 17:07:04,081 WARN clearConfigHistory, getBeforeStamp:2025-06-29 17:07:04.0, pageSize:1000

2025-07-29 17:07:04,088 WARN history config cleaner successfully

2025-07-29 17:17:02,352 INFO [capacityManagement] start correct usage

2025-07-29 17:17:02,358 INFO [capacityManagement] end correct usage, cost: 0.003601792s

2025-07-29 17:17:04,088 WARN clearHistoryConfig get scheduled

2025-07-29 17:17:04,089 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 17:17:04,092 WARN clearConfigHistory, getBeforeStamp:2025-06-29 17:17:04.0, pageSize:1000

2025-07-29 17:17:04,100 WARN history config cleaner successfully

2025-07-29 17:27:02,359 INFO [capacityManagement] start correct usage

2025-07-29 17:27:02,365 INFO [capacityManagement] end correct usage, cost: 0.002856792s

2025-07-29 17:27:04,099 WARN clearHistoryConfig get scheduled

2025-07-29 17:27:04,099 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 17:27:04,100 WARN clearConfigHistory, getBeforeStamp:2025-06-29 17:27:04.0, pageSize:1000

2025-07-29 17:27:04,106 WARN history config cleaner successfully

2025-07-29 17:37:02,365 INFO [capacityManagement] start correct usage

2025-07-29 17:37:02,368 INFO [capacityManagement] end correct usage, cost: 0.002482792s

2025-07-29 17:37:04,104 WARN clearHistoryConfig get scheduled

2025-07-29 17:37:04,105 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 17:37:04,106 WARN clearConfigHistory, getBeforeStamp:2025-06-29 17:37:04.0, pageSize:1000

2025-07-29 17:37:04,123 WARN history config cleaner successfully

2025-07-29 17:47:02,372 INFO [capacityManagement] start correct usage

2025-07-29 17:47:02,378 INFO [capacityManagement] end correct usage, cost: 0.003172625s

2025-07-29 17:47:04,122 WARN clearHistoryConfig get scheduled

2025-07-29 17:47:04,122 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 17:47:04,124 WARN clearConfigHistory, getBeforeStamp:2025-06-29 17:47:04.0, pageSize:1000

2025-07-29 17:47:04,130 WARN history config cleaner successfully

2025-07-29 17:57:02,378 INFO [capacityManagement] start correct usage

2025-07-29 17:57:02,383 INFO [capacityManagement] end correct usage, cost: 0.002160625s

2025-07-29 17:57:04,128 WARN clearHistoryConfig get scheduled

2025-07-29 17:57:04,129 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 17:57:04,131 WARN clearConfigHistory, getBeforeStamp:2025-06-29 17:57:04.0, pageSize:1000

2025-07-29 17:57:04,137 WARN history config cleaner successfully

2025-07-29 18:07:02,386 INFO [capacityManagement] start correct usage

2025-07-29 18:07:02,391 INFO [capacityManagement] end correct usage, cost: 0.001808709s

2025-07-29 18:07:04,138 WARN clearHistoryConfig get scheduled

2025-07-29 18:07:04,139 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 18:07:04,140 WARN clearConfigHistory, getBeforeStamp:2025-06-29 18:07:04.0, pageSize:1000

2025-07-29 18:07:04,143 WARN history config cleaner successfully

2025-07-29 18:17:02,395 INFO [capacityManagement] start correct usage

2025-07-29 18:17:02,402 INFO [capacityManagement] end correct usage, cost: 0.00274575s

2025-07-29 18:17:04,142 WARN clearHistoryConfig get scheduled

2025-07-29 18:17:04,143 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 18:17:04,144 WARN clearConfigHistory, getBeforeStamp:2025-06-29 18:17:04.0, pageSize:1000

2025-07-29 18:17:04,150 WARN history config cleaner successfully

2025-07-29 18:27:02,404 INFO [capacityManagement] start correct usage

2025-07-29 18:27:02,409 INFO [capacityManagement] end correct usage, cost: 0.002288625s

2025-07-29 18:27:04,156 WARN clearHistoryConfig get scheduled

2025-07-29 18:27:04,157 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 18:27:04,159 WARN clearConfigHistory, getBeforeStamp:2025-06-29 18:27:04.0, pageSize:1000

2025-07-29 18:27:04,165 WARN history config cleaner successfully

2025-07-29 18:37:02,410 INFO [capacityManagement] start correct usage

2025-07-29 18:37:02,415 INFO [capacityManagement] end correct usage, cost: 0.00176325s

2025-07-29 18:37:04,165 WARN clearHistoryConfig get scheduled

2025-07-29 18:37:04,166 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 18:37:04,167 WARN clearConfigHistory, getBeforeStamp:2025-06-29 18:37:04.0, pageSize:1000

2025-07-29 18:37:04,171 WARN history config cleaner successfully

2025-07-29 18:47:02,415 INFO [capacityManagement] start correct usage

2025-07-29 18:47:02,420 INFO [capacityManagement] end correct usage, cost: 0.002025791s

2025-07-29 18:47:04,171 WARN clearHistoryConfig get scheduled

2025-07-29 18:47:04,171 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 18:47:04,173 WARN clearConfigHistory, getBeforeStamp:2025-06-29 18:47:04.0, pageSize:1000

2025-07-29 18:47:04,183 WARN history config cleaner successfully

2025-07-29 18:59:02,983 INFO [capacityManagement] start correct usage

2025-07-29 18:59:02,985 INFO [capacityManagement] end correct usage, cost: 0.00106775s

2025-07-29 18:59:04,746 WARN clearHistoryConfig get scheduled

2025-07-29 18:59:04,747 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 18:59:04,749 WARN clearConfigHistory, getBeforeStamp:2025-06-29 18:59:04.0, pageSize:1000

2025-07-29 18:59:04,754 WARN history config cleaner successfully

2025-07-29 19:09:02,985 INFO [capacityManagement] start correct usage

2025-07-29 19:09:02,989 INFO [capacityManagement] end correct usage, cost: 0.001613834s

2025-07-29 19:09:04,754 WARN clearHistoryConfig get scheduled

2025-07-29 19:09:04,755 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 19:09:04,756 WARN clearConfigHistory, getBeforeStamp:2025-06-29 19:09:04.0, pageSize:1000

2025-07-29 19:09:04,760 WARN history config cleaner successfully

2025-07-29 19:19:02,989 INFO [capacityManagement] start correct usage

2025-07-29 19:19:02,995 INFO [capacityManagement] end correct usage, cost: 0.003525042s

2025-07-29 19:19:04,761 WARN clearHistoryConfig get scheduled

2025-07-29 19:19:04,762 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 19:19:04,764 WARN clearConfigHistory, getBeforeStamp:2025-06-29 19:19:04.0, pageSize:1000

2025-07-29 19:19:04,780 WARN history config cleaner successfully

2025-07-29 19:29:02,997 INFO [capacityManagement] start correct usage

2025-07-29 19:29:03,000 INFO [capacityManagement] end correct usage, cost: 0.001772625s

2025-07-29 19:29:04,784 WARN clearHistoryConfig get scheduled

2025-07-29 19:29:04,785 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 19:29:04,786 WARN clearConfigHistory, getBeforeStamp:2025-06-29 19:29:04.0, pageSize:1000

2025-07-29 19:29:04,793 WARN history config cleaner successfully

2025-07-29 19:50:11,841 INFO [capacityManagement] start correct usage

2025-07-29 19:50:11,844 INFO [capacityManagement] end correct usage, cost: 0.001383916s

2025-07-29 19:50:13,629 WARN clearHistoryConfig get scheduled

2025-07-29 19:50:13,630 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 19:50:13,630 WARN clearConfigHistory, getBeforeStamp:2025-06-29 19:50:13.0, pageSize:1000

2025-07-29 19:50:13,633 WARN history config cleaner successfully

2025-07-29 21:30:34,085 INFO [capacityManagement] start correct usage

2025-07-29 21:30:34,086 INFO [capacityManagement] end correct usage, cost: 3.48417E-4s

2025-07-29 21:30:35,873 WARN clearHistoryConfig get scheduled

2025-07-29 21:30:35,874 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 21:30:35,875 WARN clearConfigHistory, getBeforeStamp:2025-06-29 21:30:35.0, pageSize:1000

2025-07-29 21:30:35,877 WARN history config cleaner successfully

2025-07-29 21:40:34,079 INFO [capacityManagement] start correct usage

2025-07-29 21:40:34,081 INFO [capacityManagement] end correct usage, cost: 7.86416E-4s

2025-07-29 21:40:35,866 WARN clearHistoryConfig get scheduled

2025-07-29 21:40:35,869 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 21:40:35,869 WARN clearConfigHistory, getBeforeStamp:2025-06-29 21:40:35.0, pageSize:1000

2025-07-29 21:40:35,871 WARN history config cleaner successfully

2025-07-29 21:50:34,123 INFO [capacityManagement] start correct usage

2025-07-29 21:50:34,124 INFO [capacityManagement] end correct usage, cost: 6.12542E-4s

2025-07-29 21:50:35,912 WARN clearHistoryConfig get scheduled

2025-07-29 21:50:35,912 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 21:50:35,912 WARN clearConfigHistory, getBeforeStamp:2025-06-29 21:50:35.0, pageSize:1000

2025-07-29 21:50:35,913 WARN history config cleaner successfully

2025-07-29 22:00:34,120 INFO [capacityManagement] start correct usage

2025-07-29 22:00:34,123 INFO [capacityManagement] end correct usage, cost: 8.49625E-4s

2025-07-29 22:00:35,912 WARN clearHistoryConfig get scheduled

2025-07-29 22:00:35,913 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 22:00:35,914 WARN clearConfigHistory, getBeforeStamp:2025-06-29 22:00:35.0, pageSize:1000

2025-07-29 22:00:35,917 WARN history config cleaner successfully

2025-07-29 22:10:34,116 INFO [capacityManagement] start correct usage

2025-07-29 22:10:34,118 INFO [capacityManagement] end correct usage, cost: 7.26666E-4s

2025-07-29 22:10:35,910 WARN clearHistoryConfig get scheduled

2025-07-29 22:10:35,911 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 22:10:35,912 WARN clearConfigHistory, getBeforeStamp:2025-06-29 22:10:35.0, pageSize:1000

2025-07-29 22:10:35,914 WARN history config cleaner successfully

2025-07-29 22:20:34,126 INFO [capacityManagement] start correct usage

2025-07-29 22:20:34,128 INFO [capacityManagement] end correct usage, cost: 8.84875E-4s

2025-07-29 22:20:35,917 WARN clearHistoryConfig get scheduled

2025-07-29 22:20:35,918 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 22:20:35,919 WARN clearConfigHistory, getBeforeStamp:2025-06-29 22:20:35.0, pageSize:1000

2025-07-29 22:20:35,921 WARN history config cleaner successfully

2025-07-29 22:30:34,129 INFO [capacityManagement] start correct usage

2025-07-29 22:30:34,131 INFO [capacityManagement] end correct usage, cost: 7.33958E-4s

2025-07-29 22:30:35,916 WARN clearHistoryConfig get scheduled

2025-07-29 22:30:35,917 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 22:30:35,918 WARN clearConfigHistory, getBeforeStamp:2025-06-29 22:30:35.0, pageSize:1000

2025-07-29 22:30:35,920 WARN history config cleaner successfully

2025-07-29 22:40:34,131 INFO [capacityManagement] start correct usage

2025-07-29 22:40:34,133 INFO [capacityManagement] end correct usage, cost: 3.91125E-4s

2025-07-29 22:40:35,915 WARN clearHistoryConfig get scheduled

2025-07-29 22:40:35,915 WARN clearHistoryConfig is enable in current context, try to run cleaner

2025-07-29 22:40:35,916 WARN clearConfigHistory, getBeforeStamp:2025-06-29 22:40:35.0, pageSize:1000

2025-07-29 22:40:35,921 WARN history config cleaner successfully

