2025/07/02-14:19:04.187569 281473296902840 RocksDB version: 8.8.1
2025/07/02-14:19:04.187808 281473296902840 Compile date 2023-11-23 11:07:28
2025/07/02-14:19:04.187812 281473296902840 DB SUMMARY
2025/07/02-14:19:04.187815 281473296902840 Host name (Env):  c12ff0a377ae
2025/07/02-14:19:04.187816 281473296902840 DB Session ID:  LR5P8XSGRSWGWUKMX0PS
2025/07/02-14:19:04.188286 281473296902840 CURRENT file:  CURRENT
2025/07/02-14:19:04.188289 281473296902840 IDENTITY file:  IDENTITY
2025/07/02-14:19:04.188351 281473296902840 MANIFEST file:  MANIFEST-000043 size: 497 Bytes
2025/07/02-14:19:04.188355 281473296902840 SST files in /home/<USER>/data/protocol/raft/naming_instance_metadata/log dir, Total Num: 3, files: 000037.sst 000040.sst 000041.sst 
2025/07/02-14:19:04.188357 281473296902840 Write Ahead Log file in /home/<USER>/data/protocol/raft/naming_instance_metadata/log: 000042.log size: 455 ; 
2025/07/02-14:19:04.188360 281473296902840                         Options.error_if_exists: 0
2025/07/02-14:19:04.188362 281473296902840                       Options.create_if_missing: 1
2025/07/02-14:19:04.188364 281473296902840                         Options.paranoid_checks: 1
2025/07/02-14:19:04.188365 281473296902840             Options.flush_verify_memtable_count: 1
2025/07/02-14:19:04.188366 281473296902840          Options.compaction_verify_record_count: 1
2025/07/02-14:19:04.188367 281473296902840                               Options.track_and_verify_wals_in_manifest: 0
2025/07/02-14:19:04.188368 281473296902840        Options.verify_sst_unique_id_in_manifest: 1
2025/07/02-14:19:04.188369 281473296902840                                     Options.env: 0xffff8920e4d0
2025/07/02-14:19:04.188370 281473296902840                                      Options.fs: PosixFileSystem
2025/07/02-14:19:04.188372 281473296902840                                Options.info_log: 0xffff8e370930
2025/07/02-14:19:04.188373 281473296902840                Options.max_file_opening_threads: 16
2025/07/02-14:19:04.188373 281473296902840                              Options.statistics: 0xffff87b85020
2025/07/02-14:19:04.188375 281473296902840                              Options.statistics stats level: 3
2025/07/02-14:19:04.188376 281473296902840                               Options.use_fsync: 0
2025/07/02-14:19:04.188376 281473296902840                       Options.max_log_file_size: 0
2025/07/02-14:19:04.188378 281473296902840                  Options.max_manifest_file_size: 1073741824
2025/07/02-14:19:04.188379 281473296902840                   Options.log_file_time_to_roll: 0
2025/07/02-14:19:04.188380 281473296902840                       Options.keep_log_file_num: 100
2025/07/02-14:19:04.188380 281473296902840                    Options.recycle_log_file_num: 0
2025/07/02-14:19:04.188381 281473296902840                         Options.allow_fallocate: 1
2025/07/02-14:19:04.188382 281473296902840                        Options.allow_mmap_reads: 0
2025/07/02-14:19:04.188383 281473296902840                       Options.allow_mmap_writes: 0
2025/07/02-14:19:04.188384 281473296902840                        Options.use_direct_reads: 0
2025/07/02-14:19:04.188385 281473296902840                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/02-14:19:04.188386 281473296902840          Options.create_missing_column_families: 1
2025/07/02-14:19:04.188387 281473296902840                              Options.db_log_dir: 
2025/07/02-14:19:04.188388 281473296902840                                 Options.wal_dir: 
2025/07/02-14:19:04.188389 281473296902840                Options.table_cache_numshardbits: 6
2025/07/02-14:19:04.188390 281473296902840                         Options.WAL_ttl_seconds: 0
2025/07/02-14:19:04.188391 281473296902840                       Options.WAL_size_limit_MB: 0
2025/07/02-14:19:04.188392 281473296902840                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/02-14:19:04.188393 281473296902840             Options.manifest_preallocation_size: 4194304
2025/07/02-14:19:04.188394 281473296902840                     Options.is_fd_close_on_exec: 1
2025/07/02-14:19:04.188395 281473296902840                   Options.advise_random_on_open: 1
2025/07/02-14:19:04.188398 281473296902840                    Options.db_write_buffer_size: 0
2025/07/02-14:19:04.188400 281473296902840                    Options.write_buffer_manager: 0xffff87b85860
2025/07/02-14:19:04.188401 281473296902840         Options.access_hint_on_compaction_start: 1
2025/07/02-14:19:04.188402 281473296902840           Options.random_access_max_buffer_size: 1048576
2025/07/02-14:19:04.188403 281473296902840                      Options.use_adaptive_mutex: 0
2025/07/02-14:19:04.188403 281473296902840                            Options.rate_limiter: 0
2025/07/02-14:19:04.188405 281473296902840     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/02-14:19:04.188406 281473296902840                       Options.wal_recovery_mode: 2
2025/07/02-14:19:04.188407 281473296902840                  Options.enable_thread_tracking: 0
2025/07/02-14:19:04.188408 281473296902840                  Options.enable_pipelined_write: 0
2025/07/02-14:19:04.188409 281473296902840                  Options.unordered_write: 0
2025/07/02-14:19:04.188410 281473296902840         Options.allow_concurrent_memtable_write: 1
2025/07/02-14:19:04.188410 281473296902840      Options.enable_write_thread_adaptive_yield: 1
2025/07/02-14:19:04.188411 281473296902840             Options.write_thread_max_yield_usec: 100
2025/07/02-14:19:04.188412 281473296902840            Options.write_thread_slow_yield_usec: 3
2025/07/02-14:19:04.188413 281473296902840                               Options.row_cache: None
2025/07/02-14:19:04.188414 281473296902840                              Options.wal_filter: None
2025/07/02-14:19:04.188415 281473296902840             Options.avoid_flush_during_recovery: 0
2025/07/02-14:19:04.188416 281473296902840             Options.allow_ingest_behind: 0
2025/07/02-14:19:04.188417 281473296902840             Options.two_write_queues: 0
2025/07/02-14:19:04.188418 281473296902840             Options.manual_wal_flush: 0
2025/07/02-14:19:04.188419 281473296902840             Options.wal_compression: 0
2025/07/02-14:19:04.188420 281473296902840             Options.atomic_flush: 0
2025/07/02-14:19:04.188421 281473296902840             Options.avoid_unnecessary_blocking_io: 0
2025/07/02-14:19:04.188422 281473296902840                 Options.persist_stats_to_disk: 0
2025/07/02-14:19:04.188423 281473296902840                 Options.write_dbid_to_manifest: 0
2025/07/02-14:19:04.188424 281473296902840                 Options.log_readahead_size: 0
2025/07/02-14:19:04.188425 281473296902840                 Options.file_checksum_gen_factory: Unknown
2025/07/02-14:19:04.188426 281473296902840                 Options.best_efforts_recovery: 0
2025/07/02-14:19:04.188427 281473296902840                Options.max_bgerror_resume_count: 2147483647
2025/07/02-14:19:04.188428 281473296902840            Options.bgerror_resume_retry_interval: 1000000
2025/07/02-14:19:04.188428 281473296902840             Options.allow_data_in_errors: 0
2025/07/02-14:19:04.188429 281473296902840             Options.db_host_id: __hostname__
2025/07/02-14:19:04.188430 281473296902840             Options.enforce_single_del_contracts: true
2025/07/02-14:19:04.188432 281473296902840             Options.max_background_jobs: 2
2025/07/02-14:19:04.188432 281473296902840             Options.max_background_compactions: -1
2025/07/02-14:19:04.188433 281473296902840             Options.max_subcompactions: 1
2025/07/02-14:19:04.188434 281473296902840             Options.avoid_flush_during_shutdown: 0
2025/07/02-14:19:04.188435 281473296902840           Options.writable_file_max_buffer_size: 1048576
2025/07/02-14:19:04.188436 281473296902840             Options.delayed_write_rate : 16777216
2025/07/02-14:19:04.188437 281473296902840             Options.max_total_wal_size: 1073741824
2025/07/02-14:19:04.188438 281473296902840             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/02-14:19:04.188439 281473296902840                   Options.stats_dump_period_sec: 600
2025/07/02-14:19:04.188460 281473296902840                 Options.stats_persist_period_sec: 600
2025/07/02-14:19:04.188461 281473296902840                 Options.stats_history_buffer_size: 1048576
2025/07/02-14:19:04.188462 281473296902840                          Options.max_open_files: -1
2025/07/02-14:19:04.188463 281473296902840                          Options.bytes_per_sync: 0
2025/07/02-14:19:04.188464 281473296902840                      Options.wal_bytes_per_sync: 0
2025/07/02-14:19:04.188465 281473296902840                   Options.strict_bytes_per_sync: 0
2025/07/02-14:19:04.188466 281473296902840       Options.compaction_readahead_size: 2097152
2025/07/02-14:19:04.188467 281473296902840                  Options.max_background_flushes: -1
2025/07/02-14:19:04.188468 281473296902840 Options.daily_offpeak_time_utc: 
2025/07/02-14:19:04.188469 281473296902840 Compression algorithms supported:
2025/07/02-14:19:04.188470 281473296902840 	kZSTDNotFinalCompression supported: 1
2025/07/02-14:19:04.188472 281473296902840 	kZSTD supported: 1
2025/07/02-14:19:04.188473 281473296902840 	kXpressCompression supported: 0
2025/07/02-14:19:04.188475 281473296902840 	kLZ4HCCompression supported: 1
2025/07/02-14:19:04.188476 281473296902840 	kLZ4Compression supported: 1
2025/07/02-14:19:04.188477 281473296902840 	kBZip2Compression supported: 1
2025/07/02-14:19:04.188478 281473296902840 	kZlibCompression supported: 1
2025/07/02-14:19:04.188479 281473296902840 	kSnappyCompression supported: 1
2025/07/02-14:19:04.188482 281473296902840 Fast CRC32 supported: Supported on Arm64
2025/07/02-14:19:04.188483 281473296902840 DMutex implementation: pthread_mutex_t
2025/07/02-14:19:04.189436 281473296902840 [/version_set.cc:5906] Recovering from manifest file: /home/<USER>/data/protocol/raft/naming_instance_metadata/log/MANIFEST-000043
2025/07/02-14:19:04.189779 281473296902840 [/column_family.cc:618] --------------- Options for column family [default]:
2025/07/02-14:19:04.189782 281473296902840               Options.comparator: leveldb.BytewiseComparator
2025/07/02-14:19:04.189784 281473296902840           Options.merge_operator: StringAppendOperator
2025/07/02-14:19:04.189785 281473296902840        Options.compaction_filter: None
2025/07/02-14:19:04.189786 281473296902840        Options.compaction_filter_factory: None
2025/07/02-14:19:04.189787 281473296902840  Options.sst_partitioner_factory: None
2025/07/02-14:19:04.189788 281473296902840         Options.memtable_factory: SkipListFactory
2025/07/02-14:19:04.189789 281473296902840            Options.table_factory: BlockBasedTable
2025/07/02-14:19:04.189842 281473296902840            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff87ae7310)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff87b851b0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/02-14:19:04.189854 281473296902840        Options.write_buffer_size: 134217728
2025/07/02-14:19:04.189855 281473296902840  Options.max_write_buffer_number: 6
2025/07/02-14:19:04.189856 281473296902840        Options.compression[0]: NoCompression
2025/07/02-14:19:04.189858 281473296902840        Options.compression[1]: NoCompression
2025/07/02-14:19:04.189859 281473296902840        Options.compression[2]: LZ4
2025/07/02-14:19:04.189860 281473296902840        Options.compression[3]: LZ4
2025/07/02-14:19:04.189861 281473296902840        Options.compression[4]: LZ4
2025/07/02-14:19:04.189862 281473296902840        Options.compression[5]: LZ4
2025/07/02-14:19:04.189864 281473296902840        Options.compression[6]: LZ4
2025/07/02-14:19:04.189865 281473296902840                  Options.bottommost_compression: Disabled
2025/07/02-14:19:04.189866 281473296902840       Options.prefix_extractor: rocksdb.FixedPrefix
2025/07/02-14:19:04.189867 281473296902840   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/02-14:19:04.189868 281473296902840             Options.num_levels: 7
2025/07/02-14:19:04.189869 281473296902840        Options.min_write_buffer_number_to_merge: 2
2025/07/02-14:19:04.189870 281473296902840     Options.max_write_buffer_number_to_maintain: 0
2025/07/02-14:19:04.189871 281473296902840     Options.max_write_buffer_size_to_maintain: 0
2025/07/02-14:19:04.189872 281473296902840            Options.bottommost_compression_opts.window_bits: -14
2025/07/02-14:19:04.189873 281473296902840                  Options.bottommost_compression_opts.level: 32767
2025/07/02-14:19:04.189874 281473296902840               Options.bottommost_compression_opts.strategy: 0
2025/07/02-14:19:04.189875 281473296902840         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/02-14:19:04.189876 281473296902840         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:04.189877 281473296902840         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/02-14:19:04.189878 281473296902840                  Options.bottommost_compression_opts.enabled: false
2025/07/02-14:19:04.189879 281473296902840         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:04.189880 281473296902840         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:04.189881 281473296902840            Options.compression_opts.window_bits: -14
2025/07/02-14:19:04.189882 281473296902840                  Options.compression_opts.level: 32767
2025/07/02-14:19:04.189883 281473296902840               Options.compression_opts.strategy: 0
2025/07/02-14:19:04.189884 281473296902840         Options.compression_opts.max_dict_bytes: 0
2025/07/02-14:19:04.189885 281473296902840         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:04.189886 281473296902840         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:04.189887 281473296902840         Options.compression_opts.parallel_threads: 1
2025/07/02-14:19:04.189888 281473296902840                  Options.compression_opts.enabled: false
2025/07/02-14:19:04.189889 281473296902840         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:04.189890 281473296902840      Options.level0_file_num_compaction_trigger: 2
2025/07/02-14:19:04.189891 281473296902840          Options.level0_slowdown_writes_trigger: 20
2025/07/02-14:19:04.189892 281473296902840              Options.level0_stop_writes_trigger: 40
2025/07/02-14:19:04.189893 281473296902840                   Options.target_file_size_base: 67108864
2025/07/02-14:19:04.189894 281473296902840             Options.target_file_size_multiplier: 1
2025/07/02-14:19:04.189895 281473296902840                Options.max_bytes_for_level_base: 536870912
2025/07/02-14:19:04.189896 281473296902840 Options.level_compaction_dynamic_level_bytes: 1
2025/07/02-14:19:04.189896 281473296902840          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/02-14:19:04.189898 281473296902840 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/02-14:19:04.189899 281473296902840 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/02-14:19:04.189900 281473296902840 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/02-14:19:04.189901 281473296902840 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/02-14:19:04.189903 281473296902840 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/02-14:19:04.189904 281473296902840 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/02-14:19:04.189905 281473296902840 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/02-14:19:04.189905 281473296902840       Options.max_sequential_skip_in_iterations: 8
2025/07/02-14:19:04.189906 281473296902840                    Options.max_compaction_bytes: 1677721600
2025/07/02-14:19:04.189907 281473296902840   Options.ignore_max_compaction_bytes_for_input: true
2025/07/02-14:19:04.189908 281473296902840                        Options.arena_block_size: 1048576
2025/07/02-14:19:04.189909 281473296902840   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/02-14:19:04.189910 281473296902840   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/02-14:19:04.189911 281473296902840                Options.disable_auto_compactions: 0
2025/07/02-14:19:04.189913 281473296902840                        Options.compaction_style: kCompactionStyleLevel
2025/07/02-14:19:04.189914 281473296902840                          Options.compaction_pri: kMinOverlappingRatio
2025/07/02-14:19:04.189915 281473296902840 Options.compaction_options_universal.size_ratio: 1
2025/07/02-14:19:04.189916 281473296902840 Options.compaction_options_universal.min_merge_width: 2
2025/07/02-14:19:04.189917 281473296902840 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/02-14:19:04.189918 281473296902840 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/02-14:19:04.189919 281473296902840 Options.compaction_options_universal.compression_size_percent: -1
2025/07/02-14:19:04.189921 281473296902840 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/02-14:19:04.189922 281473296902840 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/02-14:19:04.189923 281473296902840 Options.compaction_options_fifo.allow_compaction: 0
2025/07/02-14:19:04.189927 281473296902840                   Options.table_properties_collectors: 
2025/07/02-14:19:04.189928 281473296902840                   Options.inplace_update_support: 0
2025/07/02-14:19:04.189929 281473296902840                 Options.inplace_update_num_locks: 10000
2025/07/02-14:19:04.189930 281473296902840               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/07/02-14:19:04.189931 281473296902840               Options.memtable_whole_key_filtering: 0
2025/07/02-14:19:04.189932 281473296902840   Options.memtable_huge_page_size: 0
2025/07/02-14:19:04.189933 281473296902840                           Options.bloom_locality: 0
2025/07/02-14:19:04.189934 281473296902840                    Options.max_successive_merges: 0
2025/07/02-14:19:04.189935 281473296902840                Options.optimize_filters_for_hits: 0
2025/07/02-14:19:04.189936 281473296902840                Options.paranoid_file_checks: 0
2025/07/02-14:19:04.189937 281473296902840                Options.force_consistency_checks: 1
2025/07/02-14:19:04.189937 281473296902840                Options.report_bg_io_stats: 0
2025/07/02-14:19:04.189938 281473296902840                               Options.ttl: 2592000
2025/07/02-14:19:04.189939 281473296902840          Options.periodic_compaction_seconds: 0
2025/07/02-14:19:04.189940 281473296902840                        Options.default_temperature: kUnknown
2025/07/02-14:19:04.189941 281473296902840  Options.preclude_last_level_data_seconds: 0
2025/07/02-14:19:04.189942 281473296902840    Options.preserve_internal_time_seconds: 0
2025/07/02-14:19:04.189943 281473296902840                       Options.enable_blob_files: false
2025/07/02-14:19:04.189944 281473296902840                           Options.min_blob_size: 0
2025/07/02-14:19:04.189945 281473296902840                          Options.blob_file_size: 268435456
2025/07/02-14:19:04.189946 281473296902840                   Options.blob_compression_type: NoCompression
2025/07/02-14:19:04.189948 281473296902840          Options.enable_blob_garbage_collection: false
2025/07/02-14:19:04.189949 281473296902840      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/02-14:19:04.189950 281473296902840 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/02-14:19:04.189951 281473296902840          Options.blob_compaction_readahead_size: 0
2025/07/02-14:19:04.189952 281473296902840                Options.blob_file_starting_level: 0
2025/07/02-14:19:04.189953 281473296902840         Options.experimental_mempurge_threshold: 0.000000
2025/07/02-14:19:04.189954 281473296902840            Options.memtable_max_range_deletions: 0
2025/07/02-14:19:04.196728 281473296902840 [/column_family.cc:618] --------------- Options for column family [Configuration]:
2025/07/02-14:19:04.196739 281473296902840               Options.comparator: leveldb.BytewiseComparator
2025/07/02-14:19:04.196741 281473296902840           Options.merge_operator: StringAppendOperator
2025/07/02-14:19:04.196743 281473296902840        Options.compaction_filter: None
2025/07/02-14:19:04.196744 281473296902840        Options.compaction_filter_factory: None
2025/07/02-14:19:04.196745 281473296902840  Options.sst_partitioner_factory: None
2025/07/02-14:19:04.196746 281473296902840         Options.memtable_factory: SkipListFactory
2025/07/02-14:19:04.196748 281473296902840            Options.table_factory: BlockBasedTable
2025/07/02-14:19:04.196832 281473296902840            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff87ae7310)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff87b851b0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/02-14:19:04.196844 281473296902840        Options.write_buffer_size: 134217728
2025/07/02-14:19:04.196845 281473296902840  Options.max_write_buffer_number: 6
2025/07/02-14:19:04.196847 281473296902840        Options.compression[0]: NoCompression
2025/07/02-14:19:04.196848 281473296902840        Options.compression[1]: NoCompression
2025/07/02-14:19:04.196849 281473296902840        Options.compression[2]: LZ4
2025/07/02-14:19:04.196851 281473296902840        Options.compression[3]: LZ4
2025/07/02-14:19:04.196852 281473296902840        Options.compression[4]: LZ4
2025/07/02-14:19:04.196853 281473296902840        Options.compression[5]: LZ4
2025/07/02-14:19:04.196854 281473296902840        Options.compression[6]: LZ4
2025/07/02-14:19:04.196855 281473296902840                  Options.bottommost_compression: Disabled
2025/07/02-14:19:04.196857 281473296902840       Options.prefix_extractor: rocksdb.FixedPrefix
2025/07/02-14:19:04.196858 281473296902840   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/02-14:19:04.196859 281473296902840             Options.num_levels: 7
2025/07/02-14:19:04.196860 281473296902840        Options.min_write_buffer_number_to_merge: 2
2025/07/02-14:19:04.196861 281473296902840     Options.max_write_buffer_number_to_maintain: 0
2025/07/02-14:19:04.196862 281473296902840     Options.max_write_buffer_size_to_maintain: 0
2025/07/02-14:19:04.196863 281473296902840            Options.bottommost_compression_opts.window_bits: -14
2025/07/02-14:19:04.196866 281473296902840                  Options.bottommost_compression_opts.level: 32767
2025/07/02-14:19:04.196867 281473296902840               Options.bottommost_compression_opts.strategy: 0
2025/07/02-14:19:04.196868 281473296902840         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/02-14:19:04.196869 281473296902840         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:04.196871 281473296902840         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/02-14:19:04.196872 281473296902840                  Options.bottommost_compression_opts.enabled: false
2025/07/02-14:19:04.196874 281473296902840         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:04.196875 281473296902840         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:04.196899 281473296902840            Options.compression_opts.window_bits: -14
2025/07/02-14:19:04.196901 281473296902840                  Options.compression_opts.level: 32767
2025/07/02-14:19:04.196902 281473296902840               Options.compression_opts.strategy: 0
2025/07/02-14:19:04.196903 281473296902840         Options.compression_opts.max_dict_bytes: 0
2025/07/02-14:19:04.196904 281473296902840         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:04.196905 281473296902840         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:04.196906 281473296902840         Options.compression_opts.parallel_threads: 1
2025/07/02-14:19:04.196907 281473296902840                  Options.compression_opts.enabled: false
2025/07/02-14:19:04.196909 281473296902840         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:04.196910 281473296902840      Options.level0_file_num_compaction_trigger: 2
2025/07/02-14:19:04.196911 281473296902840          Options.level0_slowdown_writes_trigger: 20
2025/07/02-14:19:04.196912 281473296902840              Options.level0_stop_writes_trigger: 40
2025/07/02-14:19:04.196913 281473296902840                   Options.target_file_size_base: 67108864
2025/07/02-14:19:04.196914 281473296902840             Options.target_file_size_multiplier: 1
2025/07/02-14:19:04.196915 281473296902840                Options.max_bytes_for_level_base: 536870912
2025/07/02-14:19:04.196916 281473296902840 Options.level_compaction_dynamic_level_bytes: 1
2025/07/02-14:19:04.196917 281473296902840          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/02-14:19:04.196919 281473296902840 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/02-14:19:04.196920 281473296902840 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/02-14:19:04.196921 281473296902840 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/02-14:19:04.196922 281473296902840 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/02-14:19:04.196923 281473296902840 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/02-14:19:04.196924 281473296902840 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/02-14:19:04.196925 281473296902840 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/02-14:19:04.196927 281473296902840       Options.max_sequential_skip_in_iterations: 8
2025/07/02-14:19:04.196928 281473296902840                    Options.max_compaction_bytes: 1677721600
2025/07/02-14:19:04.196929 281473296902840   Options.ignore_max_compaction_bytes_for_input: true
2025/07/02-14:19:04.196930 281473296902840                        Options.arena_block_size: 1048576
2025/07/02-14:19:04.196931 281473296902840   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/02-14:19:04.196932 281473296902840   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/02-14:19:04.196933 281473296902840                Options.disable_auto_compactions: 0
2025/07/02-14:19:04.196935 281473296902840                        Options.compaction_style: kCompactionStyleLevel
2025/07/02-14:19:04.196937 281473296902840                          Options.compaction_pri: kMinOverlappingRatio
2025/07/02-14:19:04.196941 281473296902840 Options.compaction_options_universal.size_ratio: 1
2025/07/02-14:19:04.196942 281473296902840 Options.compaction_options_universal.min_merge_width: 2
2025/07/02-14:19:04.196943 281473296902840 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/02-14:19:04.196944 281473296902840 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/02-14:19:04.196945 281473296902840 Options.compaction_options_universal.compression_size_percent: -1
2025/07/02-14:19:04.196946 281473296902840 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/02-14:19:04.196947 281473296902840 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/02-14:19:04.196948 281473296902840 Options.compaction_options_fifo.allow_compaction: 0
2025/07/02-14:19:04.196954 281473296902840                   Options.table_properties_collectors: 
2025/07/02-14:19:04.196955 281473296902840                   Options.inplace_update_support: 0
2025/07/02-14:19:04.196956 281473296902840                 Options.inplace_update_num_locks: 10000
2025/07/02-14:19:04.196957 281473296902840               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/07/02-14:19:04.196958 281473296902840               Options.memtable_whole_key_filtering: 0
2025/07/02-14:19:04.196959 281473296902840   Options.memtable_huge_page_size: 0
2025/07/02-14:19:04.196960 281473296902840                           Options.bloom_locality: 0
2025/07/02-14:19:04.196961 281473296902840                    Options.max_successive_merges: 0
2025/07/02-14:19:04.196962 281473296902840                Options.optimize_filters_for_hits: 0
2025/07/02-14:19:04.196963 281473296902840                Options.paranoid_file_checks: 0
2025/07/02-14:19:04.196964 281473296902840                Options.force_consistency_checks: 1
2025/07/02-14:19:04.196965 281473296902840                Options.report_bg_io_stats: 0
2025/07/02-14:19:04.196966 281473296902840                               Options.ttl: 2592000
2025/07/02-14:19:04.196967 281473296902840          Options.periodic_compaction_seconds: 0
2025/07/02-14:19:04.196968 281473296902840                        Options.default_temperature: kUnknown
2025/07/02-14:19:04.196969 281473296902840  Options.preclude_last_level_data_seconds: 0
2025/07/02-14:19:04.196970 281473296902840    Options.preserve_internal_time_seconds: 0
2025/07/02-14:19:04.196971 281473296902840                       Options.enable_blob_files: false
2025/07/02-14:19:04.196972 281473296902840                           Options.min_blob_size: 0
2025/07/02-14:19:04.196973 281473296902840                          Options.blob_file_size: 268435456
2025/07/02-14:19:04.196974 281473296902840                   Options.blob_compression_type: NoCompression
2025/07/02-14:19:04.196975 281473296902840          Options.enable_blob_garbage_collection: false
2025/07/02-14:19:04.196976 281473296902840      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/02-14:19:04.196977 281473296902840 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/02-14:19:04.196979 281473296902840          Options.blob_compaction_readahead_size: 0
2025/07/02-14:19:04.196980 281473296902840                Options.blob_file_starting_level: 0
2025/07/02-14:19:04.196981 281473296902840         Options.experimental_mempurge_threshold: 0.000000
2025/07/02-14:19:04.196982 281473296902840            Options.memtable_max_range_deletions: 0
2025/07/02-14:19:04.283761 281473296902840 [/version_set.cc:5957] Recovered from manifest file:/home/<USER>/data/protocol/raft/naming_instance_metadata/log/MANIFEST-000043 succeeded,manifest_file_number is 43, next_file_number is 45, last_sequence is 113, log_number is 33,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 33
2025/07/02-14:19:04.283769 281473296902840 [/version_set.cc:5966] Column family [default] (ID 0), log number is 33
2025/07/02-14:19:04.283772 281473296902840 [/version_set.cc:5966] Column family [Configuration] (ID 1), log number is 33
2025/07/02-14:19:04.286598 281473296902840 [/db_impl/db_impl_open.cc:646] DB ID: f5c9aca2-cd6c-48f7-a132-ea8085e321f6
2025/07/02-14:19:04.287476 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437144287473, "job": 1, "event": "recovery_started", "wal_files": [42]}
2025/07/02-14:19:04.287479 281473296902840 [/db_impl/db_impl_open.cc:1145] Recovering log #42 mode 2
2025/07/02-14:19:04.288892 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437144288867, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 46, "file_size": 1067, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 115, "largest_seqno": 115, "table_properties": {"data_size": 0, "index_size": 13, "index_partitions": 0, "top_level_index_size": 8, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 0, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751437144, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "f5c9aca2-cd6c-48f7-a132-ea8085e321f6", "db_session_id": "LR5P8XSGRSWGWUKMX0PS", "orig_file_number": 46, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:19:04.291720 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437144291706, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 47, "file_size": 1218, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 116, "largest_seqno": 123, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 42, "raw_average_key_size": 21, "raw_value_size": 16, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751437144, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "f5c9aca2-cd6c-48f7-a132-ea8085e321f6", "db_session_id": "LR5P8XSGRSWGWUKMX0PS", "orig_file_number": 47, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:19:04.293005 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437144293004, "job": 1, "event": "recovery_finished"}
2025/07/02-14:19:04.293223 281473296902840 [/version_set.cc:5417] Creating manifest 49
2025/07/02-14:19:04.376916 281473296902840 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_instance_metadata/log/000042.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.377062 281473296902840 [/db_impl/db_impl_open.cc:2150] SstFileManager instance 0xffff87ae7ed0
2025/07/02-14:19:04.377133 281472957408744 [/compaction/compaction_job.cc:2080] [default] [JOB 3] Compacting 2@0 files to L6, score 1.00
2025/07/02-14:19:04.377136 281472957408744 [/compaction/compaction_job.cc:2084] [default]: Compaction start summary: Base version 4 Base level 0, inputs: [46(1067B) 40(1067B)]
2025/07/02-14:19:04.377145 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144377141, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [46, 40], "score": 1, "input_data_size": 2134, "oldest_snapshot_seqno": -1}
2025/07/02-14:19:04.377322 281473296902840 DB pointer 0xffff8abfc100
2025/07/02-14:19:04.381067 281472957408744 (Original Log Time 2025/07/02-14:19:04.380741) [/compaction/compaction_job.cc:1733] [default] [JOB 3] Compacted 2@0 files to L6 => 1024 bytes
2025/07/02-14:19:04.381070 281472957408744 (Original Log Time 2025/07/02-14:19:04.381031) [/compaction/compaction_job.cc:926] [default] compacted to: files[0 0 0 0 0 0 0] max score 0.00, MB/sec: 0.6 rd, 0.3 wr, level 6, files in(2, 0) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(1.5) write-amplify(0.5) OK, records in: 2, records dropped: 2 output_compression: NoCompression
2025/07/02-14:19:04.381078 281472957408744 (Original Log Time 2025/07/02-14:19:04.381049) EVENT_LOG_v1 {"time_micros": 1751437144381041, "job": 3, "event": "compaction_finished", "compaction_time_micros": 3405, "compaction_time_cpu_micros": 608, "output_level": 6, "num_output_files": 1, "total_output_size": 1024, "num_input_records": 2, "num_output_records": 0, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 0, 0, 0, 0, 0, 0]}
2025/07/02-14:19:04.381527 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_instance_metadata/log/000040.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.381548 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144381544, "job": 3, "event": "table_file_deletion", "file_number": 40}
2025/07/02-14:19:04.381894 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_instance_metadata/log/000046.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.381910 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144381907, "job": 3, "event": "table_file_deletion", "file_number": 46}
2025/07/02-14:19:04.382105 281472957408744 [/compaction/compaction_job.cc:2080] [Configuration] [JOB 6] Compacting 2@0 + 1@6 files to L6, score 1.00
2025/07/02-14:19:04.382112 281472957408744 [/compaction/compaction_job.cc:2084] [Configuration]: Compaction start summary: Base version 5 Base level 0, inputs: [47(1218B) 41(1218B)], [37(1174B)]
2025/07/02-14:19:04.382124 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144382117, "job": 6, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [47, 41], "files_L6": [37], "score": 1, "input_data_size": 3610, "oldest_snapshot_seqno": -1}
2025/07/02-14:19:04.383935 281472957408744 [/compaction/compaction_job.cc:1661] [Configuration] [JOB 6] Generated table #53: 1 keys, 1174 bytes, temperature: kUnknown
2025/07/02-14:19:04.383988 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144383949, "cf_name": "Configuration", "job": 6, "event": "table_file_creation", "file_number": 53, "file_size": 1174, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 0, "largest_seqno": 0, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 26, "raw_average_key_size": 26, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751248198, "oldest_key_time": 0, "file_creation_time": 1751437144, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "f5c9aca2-cd6c-48f7-a132-ea8085e321f6", "db_session_id": "LR5P8XSGRSWGWUKMX0PS", "orig_file_number": 53, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:19:04.386295 281472957408744 (Original Log Time 2025/07/02-14:19:04.385241) [/compaction/compaction_job.cc:1733] [Configuration] [JOB 6] Compacted 2@0 + 1@6 files to L6 => 1174 bytes
2025/07/02-14:19:04.386300 281472957408744 (Original Log Time 2025/07/02-14:19:04.385940) [/compaction/compaction_job.cc:926] [Configuration] compacted to: base level 6 level multiplier 10.00 max bytes base 536870912 files[0 0 0 0 0 0 1] max score 0.00, MB/sec: 1.8 rd, 0.6 wr, level 6, files in(2, 1) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(2.0) write-amplify(0.5) OK, records in: 5, records dropped: 4 output_compression: NoCompression
2025/07/02-14:19:04.386315 281472957408744 (Original Log Time 2025/07/02-14:19:04.385972) EVENT_LOG_v1 {"time_micros": 1751437144385956, "job": 6, "event": "compaction_finished", "compaction_time_micros": 2000, "compaction_time_cpu_micros": 651, "output_level": 6, "num_output_files": 1, "total_output_size": 1174, "num_input_records": 5, "num_output_records": 1, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 0, 0, 0, 0, 0, 1]}
2025/07/02-14:19:04.386730 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_instance_metadata/log/000037.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.386749 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144386745, "job": 6, "event": "table_file_deletion", "file_number": 37}
2025/07/02-14:19:04.387049 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_instance_metadata/log/000041.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.387069 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144387067, "job": 6, "event": "table_file_deletion", "file_number": 41}
2025/07/02-14:19:04.387417 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_instance_metadata/log/000047.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.387421 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144387421, "job": 6, "event": "table_file_deletion", "file_number": 47}
2025/07/02-14:19:10.377706 281472947103208 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-14:19:10.377790 281472947103208 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 6.2 total, 6.2 interval
Cumulative writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0
  L6      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.6      0.3      0.00              0.00         1    0.003       2      2       0.0       0.0
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.4      0.4      0.00              0.00         2    0.002       2      2       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.4      0.4      0.00              0.00         2    0.002       2      2       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.6      0.3      0.00              0.00         1    0.003       2      2       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6.2 total, 6.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff87b851b0#1 capacity: 8.00 MB seed: 1424354510 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 1 last_copies: 1 last_secs: 2.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 10 Average: 133.4000  StdDev: 386.29
Min: 1  Median: 1.0000  Max: 1292
Percentiles: P50: 1.00 P75: 2.50 P99: 1257.00 P99.9: 1292.00 P99.99: 1292.00
------------------------------------------------------
[       0,       1 ]        5  50.000%  50.000% ##########
(       1,       2 ]        2  20.000%  70.000% ####
(       2,       3 ]        1  10.000%  80.000% ##
(      22,      34 ]        1  10.000%  90.000% ##
(     870,    1300 ]        1  10.000% 100.000% ##


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.2      0.00              0.00         1    0.001       0      0       0.0       0.0
  L6      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      1.7      0.6      0.00              0.00         1    0.002       5      4       0.0       0.0
 Sum      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      1.2      0.8      0.00              0.00         2    0.001       5      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      1.2      0.8      0.00              0.00         2    0.001       5      4       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      1.7      0.6      0.00              0.00         1    0.002       5      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.2      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6.2 total, 6.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff87b851b0#1 capacity: 8.00 MB seed: 1424354510 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 1 last_copies: 1 last_secs: 2.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 14 Average: 72.0714  StdDev: 200.33
Min: 0  Median: 0.8750  Max: 782
Percentiles: P50: 0.88 P75: 38.25 P99: 782.00 P99.9: 782.00 P99.99: 782.00
------------------------------------------------------
[       0,       1 ]        8  57.143%  57.143% ###########
(       1,       2 ]        2  14.286%  71.429% ###
(      34,      51 ]        2  14.286%  85.714% ###
(     110,     170 ]        1   7.143%  92.857% #
(     580,     870 ]        1   7.143% 100.000% #

** Level 6 read latency histogram (micros):
Count: 11 Average: 310.0909  StdDev: 947.10
Min: 0  Median: 0.9167  Max: 3304
Percentiles: P50: 0.92 P75: 7.00 P99: 3304.00 P99.9: 3304.00 P99.99: 3304.00
------------------------------------------------------
[       0,       1 ]        6  54.545%  54.545% ###########
(       1,       2 ]        1   9.091%  63.636% ##
(       2,       3 ]        1   9.091%  72.727% ##
(       6,      10 ]        1   9.091%  81.818% ##
(      76,     110 ]        1   9.091%  90.909% ##
(    2900,    4400 ]        1   9.091% 100.000% ##

2025/07/02-14:19:10.378285 281472947103208 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 5
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 168
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 168
rocksdb.write.self COUNT : 5
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 5
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1259
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 977.500000 P95 : 87518.000000 P99 : 87518.000000 P100 : 87518.000000 COUNT : 5 SUM : 90653
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3405.000000 P99 : 3405.000000 P100 : 3405.000000 COUNT : 2 SUM : 5405
rocksdb.compaction.times.cpu_micros P50 : 651.000000 P95 : 651.000000 P99 : 651.000000 P100 : 651.000000 COUNT : 2 SUM : 1259
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 180.000000 P95 : 180.000000 P99 : 180.000000 P100 : 180.000000 COUNT : 2 SUM : 357
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 750.000000 P99 : 750.000000 P100 : 750.000000 COUNT : 2 SUM : 1100
rocksdb.wal.file.sync.micros P50 : 920.000000 P95 : 920.000000 P99 : 920.000000 P100 : 920.000000 COUNT : 1 SUM : 920
rocksdb.manifest.file.sync.micros P50 : 140.000000 P95 : 425.000000 P99 : 425.000000 P100 : 425.000000 COUNT : 3 SUM : 597
rocksdb.table.open.io.micros P50 : 250.000000 P95 : 3391.000000 P99 : 3391.000000 P100 : 3391.000000 COUNT : 6 SUM : 6240
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 2.375000 P95 : 119.000000 P99 : 148.000000 P100 : 148.000000 COUNT : 17 SUM : 204
rocksdb.write.raw.block.micros P50 : 0.600000 P95 : 605.000000 P99 : 605.000000 P100 : 605.000000 COUNT : 18 SUM : 764
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 0.921053 P95 : 977.500000 P99 : 3304.000000 P100 : 3304.000000 COUNT : 35 SUM : 5754
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 1.500000 P95 : 91.000000 P99 : 91.000000 P100 : 91.000000 COUNT : 5 SUM : 98
rocksdb.file.read.db.open.micros P50 : 0.866667 P95 : 1171.000000 P99 : 3304.000000 P100 : 3304.000000 COUNT : 26 SUM : 5511
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 5 SUM : 168
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-14:19:11.377709 281472947103208 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-14:19:11.377717 281472947103208 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/07/02-14:19:11.377718 281472947103208 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/07/02-14:19:23.687706 281472921503208 [/db_impl/db_impl.cc:487] Shutdown: canceling all background work
2025/07/02-14:19:23.691408 281472921503208 [/db_impl/db_impl.cc:668] Shutdown complete
