2025/07/02-14:19:02.995511 281473296902840 RocksDB version: 8.8.1
2025/07/02-14:19:02.995730 281473296902840 Compile date 2023-11-23 11:07:28
2025/07/02-14:19:02.995753 281473296902840 DB SUMMARY
2025/07/02-14:19:02.995756 281473296902840 Host name (Env):  c12ff0a377ae
2025/07/02-14:19:02.995757 281473296902840 DB Session ID:  LR5P8XSGRSWGWUKMX0PU
2025/07/02-14:19:02.996433 281473296902840 CURRENT file:  CURRENT
2025/07/02-14:19:02.996439 281473296902840 IDENTITY file:  IDENTITY
2025/07/02-14:19:02.996726 281473296902840 MANIFEST file:  MANIFEST-000043 size: 497 Bytes
2025/07/02-14:19:02.996733 281473296902840 SST files in /home/<USER>/data/protocol/raft/naming_persistent_service/log dir, Total Num: 3, files: 000037.sst 000040.sst 000041.sst 
2025/07/02-14:19:02.996736 281473296902840 Write Ahead Log file in /home/<USER>/data/protocol/raft/naming_persistent_service/log: 000042.log size: 329 ; 
2025/07/02-14:19:02.996739 281473296902840                         Options.error_if_exists: 0
2025/07/02-14:19:02.996741 281473296902840                       Options.create_if_missing: 1
2025/07/02-14:19:02.996742 281473296902840                         Options.paranoid_checks: 1
2025/07/02-14:19:02.996743 281473296902840             Options.flush_verify_memtable_count: 1
2025/07/02-14:19:02.996744 281473296902840          Options.compaction_verify_record_count: 1
2025/07/02-14:19:02.996745 281473296902840                               Options.track_and_verify_wals_in_manifest: 0
2025/07/02-14:19:02.996746 281473296902840        Options.verify_sst_unique_id_in_manifest: 1
2025/07/02-14:19:02.996748 281473296902840                                     Options.env: 0xffff8920e4d0
2025/07/02-14:19:02.996749 281473296902840                                      Options.fs: PosixFileSystem
2025/07/02-14:19:02.996750 281473296902840                                Options.info_log: 0xffff8ce0ac60
2025/07/02-14:19:02.996751 281473296902840                Options.max_file_opening_threads: 16
2025/07/02-14:19:02.996752 281473296902840                              Options.statistics: 0xffff8920c190
2025/07/02-14:19:02.996753 281473296902840                              Options.statistics stats level: 3
2025/07/02-14:19:02.996754 281473296902840                               Options.use_fsync: 0
2025/07/02-14:19:02.996756 281473296902840                       Options.max_log_file_size: 0
2025/07/02-14:19:02.996757 281473296902840                  Options.max_manifest_file_size: 1073741824
2025/07/02-14:19:02.996758 281473296902840                   Options.log_file_time_to_roll: 0
2025/07/02-14:19:02.996759 281473296902840                       Options.keep_log_file_num: 100
2025/07/02-14:19:02.996760 281473296902840                    Options.recycle_log_file_num: 0
2025/07/02-14:19:02.996761 281473296902840                         Options.allow_fallocate: 1
2025/07/02-14:19:02.996762 281473296902840                        Options.allow_mmap_reads: 0
2025/07/02-14:19:02.996763 281473296902840                       Options.allow_mmap_writes: 0
2025/07/02-14:19:02.996764 281473296902840                        Options.use_direct_reads: 0
2025/07/02-14:19:02.996765 281473296902840                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/02-14:19:02.996766 281473296902840          Options.create_missing_column_families: 1
2025/07/02-14:19:02.996767 281473296902840                              Options.db_log_dir: 
2025/07/02-14:19:02.996768 281473296902840                                 Options.wal_dir: 
2025/07/02-14:19:02.996769 281473296902840                Options.table_cache_numshardbits: 6
2025/07/02-14:19:02.996770 281473296902840                         Options.WAL_ttl_seconds: 0
2025/07/02-14:19:02.996771 281473296902840                       Options.WAL_size_limit_MB: 0
2025/07/02-14:19:02.996772 281473296902840                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/02-14:19:02.996773 281473296902840             Options.manifest_preallocation_size: 4194304
2025/07/02-14:19:02.996774 281473296902840                     Options.is_fd_close_on_exec: 1
2025/07/02-14:19:02.996775 281473296902840                   Options.advise_random_on_open: 1
2025/07/02-14:19:02.996782 281473296902840                    Options.db_write_buffer_size: 0
2025/07/02-14:19:02.996783 281473296902840                    Options.write_buffer_manager: 0xffff8920ca90
2025/07/02-14:19:02.996784 281473296902840         Options.access_hint_on_compaction_start: 1
2025/07/02-14:19:02.996785 281473296902840           Options.random_access_max_buffer_size: 1048576
2025/07/02-14:19:02.996786 281473296902840                      Options.use_adaptive_mutex: 0
2025/07/02-14:19:02.996787 281473296902840                            Options.rate_limiter: 0
2025/07/02-14:19:02.996789 281473296902840     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/02-14:19:02.996790 281473296902840                       Options.wal_recovery_mode: 2
2025/07/02-14:19:02.996791 281473296902840                  Options.enable_thread_tracking: 0
2025/07/02-14:19:02.996792 281473296902840                  Options.enable_pipelined_write: 0
2025/07/02-14:19:02.996793 281473296902840                  Options.unordered_write: 0
2025/07/02-14:19:02.996795 281473296902840         Options.allow_concurrent_memtable_write: 1
2025/07/02-14:19:02.996796 281473296902840      Options.enable_write_thread_adaptive_yield: 1
2025/07/02-14:19:02.996797 281473296902840             Options.write_thread_max_yield_usec: 100
2025/07/02-14:19:02.996798 281473296902840            Options.write_thread_slow_yield_usec: 3
2025/07/02-14:19:02.996799 281473296902840                               Options.row_cache: None
2025/07/02-14:19:02.996800 281473296902840                              Options.wal_filter: None
2025/07/02-14:19:02.996801 281473296902840             Options.avoid_flush_during_recovery: 0
2025/07/02-14:19:02.996802 281473296902840             Options.allow_ingest_behind: 0
2025/07/02-14:19:02.996803 281473296902840             Options.two_write_queues: 0
2025/07/02-14:19:02.996804 281473296902840             Options.manual_wal_flush: 0
2025/07/02-14:19:02.996805 281473296902840             Options.wal_compression: 0
2025/07/02-14:19:02.996806 281473296902840             Options.atomic_flush: 0
2025/07/02-14:19:02.996807 281473296902840             Options.avoid_unnecessary_blocking_io: 0
2025/07/02-14:19:02.996808 281473296902840                 Options.persist_stats_to_disk: 0
2025/07/02-14:19:02.996809 281473296902840                 Options.write_dbid_to_manifest: 0
2025/07/02-14:19:02.996810 281473296902840                 Options.log_readahead_size: 0
2025/07/02-14:19:02.996811 281473296902840                 Options.file_checksum_gen_factory: Unknown
2025/07/02-14:19:02.996812 281473296902840                 Options.best_efforts_recovery: 0
2025/07/02-14:19:02.996813 281473296902840                Options.max_bgerror_resume_count: 2147483647
2025/07/02-14:19:02.996814 281473296902840            Options.bgerror_resume_retry_interval: 1000000
2025/07/02-14:19:02.996815 281473296902840             Options.allow_data_in_errors: 0
2025/07/02-14:19:02.996816 281473296902840             Options.db_host_id: __hostname__
2025/07/02-14:19:02.996817 281473296902840             Options.enforce_single_del_contracts: true
2025/07/02-14:19:02.996818 281473296902840             Options.max_background_jobs: 2
2025/07/02-14:19:02.996819 281473296902840             Options.max_background_compactions: -1
2025/07/02-14:19:02.996820 281473296902840             Options.max_subcompactions: 1
2025/07/02-14:19:02.996821 281473296902840             Options.avoid_flush_during_shutdown: 0
2025/07/02-14:19:02.996822 281473296902840           Options.writable_file_max_buffer_size: 1048576
2025/07/02-14:19:02.996825 281473296902840             Options.delayed_write_rate : 16777216
2025/07/02-14:19:02.996826 281473296902840             Options.max_total_wal_size: 1073741824
2025/07/02-14:19:02.996827 281473296902840             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/02-14:19:02.996828 281473296902840                   Options.stats_dump_period_sec: 600
2025/07/02-14:19:02.996832 281473296902840                 Options.stats_persist_period_sec: 600
2025/07/02-14:19:02.996834 281473296902840                 Options.stats_history_buffer_size: 1048576
2025/07/02-14:19:02.996835 281473296902840                          Options.max_open_files: -1
2025/07/02-14:19:02.996836 281473296902840                          Options.bytes_per_sync: 0
2025/07/02-14:19:02.996837 281473296902840                      Options.wal_bytes_per_sync: 0
2025/07/02-14:19:02.996838 281473296902840                   Options.strict_bytes_per_sync: 0
2025/07/02-14:19:02.996839 281473296902840       Options.compaction_readahead_size: 2097152
2025/07/02-14:19:02.996840 281473296902840                  Options.max_background_flushes: -1
2025/07/02-14:19:02.996841 281473296902840 Options.daily_offpeak_time_utc: 
2025/07/02-14:19:02.996842 281473296902840 Compression algorithms supported:
2025/07/02-14:19:02.996843 281473296902840 	kZSTDNotFinalCompression supported: 1
2025/07/02-14:19:02.996876 281473296902840 	kZSTD supported: 1
2025/07/02-14:19:02.996878 281473296902840 	kXpressCompression supported: 0
2025/07/02-14:19:02.996879 281473296902840 	kLZ4HCCompression supported: 1
2025/07/02-14:19:02.996880 281473296902840 	kLZ4Compression supported: 1
2025/07/02-14:19:02.996882 281473296902840 	kBZip2Compression supported: 1
2025/07/02-14:19:02.996884 281473296902840 	kZlibCompression supported: 1
2025/07/02-14:19:02.996885 281473296902840 	kSnappyCompression supported: 1
2025/07/02-14:19:02.996890 281473296902840 Fast CRC32 supported: Supported on Arm64
2025/07/02-14:19:02.996892 281473296902840 DMutex implementation: pthread_mutex_t
2025/07/02-14:19:02.998259 281473296902840 [/version_set.cc:5906] Recovering from manifest file: /home/<USER>/data/protocol/raft/naming_persistent_service/log/MANIFEST-000043
2025/07/02-14:19:02.998826 281473296902840 [/column_family.cc:618] --------------- Options for column family [default]:
2025/07/02-14:19:02.998832 281473296902840               Options.comparator: leveldb.BytewiseComparator
2025/07/02-14:19:02.998834 281473296902840           Options.merge_operator: StringAppendOperator
2025/07/02-14:19:02.998836 281473296902840        Options.compaction_filter: None
2025/07/02-14:19:02.998837 281473296902840        Options.compaction_filter_factory: None
2025/07/02-14:19:02.998838 281473296902840  Options.sst_partitioner_factory: None
2025/07/02-14:19:02.998839 281473296902840         Options.memtable_factory: SkipListFactory
2025/07/02-14:19:02.998841 281473296902840            Options.table_factory: BlockBasedTable
2025/07/02-14:19:02.998889 281473296902840            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff89218d30)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8920c3f0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/02-14:19:02.998896 281473296902840        Options.write_buffer_size: 134217728
2025/07/02-14:19:02.998897 281473296902840  Options.max_write_buffer_number: 6
2025/07/02-14:19:02.998898 281473296902840        Options.compression[0]: NoCompression
2025/07/02-14:19:02.998900 281473296902840        Options.compression[1]: NoCompression
2025/07/02-14:19:02.998901 281473296902840        Options.compression[2]: LZ4
2025/07/02-14:19:02.998902 281473296902840        Options.compression[3]: LZ4
2025/07/02-14:19:02.998903 281473296902840        Options.compression[4]: LZ4
2025/07/02-14:19:02.998904 281473296902840        Options.compression[5]: LZ4
2025/07/02-14:19:02.998905 281473296902840        Options.compression[6]: LZ4
2025/07/02-14:19:02.998906 281473296902840                  Options.bottommost_compression: Disabled
2025/07/02-14:19:02.998908 281473296902840       Options.prefix_extractor: rocksdb.FixedPrefix
2025/07/02-14:19:02.998909 281473296902840   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/02-14:19:02.998910 281473296902840             Options.num_levels: 7
2025/07/02-14:19:02.998911 281473296902840        Options.min_write_buffer_number_to_merge: 2
2025/07/02-14:19:02.998912 281473296902840     Options.max_write_buffer_number_to_maintain: 0
2025/07/02-14:19:02.998912 281473296902840     Options.max_write_buffer_size_to_maintain: 0
2025/07/02-14:19:02.998913 281473296902840            Options.bottommost_compression_opts.window_bits: -14
2025/07/02-14:19:02.998915 281473296902840                  Options.bottommost_compression_opts.level: 32767
2025/07/02-14:19:02.998915 281473296902840               Options.bottommost_compression_opts.strategy: 0
2025/07/02-14:19:02.998916 281473296902840         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/02-14:19:02.998917 281473296902840         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:02.998918 281473296902840         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/02-14:19:02.998919 281473296902840                  Options.bottommost_compression_opts.enabled: false
2025/07/02-14:19:02.998920 281473296902840         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:02.998921 281473296902840         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:02.998922 281473296902840            Options.compression_opts.window_bits: -14
2025/07/02-14:19:02.998923 281473296902840                  Options.compression_opts.level: 32767
2025/07/02-14:19:02.998924 281473296902840               Options.compression_opts.strategy: 0
2025/07/02-14:19:02.998925 281473296902840         Options.compression_opts.max_dict_bytes: 0
2025/07/02-14:19:02.998926 281473296902840         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:02.998927 281473296902840         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:02.998928 281473296902840         Options.compression_opts.parallel_threads: 1
2025/07/02-14:19:02.998929 281473296902840                  Options.compression_opts.enabled: false
2025/07/02-14:19:02.998930 281473296902840         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:02.998931 281473296902840      Options.level0_file_num_compaction_trigger: 2
2025/07/02-14:19:02.998932 281473296902840          Options.level0_slowdown_writes_trigger: 20
2025/07/02-14:19:02.998933 281473296902840              Options.level0_stop_writes_trigger: 40
2025/07/02-14:19:02.998934 281473296902840                   Options.target_file_size_base: 67108864
2025/07/02-14:19:02.998935 281473296902840             Options.target_file_size_multiplier: 1
2025/07/02-14:19:02.998936 281473296902840                Options.max_bytes_for_level_base: 536870912
2025/07/02-14:19:02.998937 281473296902840 Options.level_compaction_dynamic_level_bytes: 1
2025/07/02-14:19:02.998938 281473296902840          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/02-14:19:02.998939 281473296902840 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/02-14:19:02.998940 281473296902840 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/02-14:19:02.998941 281473296902840 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/02-14:19:02.998942 281473296902840 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/02-14:19:02.998944 281473296902840 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/02-14:19:02.998945 281473296902840 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/02-14:19:02.998947 281473296902840 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/02-14:19:02.998948 281473296902840       Options.max_sequential_skip_in_iterations: 8
2025/07/02-14:19:02.998949 281473296902840                    Options.max_compaction_bytes: 1677721600
2025/07/02-14:19:02.998950 281473296902840   Options.ignore_max_compaction_bytes_for_input: true
2025/07/02-14:19:02.998951 281473296902840                        Options.arena_block_size: 1048576
2025/07/02-14:19:02.998952 281473296902840   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/02-14:19:02.998953 281473296902840   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/02-14:19:02.998954 281473296902840                Options.disable_auto_compactions: 0
2025/07/02-14:19:02.998956 281473296902840                        Options.compaction_style: kCompactionStyleLevel
2025/07/02-14:19:02.998958 281473296902840                          Options.compaction_pri: kMinOverlappingRatio
2025/07/02-14:19:02.998959 281473296902840 Options.compaction_options_universal.size_ratio: 1
2025/07/02-14:19:02.998960 281473296902840 Options.compaction_options_universal.min_merge_width: 2
2025/07/02-14:19:02.998961 281473296902840 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/02-14:19:02.998962 281473296902840 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/02-14:19:02.998963 281473296902840 Options.compaction_options_universal.compression_size_percent: -1
2025/07/02-14:19:02.998964 281473296902840 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/02-14:19:02.998965 281473296902840 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/02-14:19:02.998966 281473296902840 Options.compaction_options_fifo.allow_compaction: 0
2025/07/02-14:19:02.998971 281473296902840                   Options.table_properties_collectors: 
2025/07/02-14:19:02.998972 281473296902840                   Options.inplace_update_support: 0
2025/07/02-14:19:02.998973 281473296902840                 Options.inplace_update_num_locks: 10000
2025/07/02-14:19:02.998974 281473296902840               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/07/02-14:19:02.998975 281473296902840               Options.memtable_whole_key_filtering: 0
2025/07/02-14:19:02.998976 281473296902840   Options.memtable_huge_page_size: 0
2025/07/02-14:19:02.998977 281473296902840                           Options.bloom_locality: 0
2025/07/02-14:19:02.998978 281473296902840                    Options.max_successive_merges: 0
2025/07/02-14:19:02.998979 281473296902840                Options.optimize_filters_for_hits: 0
2025/07/02-14:19:02.998980 281473296902840                Options.paranoid_file_checks: 0
2025/07/02-14:19:02.998981 281473296902840                Options.force_consistency_checks: 1
2025/07/02-14:19:02.998982 281473296902840                Options.report_bg_io_stats: 0
2025/07/02-14:19:02.998983 281473296902840                               Options.ttl: 2592000
2025/07/02-14:19:02.998984 281473296902840          Options.periodic_compaction_seconds: 0
2025/07/02-14:19:02.998985 281473296902840                        Options.default_temperature: kUnknown
2025/07/02-14:19:02.998986 281473296902840  Options.preclude_last_level_data_seconds: 0
2025/07/02-14:19:02.998987 281473296902840    Options.preserve_internal_time_seconds: 0
2025/07/02-14:19:02.998988 281473296902840                       Options.enable_blob_files: false
2025/07/02-14:19:02.998989 281473296902840                           Options.min_blob_size: 0
2025/07/02-14:19:02.998990 281473296902840                          Options.blob_file_size: 268435456
2025/07/02-14:19:02.998991 281473296902840                   Options.blob_compression_type: NoCompression
2025/07/02-14:19:02.998993 281473296902840          Options.enable_blob_garbage_collection: false
2025/07/02-14:19:02.998994 281473296902840      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/02-14:19:02.998995 281473296902840 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/02-14:19:02.998996 281473296902840          Options.blob_compaction_readahead_size: 0
2025/07/02-14:19:02.998997 281473296902840                Options.blob_file_starting_level: 0
2025/07/02-14:19:02.998998 281473296902840         Options.experimental_mempurge_threshold: 0.000000
2025/07/02-14:19:02.998999 281473296902840            Options.memtable_max_range_deletions: 0
2025/07/02-14:19:03.003764 281473296902840 [/column_family.cc:618] --------------- Options for column family [Configuration]:
2025/07/02-14:19:03.003771 281473296902840               Options.comparator: leveldb.BytewiseComparator
2025/07/02-14:19:03.003772 281473296902840           Options.merge_operator: StringAppendOperator
2025/07/02-14:19:03.003773 281473296902840        Options.compaction_filter: None
2025/07/02-14:19:03.003774 281473296902840        Options.compaction_filter_factory: None
2025/07/02-14:19:03.003774 281473296902840  Options.sst_partitioner_factory: None
2025/07/02-14:19:03.003775 281473296902840         Options.memtable_factory: SkipListFactory
2025/07/02-14:19:03.003776 281473296902840            Options.table_factory: BlockBasedTable
2025/07/02-14:19:03.003806 281473296902840            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff89218d30)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8920c3f0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/02-14:19:03.003811 281473296902840        Options.write_buffer_size: 134217728
2025/07/02-14:19:03.003811 281473296902840  Options.max_write_buffer_number: 6
2025/07/02-14:19:03.003812 281473296902840        Options.compression[0]: NoCompression
2025/07/02-14:19:03.003813 281473296902840        Options.compression[1]: NoCompression
2025/07/02-14:19:03.003813 281473296902840        Options.compression[2]: LZ4
2025/07/02-14:19:03.003814 281473296902840        Options.compression[3]: LZ4
2025/07/02-14:19:03.003815 281473296902840        Options.compression[4]: LZ4
2025/07/02-14:19:03.003873 281473296902840        Options.compression[5]: LZ4
2025/07/02-14:19:03.003878 281473296902840        Options.compression[6]: LZ4
2025/07/02-14:19:03.003882 281473296902840                  Options.bottommost_compression: Disabled
2025/07/02-14:19:03.003884 281473296902840       Options.prefix_extractor: rocksdb.FixedPrefix
2025/07/02-14:19:03.003885 281473296902840   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/02-14:19:03.003886 281473296902840             Options.num_levels: 7
2025/07/02-14:19:03.003887 281473296902840        Options.min_write_buffer_number_to_merge: 2
2025/07/02-14:19:03.003888 281473296902840     Options.max_write_buffer_number_to_maintain: 0
2025/07/02-14:19:03.003889 281473296902840     Options.max_write_buffer_size_to_maintain: 0
2025/07/02-14:19:03.003891 281473296902840            Options.bottommost_compression_opts.window_bits: -14
2025/07/02-14:19:03.003914 281473296902840                  Options.bottommost_compression_opts.level: 32767
2025/07/02-14:19:03.003915 281473296902840               Options.bottommost_compression_opts.strategy: 0
2025/07/02-14:19:03.003916 281473296902840         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/02-14:19:03.003917 281473296902840         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:03.003918 281473296902840         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/02-14:19:03.003919 281473296902840                  Options.bottommost_compression_opts.enabled: false
2025/07/02-14:19:03.003921 281473296902840         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:03.003922 281473296902840         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:03.003923 281473296902840            Options.compression_opts.window_bits: -14
2025/07/02-14:19:03.003924 281473296902840                  Options.compression_opts.level: 32767
2025/07/02-14:19:03.003925 281473296902840               Options.compression_opts.strategy: 0
2025/07/02-14:19:03.003926 281473296902840         Options.compression_opts.max_dict_bytes: 0
2025/07/02-14:19:03.003927 281473296902840         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:03.003928 281473296902840         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:03.003929 281473296902840         Options.compression_opts.parallel_threads: 1
2025/07/02-14:19:03.003930 281473296902840                  Options.compression_opts.enabled: false
2025/07/02-14:19:03.003931 281473296902840         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:03.003932 281473296902840      Options.level0_file_num_compaction_trigger: 2
2025/07/02-14:19:03.003934 281473296902840          Options.level0_slowdown_writes_trigger: 20
2025/07/02-14:19:03.003936 281473296902840              Options.level0_stop_writes_trigger: 40
2025/07/02-14:19:03.003937 281473296902840                   Options.target_file_size_base: 67108864
2025/07/02-14:19:03.003938 281473296902840             Options.target_file_size_multiplier: 1
2025/07/02-14:19:03.003939 281473296902840                Options.max_bytes_for_level_base: 536870912
2025/07/02-14:19:03.003940 281473296902840 Options.level_compaction_dynamic_level_bytes: 1
2025/07/02-14:19:03.003941 281473296902840          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/02-14:19:03.003944 281473296902840 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/02-14:19:03.003945 281473296902840 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/02-14:19:03.003946 281473296902840 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/02-14:19:03.003947 281473296902840 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/02-14:19:03.003948 281473296902840 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/02-14:19:03.003949 281473296902840 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/02-14:19:03.003950 281473296902840 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/02-14:19:03.003951 281473296902840       Options.max_sequential_skip_in_iterations: 8
2025/07/02-14:19:03.003952 281473296902840                    Options.max_compaction_bytes: 1677721600
2025/07/02-14:19:03.003953 281473296902840   Options.ignore_max_compaction_bytes_for_input: true
2025/07/02-14:19:03.003954 281473296902840                        Options.arena_block_size: 1048576
2025/07/02-14:19:03.003955 281473296902840   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/02-14:19:03.003956 281473296902840   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/02-14:19:03.003957 281473296902840                Options.disable_auto_compactions: 0
2025/07/02-14:19:03.004003 281473296902840                        Options.compaction_style: kCompactionStyleLevel
2025/07/02-14:19:03.004008 281473296902840                          Options.compaction_pri: kMinOverlappingRatio
2025/07/02-14:19:03.004008 281473296902840 Options.compaction_options_universal.size_ratio: 1
2025/07/02-14:19:03.004009 281473296902840 Options.compaction_options_universal.min_merge_width: 2
2025/07/02-14:19:03.004010 281473296902840 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/02-14:19:03.004010 281473296902840 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/02-14:19:03.004011 281473296902840 Options.compaction_options_universal.compression_size_percent: -1
2025/07/02-14:19:03.004012 281473296902840 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/02-14:19:03.004012 281473296902840 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/02-14:19:03.004013 281473296902840 Options.compaction_options_fifo.allow_compaction: 0
2025/07/02-14:19:03.004053 281473296902840                   Options.table_properties_collectors: 
2025/07/02-14:19:03.004089 281473296902840                   Options.inplace_update_support: 0
2025/07/02-14:19:03.004091 281473296902840                 Options.inplace_update_num_locks: 10000
2025/07/02-14:19:03.004092 281473296902840               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/07/02-14:19:03.004094 281473296902840               Options.memtable_whole_key_filtering: 0
2025/07/02-14:19:03.004095 281473296902840   Options.memtable_huge_page_size: 0
2025/07/02-14:19:03.004096 281473296902840                           Options.bloom_locality: 0
2025/07/02-14:19:03.004097 281473296902840                    Options.max_successive_merges: 0
2025/07/02-14:19:03.004098 281473296902840                Options.optimize_filters_for_hits: 0
2025/07/02-14:19:03.004099 281473296902840                Options.paranoid_file_checks: 0
2025/07/02-14:19:03.004100 281473296902840                Options.force_consistency_checks: 1
2025/07/02-14:19:03.004101 281473296902840                Options.report_bg_io_stats: 0
2025/07/02-14:19:03.004102 281473296902840                               Options.ttl: 2592000
2025/07/02-14:19:03.004103 281473296902840          Options.periodic_compaction_seconds: 0
2025/07/02-14:19:03.004104 281473296902840                        Options.default_temperature: kUnknown
2025/07/02-14:19:03.004106 281473296902840  Options.preclude_last_level_data_seconds: 0
2025/07/02-14:19:03.004106 281473296902840    Options.preserve_internal_time_seconds: 0
2025/07/02-14:19:03.004107 281473296902840                       Options.enable_blob_files: false
2025/07/02-14:19:03.004109 281473296902840                           Options.min_blob_size: 0
2025/07/02-14:19:03.004110 281473296902840                          Options.blob_file_size: 268435456
2025/07/02-14:19:03.004111 281473296902840                   Options.blob_compression_type: NoCompression
2025/07/02-14:19:03.004112 281473296902840          Options.enable_blob_garbage_collection: false
2025/07/02-14:19:03.004113 281473296902840      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/02-14:19:03.004114 281473296902840 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/02-14:19:03.004115 281473296902840          Options.blob_compaction_readahead_size: 0
2025/07/02-14:19:03.004116 281473296902840                Options.blob_file_starting_level: 0
2025/07/02-14:19:03.004117 281473296902840         Options.experimental_mempurge_threshold: 0.000000
2025/07/02-14:19:03.004118 281473296902840            Options.memtable_max_range_deletions: 0
2025/07/02-14:19:03.011265 281473296902840 [/version_set.cc:5957] Recovered from manifest file:/home/<USER>/data/protocol/raft/naming_persistent_service/log/MANIFEST-000043 succeeded,manifest_file_number is 43, next_file_number is 45, last_sequence is 113, log_number is 33,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 33
2025/07/02-14:19:03.011270 281473296902840 [/version_set.cc:5966] Column family [default] (ID 0), log number is 33
2025/07/02-14:19:03.011272 281473296902840 [/version_set.cc:5966] Column family [Configuration] (ID 1), log number is 33
2025/07/02-14:19:03.011565 281473296902840 [/db_impl/db_impl_open.cc:646] DB ID: d6c423a5-bd84-4148-9118-344542ec36da
2025/07/02-14:19:03.012194 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437143012187, "job": 1, "event": "recovery_started", "wal_files": [42]}
2025/07/02-14:19:03.012201 281473296902840 [/db_impl/db_impl_open.cc:1145] Recovering log #42 mode 2
2025/07/02-14:19:03.013044 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437143013031, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 46, "file_size": 1067, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 115, "largest_seqno": 115, "table_properties": {"data_size": 0, "index_size": 13, "index_partitions": 0, "top_level_index_size": 8, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 0, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751437143, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "d6c423a5-bd84-4148-9118-344542ec36da", "db_session_id": "LR5P8XSGRSWGWUKMX0PU", "orig_file_number": 46, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:19:03.015619 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437143015608, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 47, "file_size": 1218, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 116, "largest_seqno": 120, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 42, "raw_average_key_size": 21, "raw_value_size": 16, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751437143, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "d6c423a5-bd84-4148-9118-344542ec36da", "db_session_id": "LR5P8XSGRSWGWUKMX0PU", "orig_file_number": 47, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:19:03.016825 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437143016824, "job": 1, "event": "recovery_finished"}
2025/07/02-14:19:03.016983 281473296902840 [/version_set.cc:5417] Creating manifest 49
2025/07/02-14:19:03.019871 281473296902840 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service/log/000042.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:03.019983 281473296902840 [/db_impl/db_impl_open.cc:2150] SstFileManager instance 0xffff88ee8bf0
2025/07/02-14:19:03.020039 281472957408744 [/compaction/compaction_job.cc:2080] [default] [JOB 3] Compacting 2@0 files to L6, score 1.00
2025/07/02-14:19:03.020044 281472957408744 [/compaction/compaction_job.cc:2084] [default]: Compaction start summary: Base version 4 Base level 0, inputs: [46(1067B) 40(1067B)]
2025/07/02-14:19:03.020057 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437143020046, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [46, 40], "score": 1, "input_data_size": 2134, "oldest_snapshot_seqno": -1}
2025/07/02-14:19:03.020173 281473296902840 DB pointer 0xffff87191200
2025/07/02-14:19:03.020464 281472947103208 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-14:19:03.020469 281472947103208 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/2    2.08 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.8      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      2/2    2.08 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.8      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.8      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.8      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff8920c3f0#1 capacity: 8.00 MB seed: 1424354510 usage: 1.33 KB table_size: 256 occupancy: 7 collections: 1 last_copies: 1 last_secs: 1.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(4,0.50 KB,0.00610352%) IndexBlock(2,0.24 KB,0.00293255%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 10 Average: 35.4000  StdDev: 100.26
Min: 0  Median: 0.7143  Max: 336
Percentiles: P50: 0.71 P75: 1.50 P99: 336.00 P99.9: 336.00 P99.99: 336.00
------------------------------------------------------
[       0,       1 ]        7  70.000%  70.000% ##############
(       1,       2 ]        1  10.000%  80.000% ##
(      10,      15 ]        1  10.000%  90.000% ##
(     250,     380 ]        1  10.000% 100.000% ##


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.38 KB   1.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0
  L6      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0    3.53 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.07 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.07 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff8920c3f0#1 capacity: 8.00 MB seed: 1424354510 usage: 1.33 KB table_size: 256 occupancy: 7 collections: 1 last_copies: 1 last_secs: 1.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(4,0.50 KB,0.00610352%) IndexBlock(2,0.24 KB,0.00293255%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 12 Average: 74.0833  StdDev: 231.05
Min: 0  Median: 0.7500  Max: 840
Percentiles: P50: 0.75 P75: 3.00 P99: 835.20 P99.9: 840.00 P99.99: 840.00
------------------------------------------------------
[       0,       1 ]        8  66.667%  66.667% #############
(       2,       3 ]        1   8.333%  75.000% ##
(      15,      22 ]        2  16.667%  91.667% ###
(     580,     870 ]        1   8.333% 100.000% ##

** Level 6 read latency histogram (micros):
Count: 4 Average: 207.0000  StdDev: 357.96
Min: 0  Median: 0.6667  Max: 827
Percentiles: P50: 0.67 P75: 1.00 P99: 827.00 P99.9: 827.00 P99.99: 827.00
------------------------------------------------------
[       0,       1 ]        3  75.000%  75.000% ###############
(     580,     870 ]        1  25.000% 100.000% #####

2025/07/02-14:19:03.020976 281472947103208 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 6
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 6
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 2
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 2
rocksdb.block.cache.index.bytes.insert COUNT : 246
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 4
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 4
rocksdb.block.cache.data.bytes.insert COUNT : 512
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 758
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 1
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 1
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 0
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 0
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 0
rocksdb.number.db.next COUNT : 0
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 0
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 0
rocksdb.no.file.opens COUNT : 5
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 0
rocksdb.wal.bytes COUNT : 0
rocksdb.write.self COUNT : 0
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 0
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2285
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 0
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 0
rocksdb.num.iterator.deleted COUNT : 0
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 1089
rocksdb.last.level.read.count COUNT : 4
rocksdb.non.last.level.read.bytes COUNT : 6836
rocksdb.non.last.level.read.count COUNT : 22
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 0
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 0
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 16
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 121
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 5
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 70
rocksdb.prefetch.hits COUNT : 2
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 110.000000 P95 : 123.000000 P99 : 123.000000 P100 : 123.000000 COUNT : 2 SUM : 231
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.manifest.file.sync.micros P50 : 55.000000 P95 : 55.000000 P99 : 55.000000 P100 : 55.000000 COUNT : 1 SUM : 55
rocksdb.table.open.io.micros P50 : 725.000000 P95 : 953.000000 P99 : 953.000000 P100 : 953.000000 COUNT : 5 SUM : 2656
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 1.375000 P95 : 53.000000 P99 : 53.000000 P100 : 53.000000 COUNT : 11 SUM : 73
rocksdb.write.raw.block.micros P50 : 0.541667 P95 : 81.000000 P99 : 81.000000 P100 : 81.000000 COUNT : 13 SUM : 86
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.000000 P99 : 2.000000 P100 : 2.000000 COUNT : 1 SUM : 2
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 0.722222 P95 : 681.500000 P99 : 832.300000 P100 : 840.000000 COUNT : 26 SUM : 2071
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.open.micros P50 : 0.722222 P95 : 681.500000 P99 : 832.300000 P100 : 840.000000 COUNT : 26 SUM : 2071
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1133.000000 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 2 SUM : 2266
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-14:19:03.021932 281472957408744 (Original Log Time 2025/07/02-14:19:03.021522) [/compaction/compaction_job.cc:1733] [default] [JOB 3] Compacted 2@0 files to L6 => 1024 bytes
2025/07/02-14:19:03.021933 281472957408744 (Original Log Time 2025/07/02-14:19:03.021892) [/compaction/compaction_job.cc:926] [default] compacted to: files[0 0 0 0 0 0 0] max score 0.00, MB/sec: 1.6 rd, 0.8 wr, level 6, files in(2, 0) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(1.5) write-amplify(0.5) OK, records in: 2, records dropped: 2 output_compression: NoCompression
2025/07/02-14:19:03.021938 281472957408744 (Original Log Time 2025/07/02-14:19:03.021903) EVENT_LOG_v1 {"time_micros": 1751437143021898, "job": 3, "event": "compaction_finished", "compaction_time_micros": 1301, "compaction_time_cpu_micros": 233, "output_level": 6, "num_output_files": 1, "total_output_size": 1024, "num_input_records": 2, "num_output_records": 0, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 0, 0, 0, 0, 0, 0]}
2025/07/02-14:19:03.022131 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service/log/000040.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:03.022138 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437143022136, "job": 3, "event": "table_file_deletion", "file_number": 40}
2025/07/02-14:19:03.022408 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service/log/000046.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:03.022422 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437143022421, "job": 3, "event": "table_file_deletion", "file_number": 46}
2025/07/02-14:19:03.022643 281472957408744 [/compaction/compaction_job.cc:2080] [Configuration] [JOB 4] Compacting 2@0 + 1@6 files to L6, score 1.00
2025/07/02-14:19:03.022646 281472957408744 [/compaction/compaction_job.cc:2084] [Configuration]: Compaction start summary: Base version 5 Base level 0, inputs: [47(1218B) 41(1218B)], [37(1174B)]
2025/07/02-14:19:03.022651 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437143022648, "job": 4, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [47, 41], "files_L6": [37], "score": 1, "input_data_size": 3610, "oldest_snapshot_seqno": -1}
2025/07/02-14:19:03.024946 281472957408744 [/compaction/compaction_job.cc:1661] [Configuration] [JOB 4] Generated table #53: 1 keys, 1174 bytes, temperature: kUnknown
2025/07/02-14:19:03.024981 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437143024952, "cf_name": "Configuration", "job": 4, "event": "table_file_creation", "file_number": 53, "file_size": 1174, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 0, "largest_seqno": 0, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 26, "raw_average_key_size": 26, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751248197, "oldest_key_time": 0, "file_creation_time": 1751437143, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "d6c423a5-bd84-4148-9118-344542ec36da", "db_session_id": "LR5P8XSGRSWGWUKMX0PU", "orig_file_number": 53, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:19:03.026214 281472957408744 (Original Log Time 2025/07/02-14:19:03.025959) [/compaction/compaction_job.cc:1733] [Configuration] [JOB 4] Compacted 2@0 + 1@6 files to L6 => 1174 bytes
2025/07/02-14:19:03.026216 281472957408744 (Original Log Time 2025/07/02-14:19:03.026181) [/compaction/compaction_job.cc:926] [Configuration] compacted to: base level 6 level multiplier 10.00 max bytes base 536870912 files[0 0 0 0 0 0 1] max score 0.00, MB/sec: 1.4 rd, 0.5 wr, level 6, files in(2, 1) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(2.0) write-amplify(0.5) OK, records in: 5, records dropped: 4 output_compression: NoCompression
2025/07/02-14:19:03.026231 281472957408744 (Original Log Time 2025/07/02-14:19:03.026196) EVENT_LOG_v1 {"time_micros": 1751437143026189, "job": 4, "event": "compaction_finished", "compaction_time_micros": 2513, "compaction_time_cpu_micros": 335, "output_level": 6, "num_output_files": 1, "total_output_size": 1174, "num_input_records": 5, "num_output_records": 1, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 0, 0, 0, 0, 0, 1]}
2025/07/02-14:19:03.026389 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service/log/000037.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:03.026393 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437143026392, "job": 4, "event": "table_file_deletion", "file_number": 37}
2025/07/02-14:19:03.026476 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service/log/000041.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:03.026485 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437143026484, "job": 4, "event": "table_file_deletion", "file_number": 41}
2025/07/02-14:19:03.026570 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service/log/000047.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:03.026572 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437143026572, "job": 4, "event": "table_file_deletion", "file_number": 47}
2025/07/02-14:19:04.020597 281472947103208 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-14:19:04.020605 281472947103208 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/07/02-14:19:04.020606 281472947103208 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/07/02-14:19:23.677082 281472701302248 [/db_impl/db_impl.cc:487] Shutdown: canceling all background work
2025/07/02-14:19:23.681439 281472701302248 [/db_impl/db_impl.cc:668] Shutdown complete
