2025/07/02-14:23:01.543292 281472955067064 RocksDB version: 8.8.1
2025/07/02-14:23:01.543374 281472955067064 Compile date 2023-11-23 11:07:28
2025/07/02-14:23:01.543383 281472955067064 DB SUMMARY
2025/07/02-14:23:01.543384 281472955067064 Host name (Env):  17623f512b8f
2025/07/02-14:23:01.543385 281472955067064 DB Session ID:  U1YJJ52XBC9Y2T9LGU2V
2025/07/02-14:23:01.543637 281472955067064 CURRENT file:  CURRENT
2025/07/02-14:23:01.543638 281472955067064 IDENTITY file:  IDENTITY
2025/07/02-14:23:01.543677 281472955067064 MANIFEST file:  MANIFEST-000049 size: 853 Bytes
2025/07/02-14:23:01.543679 281472955067064 SST files in /home/<USER>/data/protocol/raft/naming_persistent_service/log dir, Total Num: 1, files: 000053.sst 
2025/07/02-14:23:01.543680 281472955067064 Write Ahead Log file in /home/<USER>/data/protocol/raft/naming_persistent_service/log: 000048.log size: 203 ; 
2025/07/02-14:23:01.543681 281472955067064                         Options.error_if_exists: 0
2025/07/02-14:23:01.543682 281472955067064                       Options.create_if_missing: 1
2025/07/02-14:23:01.543682 281472955067064                         Options.paranoid_checks: 1
2025/07/02-14:23:01.543683 281472955067064             Options.flush_verify_memtable_count: 1
2025/07/02-14:23:01.543683 281472955067064          Options.compaction_verify_record_count: 1
2025/07/02-14:23:01.543684 281472955067064                               Options.track_and_verify_wals_in_manifest: 0
2025/07/02-14:23:01.543684 281472955067064        Options.verify_sst_unique_id_in_manifest: 1
2025/07/02-14:23:01.543685 281472955067064                                     Options.env: 0xffff7596d990
2025/07/02-14:23:01.543686 281472955067064                                      Options.fs: PosixFileSystem
2025/07/02-14:23:01.543686 281472955067064                                Options.info_log: 0xffff75414020
2025/07/02-14:23:01.543687 281472955067064                Options.max_file_opening_threads: 16
2025/07/02-14:23:01.543687 281472955067064                              Options.statistics: 0xffff76c45860
2025/07/02-14:23:01.543688 281472955067064                              Options.statistics stats level: 3
2025/07/02-14:23:01.543689 281472955067064                               Options.use_fsync: 0
2025/07/02-14:23:01.543689 281472955067064                       Options.max_log_file_size: 0
2025/07/02-14:23:01.543690 281472955067064                  Options.max_manifest_file_size: 1073741824
2025/07/02-14:23:01.543690 281472955067064                   Options.log_file_time_to_roll: 0
2025/07/02-14:23:01.543691 281472955067064                       Options.keep_log_file_num: 100
2025/07/02-14:23:01.543691 281472955067064                    Options.recycle_log_file_num: 0
2025/07/02-14:23:01.543692 281472955067064                         Options.allow_fallocate: 1
2025/07/02-14:23:01.543692 281472955067064                        Options.allow_mmap_reads: 0
2025/07/02-14:23:01.543693 281472955067064                       Options.allow_mmap_writes: 0
2025/07/02-14:23:01.543693 281472955067064                        Options.use_direct_reads: 0
2025/07/02-14:23:01.543694 281472955067064                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/02-14:23:01.543695 281472955067064          Options.create_missing_column_families: 1
2025/07/02-14:23:01.543695 281472955067064                              Options.db_log_dir: 
2025/07/02-14:23:01.543696 281472955067064                                 Options.wal_dir: 
2025/07/02-14:23:01.543696 281472955067064                Options.table_cache_numshardbits: 6
2025/07/02-14:23:01.543697 281472955067064                         Options.WAL_ttl_seconds: 0
2025/07/02-14:23:01.543697 281472955067064                       Options.WAL_size_limit_MB: 0
2025/07/02-14:23:01.543698 281472955067064                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/02-14:23:01.543698 281472955067064             Options.manifest_preallocation_size: 4194304
2025/07/02-14:23:01.543699 281472955067064                     Options.is_fd_close_on_exec: 1
2025/07/02-14:23:01.543699 281472955067064                   Options.advise_random_on_open: 1
2025/07/02-14:23:01.543701 281472955067064                    Options.db_write_buffer_size: 0
2025/07/02-14:23:01.543702 281472955067064                    Options.write_buffer_manager: 0xffff76c45c20
2025/07/02-14:23:01.543702 281472955067064         Options.access_hint_on_compaction_start: 1
2025/07/02-14:23:01.543703 281472955067064           Options.random_access_max_buffer_size: 1048576
2025/07/02-14:23:01.543703 281472955067064                      Options.use_adaptive_mutex: 0
2025/07/02-14:23:01.543704 281472955067064                            Options.rate_limiter: 0
2025/07/02-14:23:01.543705 281472955067064     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/02-14:23:01.543705 281472955067064                       Options.wal_recovery_mode: 2
2025/07/02-14:23:01.543706 281472955067064                  Options.enable_thread_tracking: 0
2025/07/02-14:23:01.543706 281472955067064                  Options.enable_pipelined_write: 0
2025/07/02-14:23:01.543707 281472955067064                  Options.unordered_write: 0
2025/07/02-14:23:01.543707 281472955067064         Options.allow_concurrent_memtable_write: 1
2025/07/02-14:23:01.543708 281472955067064      Options.enable_write_thread_adaptive_yield: 1
2025/07/02-14:23:01.543708 281472955067064             Options.write_thread_max_yield_usec: 100
2025/07/02-14:23:01.543709 281472955067064            Options.write_thread_slow_yield_usec: 3
2025/07/02-14:23:01.543709 281472955067064                               Options.row_cache: None
2025/07/02-14:23:01.543710 281472955067064                              Options.wal_filter: None
2025/07/02-14:23:01.543711 281472955067064             Options.avoid_flush_during_recovery: 0
2025/07/02-14:23:01.543711 281472955067064             Options.allow_ingest_behind: 0
2025/07/02-14:23:01.543712 281472955067064             Options.two_write_queues: 0
2025/07/02-14:23:01.543712 281472955067064             Options.manual_wal_flush: 0
2025/07/02-14:23:01.543713 281472955067064             Options.wal_compression: 0
2025/07/02-14:23:01.543714 281472955067064             Options.atomic_flush: 0
2025/07/02-14:23:01.543714 281472955067064             Options.avoid_unnecessary_blocking_io: 0
2025/07/02-14:23:01.543715 281472955067064                 Options.persist_stats_to_disk: 0
2025/07/02-14:23:01.543715 281472955067064                 Options.write_dbid_to_manifest: 0
2025/07/02-14:23:01.543716 281472955067064                 Options.log_readahead_size: 0
2025/07/02-14:23:01.543716 281472955067064                 Options.file_checksum_gen_factory: Unknown
2025/07/02-14:23:01.543717 281472955067064                 Options.best_efforts_recovery: 0
2025/07/02-14:23:01.543717 281472955067064                Options.max_bgerror_resume_count: 2147483647
2025/07/02-14:23:01.543718 281472955067064            Options.bgerror_resume_retry_interval: 1000000
2025/07/02-14:23:01.543718 281472955067064             Options.allow_data_in_errors: 0
2025/07/02-14:23:01.543719 281472955067064             Options.db_host_id: __hostname__
2025/07/02-14:23:01.543720 281472955067064             Options.enforce_single_del_contracts: true
2025/07/02-14:23:01.543720 281472955067064             Options.max_background_jobs: 2
2025/07/02-14:23:01.543721 281472955067064             Options.max_background_compactions: -1
2025/07/02-14:23:01.543721 281472955067064             Options.max_subcompactions: 1
2025/07/02-14:23:01.543722 281472955067064             Options.avoid_flush_during_shutdown: 0
2025/07/02-14:23:01.543722 281472955067064           Options.writable_file_max_buffer_size: 1048576
2025/07/02-14:23:01.543723 281472955067064             Options.delayed_write_rate : 16777216
2025/07/02-14:23:01.543723 281472955067064             Options.max_total_wal_size: 1073741824
2025/07/02-14:23:01.543724 281472955067064             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/02-14:23:01.543725 281472955067064                   Options.stats_dump_period_sec: 600
2025/07/02-14:23:01.543725 281472955067064                 Options.stats_persist_period_sec: 600
2025/07/02-14:23:01.543726 281472955067064                 Options.stats_history_buffer_size: 1048576
2025/07/02-14:23:01.543727 281472955067064                          Options.max_open_files: -1
2025/07/02-14:23:01.543727 281472955067064                          Options.bytes_per_sync: 0
2025/07/02-14:23:01.543728 281472955067064                      Options.wal_bytes_per_sync: 0
2025/07/02-14:23:01.543728 281472955067064                   Options.strict_bytes_per_sync: 0
2025/07/02-14:23:01.543729 281472955067064       Options.compaction_readahead_size: 2097152
2025/07/02-14:23:01.543729 281472955067064                  Options.max_background_flushes: -1
2025/07/02-14:23:01.543730 281472955067064 Options.daily_offpeak_time_utc: 
2025/07/02-14:23:01.543730 281472955067064 Compression algorithms supported:
2025/07/02-14:23:01.543731 281472955067064 	kZSTDNotFinalCompression supported: 1
2025/07/02-14:23:01.543743 281472955067064 	kZSTD supported: 1
2025/07/02-14:23:01.543744 281472955067064 	kXpressCompression supported: 0
2025/07/02-14:23:01.543745 281472955067064 	kLZ4HCCompression supported: 1
2025/07/02-14:23:01.543745 281472955067064 	kLZ4Compression supported: 1
2025/07/02-14:23:01.543746 281472955067064 	kBZip2Compression supported: 1
2025/07/02-14:23:01.543746 281472955067064 	kZlibCompression supported: 1
2025/07/02-14:23:01.543747 281472955067064 	kSnappyCompression supported: 1
2025/07/02-14:23:01.543748 281472955067064 Fast CRC32 supported: Supported on Arm64
2025/07/02-14:23:01.543749 281472955067064 DMutex implementation: pthread_mutex_t
2025/07/02-14:23:01.544100 281472955067064 [/version_set.cc:5906] Recovering from manifest file: /home/<USER>/data/protocol/raft/naming_persistent_service/log/MANIFEST-000049
2025/07/02-14:23:01.544323 281472955067064 [/column_family.cc:618] --------------- Options for column family [default]:
2025/07/02-14:23:01.544325 281472955067064               Options.comparator: leveldb.BytewiseComparator
2025/07/02-14:23:01.544326 281472955067064           Options.merge_operator: StringAppendOperator
2025/07/02-14:23:01.544327 281472955067064        Options.compaction_filter: None
2025/07/02-14:23:01.544327 281472955067064        Options.compaction_filter_factory: None
2025/07/02-14:23:01.544328 281472955067064  Options.sst_partitioner_factory: None
2025/07/02-14:23:01.544328 281472955067064         Options.memtable_factory: SkipListFactory
2025/07/02-14:23:01.544329 281472955067064            Options.table_factory: BlockBasedTable
2025/07/02-14:23:01.544350 281472955067064            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff758395b0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff76c45b70
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/02-14:23:01.544353 281472955067064        Options.write_buffer_size: 134217728
2025/07/02-14:23:01.544354 281472955067064  Options.max_write_buffer_number: 6
2025/07/02-14:23:01.544355 281472955067064        Options.compression[0]: NoCompression
2025/07/02-14:23:01.544355 281472955067064        Options.compression[1]: NoCompression
2025/07/02-14:23:01.544357 281472955067064        Options.compression[2]: LZ4
2025/07/02-14:23:01.544357 281472955067064        Options.compression[3]: LZ4
2025/07/02-14:23:01.544358 281472955067064        Options.compression[4]: LZ4
2025/07/02-14:23:01.544358 281472955067064        Options.compression[5]: LZ4
2025/07/02-14:23:01.544359 281472955067064        Options.compression[6]: LZ4
2025/07/02-14:23:01.544360 281472955067064                  Options.bottommost_compression: Disabled
2025/07/02-14:23:01.544360 281472955067064       Options.prefix_extractor: rocksdb.FixedPrefix
2025/07/02-14:23:01.544361 281472955067064   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/02-14:23:01.544362 281472955067064             Options.num_levels: 7
2025/07/02-14:23:01.544362 281472955067064        Options.min_write_buffer_number_to_merge: 2
2025/07/02-14:23:01.544363 281472955067064     Options.max_write_buffer_number_to_maintain: 0
2025/07/02-14:23:01.544363 281472955067064     Options.max_write_buffer_size_to_maintain: 0
2025/07/02-14:23:01.544364 281472955067064            Options.bottommost_compression_opts.window_bits: -14
2025/07/02-14:23:01.544364 281472955067064                  Options.bottommost_compression_opts.level: 32767
2025/07/02-14:23:01.544365 281472955067064               Options.bottommost_compression_opts.strategy: 0
2025/07/02-14:23:01.544365 281472955067064         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/02-14:23:01.544366 281472955067064         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:23:01.544367 281472955067064         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/02-14:23:01.544367 281472955067064                  Options.bottommost_compression_opts.enabled: false
2025/07/02-14:23:01.544368 281472955067064         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:23:01.544368 281472955067064         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:23:01.544369 281472955067064            Options.compression_opts.window_bits: -14
2025/07/02-14:23:01.544370 281472955067064                  Options.compression_opts.level: 32767
2025/07/02-14:23:01.544370 281472955067064               Options.compression_opts.strategy: 0
2025/07/02-14:23:01.544371 281472955067064         Options.compression_opts.max_dict_bytes: 0
2025/07/02-14:23:01.544371 281472955067064         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:23:01.544372 281472955067064         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:23:01.544372 281472955067064         Options.compression_opts.parallel_threads: 1
2025/07/02-14:23:01.544373 281472955067064                  Options.compression_opts.enabled: false
2025/07/02-14:23:01.544374 281472955067064         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:23:01.544374 281472955067064      Options.level0_file_num_compaction_trigger: 2
2025/07/02-14:23:01.544375 281472955067064          Options.level0_slowdown_writes_trigger: 20
2025/07/02-14:23:01.544375 281472955067064              Options.level0_stop_writes_trigger: 40
2025/07/02-14:23:01.544376 281472955067064                   Options.target_file_size_base: 67108864
2025/07/02-14:23:01.544376 281472955067064             Options.target_file_size_multiplier: 1
2025/07/02-14:23:01.544377 281472955067064                Options.max_bytes_for_level_base: 536870912
2025/07/02-14:23:01.544377 281472955067064 Options.level_compaction_dynamic_level_bytes: 1
2025/07/02-14:23:01.544378 281472955067064          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/02-14:23:01.544379 281472955067064 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/02-14:23:01.544379 281472955067064 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/02-14:23:01.544380 281472955067064 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/02-14:23:01.544380 281472955067064 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/02-14:23:01.544381 281472955067064 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/02-14:23:01.544382 281472955067064 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/02-14:23:01.544382 281472955067064 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/02-14:23:01.544383 281472955067064       Options.max_sequential_skip_in_iterations: 8
2025/07/02-14:23:01.544384 281472955067064                    Options.max_compaction_bytes: 1677721600
2025/07/02-14:23:01.544384 281472955067064   Options.ignore_max_compaction_bytes_for_input: true
2025/07/02-14:23:01.544385 281472955067064                        Options.arena_block_size: 1048576
2025/07/02-14:23:01.544385 281472955067064   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/02-14:23:01.544386 281472955067064   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/02-14:23:01.544386 281472955067064                Options.disable_auto_compactions: 0
2025/07/02-14:23:01.544387 281472955067064                        Options.compaction_style: kCompactionStyleLevel
2025/07/02-14:23:01.544388 281472955067064                          Options.compaction_pri: kMinOverlappingRatio
2025/07/02-14:23:01.544389 281472955067064 Options.compaction_options_universal.size_ratio: 1
2025/07/02-14:23:01.544389 281472955067064 Options.compaction_options_universal.min_merge_width: 2
2025/07/02-14:23:01.544390 281472955067064 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/02-14:23:01.544390 281472955067064 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/02-14:23:01.544391 281472955067064 Options.compaction_options_universal.compression_size_percent: -1
2025/07/02-14:23:01.544392 281472955067064 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/02-14:23:01.544392 281472955067064 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/02-14:23:01.544393 281472955067064 Options.compaction_options_fifo.allow_compaction: 0
2025/07/02-14:23:01.544394 281472955067064                   Options.table_properties_collectors: 
2025/07/02-14:23:01.544395 281472955067064                   Options.inplace_update_support: 0
2025/07/02-14:23:01.544395 281472955067064                 Options.inplace_update_num_locks: 10000
2025/07/02-14:23:01.544396 281472955067064               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/07/02-14:23:01.544396 281472955067064               Options.memtable_whole_key_filtering: 0
2025/07/02-14:23:01.544397 281472955067064   Options.memtable_huge_page_size: 0
2025/07/02-14:23:01.544397 281472955067064                           Options.bloom_locality: 0
2025/07/02-14:23:01.544398 281472955067064                    Options.max_successive_merges: 0
2025/07/02-14:23:01.544398 281472955067064                Options.optimize_filters_for_hits: 0
2025/07/02-14:23:01.544399 281472955067064                Options.paranoid_file_checks: 0
2025/07/02-14:23:01.544400 281472955067064                Options.force_consistency_checks: 1
2025/07/02-14:23:01.544400 281472955067064                Options.report_bg_io_stats: 0
2025/07/02-14:23:01.544401 281472955067064                               Options.ttl: 2592000
2025/07/02-14:23:01.544401 281472955067064          Options.periodic_compaction_seconds: 0
2025/07/02-14:23:01.544402 281472955067064                        Options.default_temperature: kUnknown
2025/07/02-14:23:01.544402 281472955067064  Options.preclude_last_level_data_seconds: 0
2025/07/02-14:23:01.544403 281472955067064    Options.preserve_internal_time_seconds: 0
2025/07/02-14:23:01.544403 281472955067064                       Options.enable_blob_files: false
2025/07/02-14:23:01.544404 281472955067064                           Options.min_blob_size: 0
2025/07/02-14:23:01.544405 281472955067064                          Options.blob_file_size: 268435456
2025/07/02-14:23:01.544405 281472955067064                   Options.blob_compression_type: NoCompression
2025/07/02-14:23:01.544406 281472955067064          Options.enable_blob_garbage_collection: false
2025/07/02-14:23:01.544407 281472955067064      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/02-14:23:01.544407 281472955067064 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/02-14:23:01.544408 281472955067064          Options.blob_compaction_readahead_size: 0
2025/07/02-14:23:01.544408 281472955067064                Options.blob_file_starting_level: 0
2025/07/02-14:23:01.544409 281472955067064         Options.experimental_mempurge_threshold: 0.000000
2025/07/02-14:23:01.544410 281472955067064            Options.memtable_max_range_deletions: 0
2025/07/02-14:23:01.548238 281472955067064 [/column_family.cc:618] --------------- Options for column family [Configuration]:
2025/07/02-14:23:01.548249 281472955067064               Options.comparator: leveldb.BytewiseComparator
2025/07/02-14:23:01.548251 281472955067064           Options.merge_operator: StringAppendOperator
2025/07/02-14:23:01.548252 281472955067064        Options.compaction_filter: None
2025/07/02-14:23:01.548252 281472955067064        Options.compaction_filter_factory: None
2025/07/02-14:23:01.548253 281472955067064  Options.sst_partitioner_factory: None
2025/07/02-14:23:01.548255 281472955067064         Options.memtable_factory: SkipListFactory
2025/07/02-14:23:01.548257 281472955067064            Options.table_factory: BlockBasedTable
2025/07/02-14:23:01.548289 281472955067064            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff758395b0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff76c45b70
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/02-14:23:01.548294 281472955067064        Options.write_buffer_size: 134217728
2025/07/02-14:23:01.548295 281472955067064  Options.max_write_buffer_number: 6
2025/07/02-14:23:01.548295 281472955067064        Options.compression[0]: NoCompression
2025/07/02-14:23:01.548296 281472955067064        Options.compression[1]: NoCompression
2025/07/02-14:23:01.548297 281472955067064        Options.compression[2]: LZ4
2025/07/02-14:23:01.548297 281472955067064        Options.compression[3]: LZ4
2025/07/02-14:23:01.548298 281472955067064        Options.compression[4]: LZ4
2025/07/02-14:23:01.548298 281472955067064        Options.compression[5]: LZ4
2025/07/02-14:23:01.548299 281472955067064        Options.compression[6]: LZ4
2025/07/02-14:23:01.548300 281472955067064                  Options.bottommost_compression: Disabled
2025/07/02-14:23:01.548300 281472955067064       Options.prefix_extractor: rocksdb.FixedPrefix
2025/07/02-14:23:01.548301 281472955067064   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/02-14:23:01.548302 281472955067064             Options.num_levels: 7
2025/07/02-14:23:01.548302 281472955067064        Options.min_write_buffer_number_to_merge: 2
2025/07/02-14:23:01.548303 281472955067064     Options.max_write_buffer_number_to_maintain: 0
2025/07/02-14:23:01.548304 281472955067064     Options.max_write_buffer_size_to_maintain: 0
2025/07/02-14:23:01.548304 281472955067064            Options.bottommost_compression_opts.window_bits: -14
2025/07/02-14:23:01.548306 281472955067064                  Options.bottommost_compression_opts.level: 32767
2025/07/02-14:23:01.548306 281472955067064               Options.bottommost_compression_opts.strategy: 0
2025/07/02-14:23:01.548307 281472955067064         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/02-14:23:01.548307 281472955067064         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:23:01.548308 281472955067064         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/02-14:23:01.548308 281472955067064                  Options.bottommost_compression_opts.enabled: false
2025/07/02-14:23:01.548309 281472955067064         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:23:01.548310 281472955067064         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:23:01.548311 281472955067064            Options.compression_opts.window_bits: -14
2025/07/02-14:23:01.548311 281472955067064                  Options.compression_opts.level: 32767
2025/07/02-14:23:01.548312 281472955067064               Options.compression_opts.strategy: 0
2025/07/02-14:23:01.548312 281472955067064         Options.compression_opts.max_dict_bytes: 0
2025/07/02-14:23:01.548313 281472955067064         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:23:01.548313 281472955067064         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:23:01.548314 281472955067064         Options.compression_opts.parallel_threads: 1
2025/07/02-14:23:01.548314 281472955067064                  Options.compression_opts.enabled: false
2025/07/02-14:23:01.548315 281472955067064         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:23:01.548316 281472955067064      Options.level0_file_num_compaction_trigger: 2
2025/07/02-14:23:01.548316 281472955067064          Options.level0_slowdown_writes_trigger: 20
2025/07/02-14:23:01.548317 281472955067064              Options.level0_stop_writes_trigger: 40
2025/07/02-14:23:01.548317 281472955067064                   Options.target_file_size_base: 67108864
2025/07/02-14:23:01.548318 281472955067064             Options.target_file_size_multiplier: 1
2025/07/02-14:23:01.548318 281472955067064                Options.max_bytes_for_level_base: 536870912
2025/07/02-14:23:01.548319 281472955067064 Options.level_compaction_dynamic_level_bytes: 1
2025/07/02-14:23:01.548320 281472955067064          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/02-14:23:01.548320 281472955067064 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/02-14:23:01.548321 281472955067064 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/02-14:23:01.548322 281472955067064 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/02-14:23:01.548322 281472955067064 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/02-14:23:01.548323 281472955067064 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/02-14:23:01.548323 281472955067064 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/02-14:23:01.548324 281472955067064 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/02-14:23:01.548324 281472955067064       Options.max_sequential_skip_in_iterations: 8
2025/07/02-14:23:01.548325 281472955067064                    Options.max_compaction_bytes: 1677721600
2025/07/02-14:23:01.548326 281472955067064   Options.ignore_max_compaction_bytes_for_input: true
2025/07/02-14:23:01.548326 281472955067064                        Options.arena_block_size: 1048576
2025/07/02-14:23:01.548327 281472955067064   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/02-14:23:01.548327 281472955067064   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/02-14:23:01.548328 281472955067064                Options.disable_auto_compactions: 0
2025/07/02-14:23:01.548329 281472955067064                        Options.compaction_style: kCompactionStyleLevel
2025/07/02-14:23:01.548330 281472955067064                          Options.compaction_pri: kMinOverlappingRatio
2025/07/02-14:23:01.548330 281472955067064 Options.compaction_options_universal.size_ratio: 1
2025/07/02-14:23:01.548331 281472955067064 Options.compaction_options_universal.min_merge_width: 2
2025/07/02-14:23:01.548332 281472955067064 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/02-14:23:01.548332 281472955067064 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/02-14:23:01.548333 281472955067064 Options.compaction_options_universal.compression_size_percent: -1
2025/07/02-14:23:01.548334 281472955067064 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/02-14:23:01.548334 281472955067064 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/02-14:23:01.548335 281472955067064 Options.compaction_options_fifo.allow_compaction: 0
2025/07/02-14:23:01.548337 281472955067064                   Options.table_properties_collectors: 
2025/07/02-14:23:01.548338 281472955067064                   Options.inplace_update_support: 0
2025/07/02-14:23:01.548338 281472955067064                 Options.inplace_update_num_locks: 10000
2025/07/02-14:23:01.548339 281472955067064               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/07/02-14:23:01.548340 281472955067064               Options.memtable_whole_key_filtering: 0
2025/07/02-14:23:01.548340 281472955067064   Options.memtable_huge_page_size: 0
2025/07/02-14:23:01.548341 281472955067064                           Options.bloom_locality: 0
2025/07/02-14:23:01.548341 281472955067064                    Options.max_successive_merges: 0
2025/07/02-14:23:01.548342 281472955067064                Options.optimize_filters_for_hits: 0
2025/07/02-14:23:01.548342 281472955067064                Options.paranoid_file_checks: 0
2025/07/02-14:23:01.548343 281472955067064                Options.force_consistency_checks: 1
2025/07/02-14:23:01.548343 281472955067064                Options.report_bg_io_stats: 0
2025/07/02-14:23:01.548344 281472955067064                               Options.ttl: 2592000
2025/07/02-14:23:01.548344 281472955067064          Options.periodic_compaction_seconds: 0
2025/07/02-14:23:01.548345 281472955067064                        Options.default_temperature: kUnknown
2025/07/02-14:23:01.548346 281472955067064  Options.preclude_last_level_data_seconds: 0
2025/07/02-14:23:01.548346 281472955067064    Options.preserve_internal_time_seconds: 0
2025/07/02-14:23:01.548347 281472955067064                       Options.enable_blob_files: false
2025/07/02-14:23:01.548347 281472955067064                           Options.min_blob_size: 0
2025/07/02-14:23:01.548348 281472955067064                          Options.blob_file_size: 268435456
2025/07/02-14:23:01.548348 281472955067064                   Options.blob_compression_type: NoCompression
2025/07/02-14:23:01.548349 281472955067064          Options.enable_blob_garbage_collection: false
2025/07/02-14:23:01.548350 281472955067064      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/02-14:23:01.548350 281472955067064 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/02-14:23:01.548351 281472955067064          Options.blob_compaction_readahead_size: 0
2025/07/02-14:23:01.548351 281472955067064                Options.blob_file_starting_level: 0
2025/07/02-14:23:01.548352 281472955067064         Options.experimental_mempurge_threshold: 0.000000
2025/07/02-14:23:01.548353 281472955067064            Options.memtable_max_range_deletions: 0
2025/07/02-14:23:01.552756 281472955067064 [/version_set.cc:5957] Recovered from manifest file:/home/<USER>/data/protocol/raft/naming_persistent_service/log/MANIFEST-000049 succeeded,manifest_file_number is 49, next_file_number is 55, last_sequence is 120, log_number is 43,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 43
2025/07/02-14:23:01.552759 281472955067064 [/version_set.cc:5966] Column family [default] (ID 0), log number is 43
2025/07/02-14:23:01.552760 281472955067064 [/version_set.cc:5966] Column family [Configuration] (ID 1), log number is 43
2025/07/02-14:23:01.553043 281472955067064 [/db_impl/db_impl_open.cc:646] DB ID: d6c423a5-bd84-4148-9118-344542ec36da
2025/07/02-14:23:01.553692 281472955067064 EVENT_LOG_v1 {"time_micros": 1751437381553684, "job": 1, "event": "recovery_started", "wal_files": [48]}
2025/07/02-14:23:01.553695 281472955067064 [/db_impl/db_impl_open.cc:1145] Recovering log #48 mode 2
2025/07/02-14:23:01.554445 281472955067064 EVENT_LOG_v1 {"time_micros": 1751437381554429, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 56, "file_size": 1067, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 122, "largest_seqno": 122, "table_properties": {"data_size": 0, "index_size": 13, "index_partitions": 0, "top_level_index_size": 8, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 0, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751437381, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "d6c423a5-bd84-4148-9118-344542ec36da", "db_session_id": "U1YJJ52XBC9Y2T9LGU2V", "orig_file_number": 56, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:23:01.555591 281472955067064 EVENT_LOG_v1 {"time_micros": 1751437381555575, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 57, "file_size": 1218, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 123, "largest_seqno": 124, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 42, "raw_average_key_size": 21, "raw_value_size": 16, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751437381, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "d6c423a5-bd84-4148-9118-344542ec36da", "db_session_id": "U1YJJ52XBC9Y2T9LGU2V", "orig_file_number": 57, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:23:01.556152 281472955067064 EVENT_LOG_v1 {"time_micros": 1751437381556150, "job": 1, "event": "recovery_finished"}
2025/07/02-14:23:01.556303 281472955067064 [/version_set.cc:5417] Creating manifest 59
2025/07/02-14:23:01.609930 281472955067064 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service/log/000048.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:23:01.610077 281472955067064 [/db_impl/db_impl_open.cc:2150] SstFileManager instance 0xffff7583ab70
2025/07/02-14:23:01.610278 281472955067064 DB pointer 0xffff71ea5f80
2025/07/02-14:23:01.610514 281472609220072 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-14:23:01.610519 281472609220072 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.04 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.6      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      1/0    1.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.6      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.6      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.6      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.02 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.02 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff76c45b70#1 capacity: 8.00 MB seed: 1951147244 usage: 0.71 KB table_size: 256 occupancy: 4 collections: 1 last_copies: 1 last_secs: 1.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(2,0.25 KB,0.00305176%) IndexBlock(1,0.12 KB,0.00146627%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 5 Average: 2.4000  StdDev: 4.32
Min: 0  Median: 0.6250  Max: 11
Percentiles: P50: 0.62 P75: 0.94 P99: 11.00 P99.9: 11.00 P99.99: 11.00
------------------------------------------------------
[       0,       1 ]        4  80.000%  80.000% ################
(      10,      15 ]        1  20.000% 100.000% ####


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.19 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.9      0.00              0.00         1    0.001       0      0       0.0       0.0
  L6      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      2/0    2.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.9      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.9      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.9      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.02 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.02 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff76c45b70#1 capacity: 8.00 MB seed: 1951147244 usage: 0.71 KB table_size: 256 occupancy: 4 collections: 1 last_copies: 1 last_secs: 1.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(2,0.25 KB,0.00305176%) IndexBlock(1,0.12 KB,0.00146627%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 6 Average: 5.6667  StdDev: 7.80
Min: 0  Median: 0.7500  Max: 19
Percentiles: P50: 0.75 P75: 12.50 P99: 19.00 P99.9: 19.00 P99.99: 19.00
------------------------------------------------------
[       0,       1 ]        4  66.667%  66.667% #############
(      10,      15 ]        1  16.667%  83.333% ###
(      15,      22 ]        1  16.667% 100.000% ###

** Level 6 read latency histogram (micros):
Count: 4 Average: 15.7500  StdDev: 24.40
Min: 1  Median: 1.5000  Max: 58
Percentiles: P50: 1.50 P75: 2.00 P99: 58.00 P99.9: 58.00 P99.99: 58.00
------------------------------------------------------
[       0,       1 ]        1  25.000%  25.000% #####
(       1,       2 ]        2  50.000%  75.000% ##########
(      51,      76 ]        1  25.000% 100.000% #####

2025/07/02-14:23:01.611026 281472609220072 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 3
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 3
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 1
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 1
rocksdb.block.cache.index.bytes.insert COUNT : 123
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 2
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 2
rocksdb.block.cache.data.bytes.insert COUNT : 256
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 379
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 0
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 0
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 0
rocksdb.number.db.next COUNT : 0
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 0
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 0
rocksdb.no.file.opens COUNT : 3
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 0
rocksdb.wal.bytes COUNT : 0
rocksdb.write.self COUNT : 0
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 0
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2285
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 0
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 0
rocksdb.num.iterator.deleted COUNT : 0
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 1089
rocksdb.last.level.read.count COUNT : 4
rocksdb.non.last.level.read.bytes COUNT : 3418
rocksdb.non.last.level.read.count COUNT : 11
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 0
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 0
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 9
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 113
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 4
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 35
rocksdb.prefetch.hits COUNT : 1
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 140.000000 P95 : 150.000000 P99 : 150.000000 P100 : 150.000000 COUNT : 2 SUM : 270
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.manifest.file.sync.micros P50 : 87.000000 P95 : 87.000000 P99 : 87.000000 P100 : 87.000000 COUNT : 1 SUM : 87
rocksdb.table.open.io.micros P50 : 93.000000 P95 : 238.000000 P99 : 246.000000 P100 : 246.000000 COUNT : 3 SUM : 382
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 2.500000 P95 : 5.000000 P99 : 5.000000 P100 : 5.000000 COUNT : 6 SUM : 17
rocksdb.write.raw.block.micros P50 : 0.555556 P95 : 1.500000 P99 : 1.900000 P100 : 2.000000 COUNT : 10 SUM : 4
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 0.833333 P95 : 57.250000 P99 : 58.000000 P100 : 58.000000 COUNT : 15 SUM : 109
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.open.micros P50 : 0.833333 P95 : 57.250000 P99 : 58.000000 P100 : 58.000000 COUNT : 15 SUM : 109
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1133.000000 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 1 SUM : 1133
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-14:23:02.611251 281472609220072 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-14:23:02.611262 281472609220072 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/07/02-14:23:02.611264 281472609220072 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/07/02-14:31:58.214029 281472363660776 [/db_impl/db_impl.cc:487] Shutdown: canceling all background work
2025/07/02-14:31:58.224271 281472363660776 [/db_impl/db_impl.cc:668] Shutdown complete
