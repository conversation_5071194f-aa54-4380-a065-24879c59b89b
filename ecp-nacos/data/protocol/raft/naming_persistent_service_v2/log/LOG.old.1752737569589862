2025/07/02-17:08:52.208451 281473187850936 RocksDB version: 8.8.1
2025/07/02-17:08:52.208746 281473187850936 Compile date 2023-11-23 11:07:28
2025/07/02-17:08:52.208750 281473187850936 DB SUMMARY
2025/07/02-17:08:52.208753 281473187850936 Host name (Env):  58b7d9bb2e36
2025/07/02-17:08:52.208754 281473187850936 DB Session ID:  CM9FL0HBYPXSQ82NXPY7
2025/07/02-17:08:52.209620 281473187850936 CURRENT file:  CURRENT
2025/07/02-17:08:52.209627 281473187850936 IDENTITY file:  IDENTITY
2025/07/02-17:08:52.209760 281473187850936 MANIFEST file:  MANIFEST-000059 size: 497 Bytes
2025/07/02-17:08:52.209766 281473187850936 SST files in /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log dir, Total Num: 3, files: 000053.sst 000056.sst 000057.sst 
2025/07/02-17:08:52.209769 281473187850936 Write Ahead Log file in /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log: 000058.log size: 203 ; 
2025/07/02-17:08:52.209793 281473187850936                         Options.error_if_exists: 0
2025/07/02-17:08:52.209796 281473187850936                       Options.create_if_missing: 1
2025/07/02-17:08:52.209798 281473187850936                         Options.paranoid_checks: 1
2025/07/02-17:08:52.209799 281473187850936             Options.flush_verify_memtable_count: 1
2025/07/02-17:08:52.209800 281473187850936          Options.compaction_verify_record_count: 1
2025/07/02-17:08:52.209801 281473187850936                               Options.track_and_verify_wals_in_manifest: 0
2025/07/02-17:08:52.209802 281473187850936        Options.verify_sst_unique_id_in_manifest: 1
2025/07/02-17:08:52.209803 281473187850936                                     Options.env: 0xffff830e9a50
2025/07/02-17:08:52.209804 281473187850936                                      Options.fs: PosixFileSystem
2025/07/02-17:08:52.209806 281473187850936                                Options.info_log: 0xffff83534e60
2025/07/02-17:08:52.209807 281473187850936                Options.max_file_opening_threads: 16
2025/07/02-17:08:52.209808 281473187850936                              Options.statistics: 0xffff852a5860
2025/07/02-17:08:52.209809 281473187850936                              Options.statistics stats level: 3
2025/07/02-17:08:52.209810 281473187850936                               Options.use_fsync: 0
2025/07/02-17:08:52.209811 281473187850936                       Options.max_log_file_size: 0
2025/07/02-17:08:52.209812 281473187850936                  Options.max_manifest_file_size: 1073741824
2025/07/02-17:08:52.209813 281473187850936                   Options.log_file_time_to_roll: 0
2025/07/02-17:08:52.209814 281473187850936                       Options.keep_log_file_num: 100
2025/07/02-17:08:52.209815 281473187850936                    Options.recycle_log_file_num: 0
2025/07/02-17:08:52.209816 281473187850936                         Options.allow_fallocate: 1
2025/07/02-17:08:52.209817 281473187850936                        Options.allow_mmap_reads: 0
2025/07/02-17:08:52.209818 281473187850936                       Options.allow_mmap_writes: 0
2025/07/02-17:08:52.209819 281473187850936                        Options.use_direct_reads: 0
2025/07/02-17:08:52.209820 281473187850936                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/02-17:08:52.209821 281473187850936          Options.create_missing_column_families: 1
2025/07/02-17:08:52.209822 281473187850936                              Options.db_log_dir: 
2025/07/02-17:08:52.209823 281473187850936                                 Options.wal_dir: 
2025/07/02-17:08:52.209824 281473187850936                Options.table_cache_numshardbits: 6
2025/07/02-17:08:52.209825 281473187850936                         Options.WAL_ttl_seconds: 0
2025/07/02-17:08:52.209826 281473187850936                       Options.WAL_size_limit_MB: 0
2025/07/02-17:08:52.209827 281473187850936                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/02-17:08:52.209845 281473187850936             Options.manifest_preallocation_size: 4194304
2025/07/02-17:08:52.209847 281473187850936                     Options.is_fd_close_on_exec: 1
2025/07/02-17:08:52.209848 281473187850936                   Options.advise_random_on_open: 1
2025/07/02-17:08:52.209853 281473187850936                    Options.db_write_buffer_size: 0
2025/07/02-17:08:52.209855 281473187850936                    Options.write_buffer_manager: 0xffff852a5ce0
2025/07/02-17:08:52.209856 281473187850936         Options.access_hint_on_compaction_start: 1
2025/07/02-17:08:52.209857 281473187850936           Options.random_access_max_buffer_size: 1048576
2025/07/02-17:08:52.209858 281473187850936                      Options.use_adaptive_mutex: 0
2025/07/02-17:08:52.209859 281473187850936                            Options.rate_limiter: 0
2025/07/02-17:08:52.209860 281473187850936     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/02-17:08:52.209861 281473187850936                       Options.wal_recovery_mode: 2
2025/07/02-17:08:52.209862 281473187850936                  Options.enable_thread_tracking: 0
2025/07/02-17:08:52.209863 281473187850936                  Options.enable_pipelined_write: 0
2025/07/02-17:08:52.209864 281473187850936                  Options.unordered_write: 0
2025/07/02-17:08:52.209865 281473187850936         Options.allow_concurrent_memtable_write: 1
2025/07/02-17:08:52.209866 281473187850936      Options.enable_write_thread_adaptive_yield: 1
2025/07/02-17:08:52.209867 281473187850936             Options.write_thread_max_yield_usec: 100
2025/07/02-17:08:52.209868 281473187850936            Options.write_thread_slow_yield_usec: 3
2025/07/02-17:08:52.209869 281473187850936                               Options.row_cache: None
2025/07/02-17:08:52.209870 281473187850936                              Options.wal_filter: None
2025/07/02-17:08:52.209871 281473187850936             Options.avoid_flush_during_recovery: 0
2025/07/02-17:08:52.209872 281473187850936             Options.allow_ingest_behind: 0
2025/07/02-17:08:52.209873 281473187850936             Options.two_write_queues: 0
2025/07/02-17:08:52.209874 281473187850936             Options.manual_wal_flush: 0
2025/07/02-17:08:52.209875 281473187850936             Options.wal_compression: 0
2025/07/02-17:08:52.209876 281473187850936             Options.atomic_flush: 0
2025/07/02-17:08:52.209877 281473187850936             Options.avoid_unnecessary_blocking_io: 0
2025/07/02-17:08:52.209878 281473187850936                 Options.persist_stats_to_disk: 0
2025/07/02-17:08:52.209879 281473187850936                 Options.write_dbid_to_manifest: 0
2025/07/02-17:08:52.209880 281473187850936                 Options.log_readahead_size: 0
2025/07/02-17:08:52.209881 281473187850936                 Options.file_checksum_gen_factory: Unknown
2025/07/02-17:08:52.209882 281473187850936                 Options.best_efforts_recovery: 0
2025/07/02-17:08:52.209907 281473187850936                Options.max_bgerror_resume_count: 2147483647
2025/07/02-17:08:52.209910 281473187850936            Options.bgerror_resume_retry_interval: 1000000
2025/07/02-17:08:52.209911 281473187850936             Options.allow_data_in_errors: 0
2025/07/02-17:08:52.209912 281473187850936             Options.db_host_id: __hostname__
2025/07/02-17:08:52.209913 281473187850936             Options.enforce_single_del_contracts: true
2025/07/02-17:08:52.209915 281473187850936             Options.max_background_jobs: 2
2025/07/02-17:08:52.209916 281473187850936             Options.max_background_compactions: -1
2025/07/02-17:08:52.209917 281473187850936             Options.max_subcompactions: 1
2025/07/02-17:08:52.209918 281473187850936             Options.avoid_flush_during_shutdown: 0
2025/07/02-17:08:52.209919 281473187850936           Options.writable_file_max_buffer_size: 1048576
2025/07/02-17:08:52.209920 281473187850936             Options.delayed_write_rate : 16777216
2025/07/02-17:08:52.209921 281473187850936             Options.max_total_wal_size: 1073741824
2025/07/02-17:08:52.209922 281473187850936             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/02-17:08:52.209923 281473187850936                   Options.stats_dump_period_sec: 600
2025/07/02-17:08:52.209952 281473187850936                 Options.stats_persist_period_sec: 600
2025/07/02-17:08:52.209953 281473187850936                 Options.stats_history_buffer_size: 1048576
2025/07/02-17:08:52.209954 281473187850936                          Options.max_open_files: -1
2025/07/02-17:08:52.209955 281473187850936                          Options.bytes_per_sync: 0
2025/07/02-17:08:52.209956 281473187850936                      Options.wal_bytes_per_sync: 0
2025/07/02-17:08:52.209957 281473187850936                   Options.strict_bytes_per_sync: 0
2025/07/02-17:08:52.209958 281473187850936       Options.compaction_readahead_size: 2097152
2025/07/02-17:08:52.209959 281473187850936                  Options.max_background_flushes: -1
2025/07/02-17:08:52.209960 281473187850936 Options.daily_offpeak_time_utc: 
2025/07/02-17:08:52.209961 281473187850936 Compression algorithms supported:
2025/07/02-17:08:52.209963 281473187850936 	kZSTDNotFinalCompression supported: 1
2025/07/02-17:08:52.209965 281473187850936 	kZSTD supported: 1
2025/07/02-17:08:52.209966 281473187850936 	kXpressCompression supported: 0
2025/07/02-17:08:52.209967 281473187850936 	kLZ4HCCompression supported: 1
2025/07/02-17:08:52.209968 281473187850936 	kLZ4Compression supported: 1
2025/07/02-17:08:52.209969 281473187850936 	kBZip2Compression supported: 1
2025/07/02-17:08:52.209970 281473187850936 	kZlibCompression supported: 1
2025/07/02-17:08:52.209971 281473187850936 	kSnappyCompression supported: 1
2025/07/02-17:08:52.209976 281473187850936 Fast CRC32 supported: Supported on Arm64
2025/07/02-17:08:52.209977 281473187850936 DMutex implementation: pthread_mutex_t
2025/07/02-17:08:52.211432 281473187850936 [/version_set.cc:5906] Recovering from manifest file: /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/MANIFEST-000059
2025/07/02-17:08:52.211781 281473187850936 [/column_family.cc:618] --------------- Options for column family [default]:
2025/07/02-17:08:52.211784 281473187850936               Options.comparator: leveldb.BytewiseComparator
2025/07/02-17:08:52.211786 281473187850936           Options.merge_operator: StringAppendOperator
2025/07/02-17:08:52.211786 281473187850936        Options.compaction_filter: None
2025/07/02-17:08:52.211787 281473187850936        Options.compaction_filter_factory: None
2025/07/02-17:08:52.211788 281473187850936  Options.sst_partitioner_factory: None
2025/07/02-17:08:52.211788 281473187850936         Options.memtable_factory: SkipListFactory
2025/07/02-17:08:52.211789 281473187850936            Options.table_factory: BlockBasedTable
2025/07/02-17:08:52.211823 281473187850936            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff831f9cd0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff852a59f0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/02-17:08:52.211830 281473187850936        Options.write_buffer_size: 134217728
2025/07/02-17:08:52.211831 281473187850936  Options.max_write_buffer_number: 6
2025/07/02-17:08:52.211831 281473187850936        Options.compression[0]: NoCompression
2025/07/02-17:08:52.211833 281473187850936        Options.compression[1]: NoCompression
2025/07/02-17:08:52.211833 281473187850936        Options.compression[2]: LZ4
2025/07/02-17:08:52.211834 281473187850936        Options.compression[3]: LZ4
2025/07/02-17:08:52.211834 281473187850936        Options.compression[4]: LZ4
2025/07/02-17:08:52.211835 281473187850936        Options.compression[5]: LZ4
2025/07/02-17:08:52.211836 281473187850936        Options.compression[6]: LZ4
2025/07/02-17:08:52.211836 281473187850936                  Options.bottommost_compression: Disabled
2025/07/02-17:08:52.211837 281473187850936       Options.prefix_extractor: rocksdb.FixedPrefix
2025/07/02-17:08:52.211838 281473187850936   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/02-17:08:52.211839 281473187850936             Options.num_levels: 7
2025/07/02-17:08:52.211839 281473187850936        Options.min_write_buffer_number_to_merge: 2
2025/07/02-17:08:52.211840 281473187850936     Options.max_write_buffer_number_to_maintain: 0
2025/07/02-17:08:52.211840 281473187850936     Options.max_write_buffer_size_to_maintain: 0
2025/07/02-17:08:52.211841 281473187850936            Options.bottommost_compression_opts.window_bits: -14
2025/07/02-17:08:52.211842 281473187850936                  Options.bottommost_compression_opts.level: 32767
2025/07/02-17:08:52.211842 281473187850936               Options.bottommost_compression_opts.strategy: 0
2025/07/02-17:08:52.211843 281473187850936         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/02-17:08:52.211843 281473187850936         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/02-17:08:52.211844 281473187850936         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/02-17:08:52.211844 281473187850936                  Options.bottommost_compression_opts.enabled: false
2025/07/02-17:08:52.211845 281473187850936         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/02-17:08:52.211846 281473187850936         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/02-17:08:52.211846 281473187850936            Options.compression_opts.window_bits: -14
2025/07/02-17:08:52.211847 281473187850936                  Options.compression_opts.level: 32767
2025/07/02-17:08:52.211847 281473187850936               Options.compression_opts.strategy: 0
2025/07/02-17:08:52.211848 281473187850936         Options.compression_opts.max_dict_bytes: 0
2025/07/02-17:08:52.211848 281473187850936         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/02-17:08:52.211849 281473187850936         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/02-17:08:52.211850 281473187850936         Options.compression_opts.parallel_threads: 1
2025/07/02-17:08:52.211850 281473187850936                  Options.compression_opts.enabled: false
2025/07/02-17:08:52.211851 281473187850936         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/02-17:08:52.211851 281473187850936      Options.level0_file_num_compaction_trigger: 2
2025/07/02-17:08:52.211852 281473187850936          Options.level0_slowdown_writes_trigger: 20
2025/07/02-17:08:52.211852 281473187850936              Options.level0_stop_writes_trigger: 40
2025/07/02-17:08:52.211853 281473187850936                   Options.target_file_size_base: 67108864
2025/07/02-17:08:52.211853 281473187850936             Options.target_file_size_multiplier: 1
2025/07/02-17:08:52.211854 281473187850936                Options.max_bytes_for_level_base: 536870912
2025/07/02-17:08:52.211854 281473187850936 Options.level_compaction_dynamic_level_bytes: 1
2025/07/02-17:08:52.211855 281473187850936          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/02-17:08:52.211856 281473187850936 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/02-17:08:52.211857 281473187850936 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/02-17:08:52.211857 281473187850936 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/02-17:08:52.211858 281473187850936 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/02-17:08:52.211859 281473187850936 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/02-17:08:52.211859 281473187850936 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/02-17:08:52.211860 281473187850936 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/02-17:08:52.211860 281473187850936       Options.max_sequential_skip_in_iterations: 8
2025/07/02-17:08:52.211861 281473187850936                    Options.max_compaction_bytes: 1677721600
2025/07/02-17:08:52.211862 281473187850936   Options.ignore_max_compaction_bytes_for_input: true
2025/07/02-17:08:52.211862 281473187850936                        Options.arena_block_size: 1048576
2025/07/02-17:08:52.211863 281473187850936   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/02-17:08:52.211863 281473187850936   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/02-17:08:52.211864 281473187850936                Options.disable_auto_compactions: 0
2025/07/02-17:08:52.211865 281473187850936                        Options.compaction_style: kCompactionStyleLevel
2025/07/02-17:08:52.211866 281473187850936                          Options.compaction_pri: kMinOverlappingRatio
2025/07/02-17:08:52.211866 281473187850936 Options.compaction_options_universal.size_ratio: 1
2025/07/02-17:08:52.211867 281473187850936 Options.compaction_options_universal.min_merge_width: 2
2025/07/02-17:08:52.211868 281473187850936 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/02-17:08:52.211868 281473187850936 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/02-17:08:52.211869 281473187850936 Options.compaction_options_universal.compression_size_percent: -1
2025/07/02-17:08:52.211870 281473187850936 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/02-17:08:52.211870 281473187850936 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/02-17:08:52.211871 281473187850936 Options.compaction_options_fifo.allow_compaction: 0
2025/07/02-17:08:52.211874 281473187850936                   Options.table_properties_collectors: 
2025/07/02-17:08:52.211874 281473187850936                   Options.inplace_update_support: 0
2025/07/02-17:08:52.211875 281473187850936                 Options.inplace_update_num_locks: 10000
2025/07/02-17:08:52.211876 281473187850936               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/07/02-17:08:52.211876 281473187850936               Options.memtable_whole_key_filtering: 0
2025/07/02-17:08:52.211877 281473187850936   Options.memtable_huge_page_size: 0
2025/07/02-17:08:52.211877 281473187850936                           Options.bloom_locality: 0
2025/07/02-17:08:52.211878 281473187850936                    Options.max_successive_merges: 0
2025/07/02-17:08:52.211878 281473187850936                Options.optimize_filters_for_hits: 0
2025/07/02-17:08:52.211879 281473187850936                Options.paranoid_file_checks: 0
2025/07/02-17:08:52.211879 281473187850936                Options.force_consistency_checks: 1
2025/07/02-17:08:52.211880 281473187850936                Options.report_bg_io_stats: 0
2025/07/02-17:08:52.211880 281473187850936                               Options.ttl: 2592000
2025/07/02-17:08:52.211881 281473187850936          Options.periodic_compaction_seconds: 0
2025/07/02-17:08:52.211882 281473187850936                        Options.default_temperature: kUnknown
2025/07/02-17:08:52.211882 281473187850936  Options.preclude_last_level_data_seconds: 0
2025/07/02-17:08:52.211883 281473187850936    Options.preserve_internal_time_seconds: 0
2025/07/02-17:08:52.211883 281473187850936                       Options.enable_blob_files: false
2025/07/02-17:08:52.211884 281473187850936                           Options.min_blob_size: 0
2025/07/02-17:08:52.211885 281473187850936                          Options.blob_file_size: 268435456
2025/07/02-17:08:52.211885 281473187850936                   Options.blob_compression_type: NoCompression
2025/07/02-17:08:52.211886 281473187850936          Options.enable_blob_garbage_collection: false
2025/07/02-17:08:52.211887 281473187850936      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/02-17:08:52.211887 281473187850936 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/02-17:08:52.211888 281473187850936          Options.blob_compaction_readahead_size: 0
2025/07/02-17:08:52.211888 281473187850936                Options.blob_file_starting_level: 0
2025/07/02-17:08:52.211889 281473187850936         Options.experimental_mempurge_threshold: 0.000000
2025/07/02-17:08:52.211890 281473187850936            Options.memtable_max_range_deletions: 0
2025/07/02-17:08:52.215963 281473187850936 [/column_family.cc:618] --------------- Options for column family [Configuration]:
2025/07/02-17:08:52.215968 281473187850936               Options.comparator: leveldb.BytewiseComparator
2025/07/02-17:08:52.215969 281473187850936           Options.merge_operator: StringAppendOperator
2025/07/02-17:08:52.215970 281473187850936        Options.compaction_filter: None
2025/07/02-17:08:52.215970 281473187850936        Options.compaction_filter_factory: None
2025/07/02-17:08:52.215971 281473187850936  Options.sst_partitioner_factory: None
2025/07/02-17:08:52.215972 281473187850936         Options.memtable_factory: SkipListFactory
2025/07/02-17:08:52.215972 281473187850936            Options.table_factory: BlockBasedTable
2025/07/02-17:08:52.215994 281473187850936            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff831f9cd0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff852a59f0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/02-17:08:52.216006 281473187850936        Options.write_buffer_size: 134217728
2025/07/02-17:08:52.216007 281473187850936  Options.max_write_buffer_number: 6
2025/07/02-17:08:52.216009 281473187850936        Options.compression[0]: NoCompression
2025/07/02-17:08:52.216010 281473187850936        Options.compression[1]: NoCompression
2025/07/02-17:08:52.216010 281473187850936        Options.compression[2]: LZ4
2025/07/02-17:08:52.216011 281473187850936        Options.compression[3]: LZ4
2025/07/02-17:08:52.216012 281473187850936        Options.compression[4]: LZ4
2025/07/02-17:08:52.216012 281473187850936        Options.compression[5]: LZ4
2025/07/02-17:08:52.216013 281473187850936        Options.compression[6]: LZ4
2025/07/02-17:08:52.216013 281473187850936                  Options.bottommost_compression: Disabled
2025/07/02-17:08:52.216015 281473187850936       Options.prefix_extractor: rocksdb.FixedPrefix
2025/07/02-17:08:52.216015 281473187850936   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/02-17:08:52.216016 281473187850936             Options.num_levels: 7
2025/07/02-17:08:52.216017 281473187850936        Options.min_write_buffer_number_to_merge: 2
2025/07/02-17:08:52.216017 281473187850936     Options.max_write_buffer_number_to_maintain: 0
2025/07/02-17:08:52.216018 281473187850936     Options.max_write_buffer_size_to_maintain: 0
2025/07/02-17:08:52.216020 281473187850936            Options.bottommost_compression_opts.window_bits: -14
2025/07/02-17:08:52.216021 281473187850936                  Options.bottommost_compression_opts.level: 32767
2025/07/02-17:08:52.216021 281473187850936               Options.bottommost_compression_opts.strategy: 0
2025/07/02-17:08:52.216022 281473187850936         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/02-17:08:52.216022 281473187850936         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/02-17:08:52.216023 281473187850936         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/02-17:08:52.216024 281473187850936                  Options.bottommost_compression_opts.enabled: false
2025/07/02-17:08:52.216025 281473187850936         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/02-17:08:52.216025 281473187850936         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/02-17:08:52.216026 281473187850936            Options.compression_opts.window_bits: -14
2025/07/02-17:08:52.216027 281473187850936                  Options.compression_opts.level: 32767
2025/07/02-17:08:52.216027 281473187850936               Options.compression_opts.strategy: 0
2025/07/02-17:08:52.216028 281473187850936         Options.compression_opts.max_dict_bytes: 0
2025/07/02-17:08:52.216028 281473187850936         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/02-17:08:52.216029 281473187850936         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/02-17:08:52.216029 281473187850936         Options.compression_opts.parallel_threads: 1
2025/07/02-17:08:52.216030 281473187850936                  Options.compression_opts.enabled: false
2025/07/02-17:08:52.216031 281473187850936         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/02-17:08:52.216031 281473187850936      Options.level0_file_num_compaction_trigger: 2
2025/07/02-17:08:52.216032 281473187850936          Options.level0_slowdown_writes_trigger: 20
2025/07/02-17:08:52.216032 281473187850936              Options.level0_stop_writes_trigger: 40
2025/07/02-17:08:52.216033 281473187850936                   Options.target_file_size_base: 67108864
2025/07/02-17:08:52.216033 281473187850936             Options.target_file_size_multiplier: 1
2025/07/02-17:08:52.216034 281473187850936                Options.max_bytes_for_level_base: 536870912
2025/07/02-17:08:52.216035 281473187850936 Options.level_compaction_dynamic_level_bytes: 1
2025/07/02-17:08:52.216035 281473187850936          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/02-17:08:52.216036 281473187850936 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/02-17:08:52.216037 281473187850936 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/02-17:08:52.216037 281473187850936 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/02-17:08:52.216038 281473187850936 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/02-17:08:52.216038 281473187850936 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/02-17:08:52.216039 281473187850936 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/02-17:08:52.216040 281473187850936 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/02-17:08:52.216040 281473187850936       Options.max_sequential_skip_in_iterations: 8
2025/07/02-17:08:52.216041 281473187850936                    Options.max_compaction_bytes: 1677721600
2025/07/02-17:08:52.216041 281473187850936   Options.ignore_max_compaction_bytes_for_input: true
2025/07/02-17:08:52.216042 281473187850936                        Options.arena_block_size: 1048576
2025/07/02-17:08:52.216042 281473187850936   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/02-17:08:52.216043 281473187850936   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/02-17:08:52.216044 281473187850936                Options.disable_auto_compactions: 0
2025/07/02-17:08:52.216045 281473187850936                        Options.compaction_style: kCompactionStyleLevel
2025/07/02-17:08:52.216046 281473187850936                          Options.compaction_pri: kMinOverlappingRatio
2025/07/02-17:08:52.216047 281473187850936 Options.compaction_options_universal.size_ratio: 1
2025/07/02-17:08:52.216047 281473187850936 Options.compaction_options_universal.min_merge_width: 2
2025/07/02-17:08:52.216048 281473187850936 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/02-17:08:52.216049 281473187850936 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/02-17:08:52.216049 281473187850936 Options.compaction_options_universal.compression_size_percent: -1
2025/07/02-17:08:52.216050 281473187850936 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/02-17:08:52.216051 281473187850936 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/02-17:08:52.216051 281473187850936 Options.compaction_options_fifo.allow_compaction: 0
2025/07/02-17:08:52.216053 281473187850936                   Options.table_properties_collectors: 
2025/07/02-17:08:52.216054 281473187850936                   Options.inplace_update_support: 0
2025/07/02-17:08:52.216055 281473187850936                 Options.inplace_update_num_locks: 10000
2025/07/02-17:08:52.216055 281473187850936               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/07/02-17:08:52.216056 281473187850936               Options.memtable_whole_key_filtering: 0
2025/07/02-17:08:52.216056 281473187850936   Options.memtable_huge_page_size: 0
2025/07/02-17:08:52.216057 281473187850936                           Options.bloom_locality: 0
2025/07/02-17:08:52.216058 281473187850936                    Options.max_successive_merges: 0
2025/07/02-17:08:52.216058 281473187850936                Options.optimize_filters_for_hits: 0
2025/07/02-17:08:52.216059 281473187850936                Options.paranoid_file_checks: 0
2025/07/02-17:08:52.216059 281473187850936                Options.force_consistency_checks: 1
2025/07/02-17:08:52.216060 281473187850936                Options.report_bg_io_stats: 0
2025/07/02-17:08:52.216101 281473187850936                               Options.ttl: 2592000
2025/07/02-17:08:52.216103 281473187850936          Options.periodic_compaction_seconds: 0
2025/07/02-17:08:52.216105 281473187850936                        Options.default_temperature: kUnknown
2025/07/02-17:08:52.216106 281473187850936  Options.preclude_last_level_data_seconds: 0
2025/07/02-17:08:52.216106 281473187850936    Options.preserve_internal_time_seconds: 0
2025/07/02-17:08:52.216107 281473187850936                       Options.enable_blob_files: false
2025/07/02-17:08:52.216109 281473187850936                           Options.min_blob_size: 0
2025/07/02-17:08:52.216110 281473187850936                          Options.blob_file_size: 268435456
2025/07/02-17:08:52.216110 281473187850936                   Options.blob_compression_type: NoCompression
2025/07/02-17:08:52.216111 281473187850936          Options.enable_blob_garbage_collection: false
2025/07/02-17:08:52.216111 281473187850936      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/02-17:08:52.216112 281473187850936 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/02-17:08:52.216113 281473187850936          Options.blob_compaction_readahead_size: 0
2025/07/02-17:08:52.216113 281473187850936                Options.blob_file_starting_level: 0
2025/07/02-17:08:52.216114 281473187850936         Options.experimental_mempurge_threshold: 0.000000
2025/07/02-17:08:52.216115 281473187850936            Options.memtable_max_range_deletions: 0
2025/07/02-17:08:52.407282 281473187850936 [/version_set.cc:5957] Recovered from manifest file:/home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/MANIFEST-000059 succeeded,manifest_file_number is 59, next_file_number is 61, last_sequence is 124, log_number is 49,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 49
2025/07/02-17:08:52.407293 281473187850936 [/version_set.cc:5966] Column family [default] (ID 0), log number is 49
2025/07/02-17:08:52.407299 281473187850936 [/version_set.cc:5966] Column family [Configuration] (ID 1), log number is 49
2025/07/02-17:08:52.407886 281473187850936 [/db_impl/db_impl_open.cc:646] DB ID: fc6e7391-20f8-4f84-9c2f-8e24a848de88
2025/07/02-17:08:52.413619 281473187850936 EVENT_LOG_v1 {"time_micros": 1751447332413609, "job": 1, "event": "recovery_started", "wal_files": [58]}
2025/07/02-17:08:52.413634 281473187850936 [/db_impl/db_impl_open.cc:1145] Recovering log #58 mode 2
2025/07/02-17:08:52.416533 281473187850936 EVENT_LOG_v1 {"time_micros": 1751447332416496, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 62, "file_size": 1067, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 126, "largest_seqno": 126, "table_properties": {"data_size": 0, "index_size": 13, "index_partitions": 0, "top_level_index_size": 8, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 0, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751447332, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "fc6e7391-20f8-4f84-9c2f-8e24a848de88", "db_session_id": "CM9FL0HBYPXSQ82NXPY7", "orig_file_number": 62, "seqno_to_time_mapping": "N/A"}}
2025/07/02-17:08:52.501350 281473187850936 EVENT_LOG_v1 {"time_micros": 1751447332501283, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 63, "file_size": 1218, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 127, "largest_seqno": 128, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 42, "raw_average_key_size": 21, "raw_value_size": 16, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751447332, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "fc6e7391-20f8-4f84-9c2f-8e24a848de88", "db_session_id": "CM9FL0HBYPXSQ82NXPY7", "orig_file_number": 63, "seqno_to_time_mapping": "N/A"}}
2025/07/02-17:08:52.518620 281473187850936 EVENT_LOG_v1 {"time_micros": 1751447332518590, "job": 1, "event": "recovery_finished"}
2025/07/02-17:08:52.519298 281473187850936 [/version_set.cc:5417] Creating manifest 65
2025/07/02-17:08:52.614175 281473187850936 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/000058.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-17:08:52.614811 281473187850936 [/db_impl/db_impl_open.cc:2150] SstFileManager instance 0xffff831fdaa0
2025/07/02-17:08:52.614961 281472879732200 [/compaction/compaction_job.cc:2080] [default] [JOB 3] Compacting 2@0 files to L6, score 1.00
2025/07/02-17:08:52.614970 281472879732200 [/compaction/compaction_job.cc:2084] [default]: Compaction start summary: Base version 4 Base level 0, inputs: [62(1067B) 56(1067B)]
2025/07/02-17:08:52.614991 281472879732200 EVENT_LOG_v1 {"time_micros": 1751447332614980, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [62, 56], "score": 1, "input_data_size": 2134, "oldest_snapshot_seqno": -1}
2025/07/02-17:08:52.616030 281473187850936 DB pointer 0xffff8591d280
2025/07/02-17:08:52.620808 281472879732200 (Original Log Time 2025/07/02-17:08:52.620138) [/compaction/compaction_job.cc:1733] [default] [JOB 3] Compacted 2@0 files to L6 => 1024 bytes
2025/07/02-17:08:52.620810 281472879732200 (Original Log Time 2025/07/02-17:08:52.620713) [/compaction/compaction_job.cc:926] [default] compacted to: files[0 0 0 0 0 0 0] max score 0.00, MB/sec: 0.6 rd, 0.3 wr, level 6, files in(2, 0) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(1.5) write-amplify(0.5) OK, records in: 2, records dropped: 2 output_compression: NoCompression
2025/07/02-17:08:52.621196 281472879732200 (Original Log Time 2025/07/02-17:08:52.620767) EVENT_LOG_v1 {"time_micros": 1751447332620731, "job": 3, "event": "compaction_finished", "compaction_time_micros": 3622, "compaction_time_cpu_micros": 796, "output_level": 6, "num_output_files": 1, "total_output_size": 1024, "num_input_records": 2, "num_output_records": 0, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 0, 0, 0, 0, 0, 0]}
2025/07/02-17:08:52.622483 281472879732200 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/000056.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-17:08:52.622515 281472879732200 EVENT_LOG_v1 {"time_micros": 1751447332622510, "job": 3, "event": "table_file_deletion", "file_number": 56}
2025/07/02-17:08:52.622914 281472879732200 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/000062.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-17:08:52.622924 281472879732200 EVENT_LOG_v1 {"time_micros": 1751447332622922, "job": 3, "event": "table_file_deletion", "file_number": 62}
2025/07/02-17:08:52.623225 281472879732200 [/compaction/compaction_job.cc:2080] [Configuration] [JOB 6] Compacting 2@0 + 1@6 files to L6, score 1.00
2025/07/02-17:08:52.623244 281472879732200 [/compaction/compaction_job.cc:2084] [Configuration]: Compaction start summary: Base version 5 Base level 0, inputs: [63(1218B) 57(1218B)], [53(1174B)]
2025/07/02-17:08:52.623274 281472879732200 EVENT_LOG_v1 {"time_micros": 1751447332623250, "job": 6, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [63, 57], "files_L6": [53], "score": 1, "input_data_size": 3610, "oldest_snapshot_seqno": -1}
2025/07/02-17:08:52.625108 281472879732200 [/compaction/compaction_job.cc:1661] [Configuration] [JOB 6] Generated table #69: 1 keys, 1174 bytes, temperature: kUnknown
2025/07/02-17:08:52.625175 281472879732200 EVENT_LOG_v1 {"time_micros": 1751447332625141, "cf_name": "Configuration", "job": 6, "event": "table_file_creation", "file_number": 69, "file_size": 1174, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 0, "largest_seqno": 0, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 26, "raw_average_key_size": 26, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751248197, "oldest_key_time": 0, "file_creation_time": 1751447332, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "fc6e7391-20f8-4f84-9c2f-8e24a848de88", "db_session_id": "CM9FL0HBYPXSQ82NXPY7", "orig_file_number": 69, "seqno_to_time_mapping": "N/A"}}
2025/07/02-17:08:52.626745 281472879732200 (Original Log Time 2025/07/02-17:08:52.626220) [/compaction/compaction_job.cc:1733] [Configuration] [JOB 6] Compacted 2@0 + 1@6 files to L6 => 1174 bytes
2025/07/02-17:08:52.626748 281472879732200 (Original Log Time 2025/07/02-17:08:52.626656) [/compaction/compaction_job.cc:926] [Configuration] compacted to: base level 6 level multiplier 10.00 max bytes base 536870912 files[0 0 0 0 0 0 1] max score 0.00, MB/sec: 1.6 rd, 0.5 wr, level 6, files in(2, 1) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(2.0) write-amplify(0.5) OK, records in: 5, records dropped: 4 output_compression: NoCompression
2025/07/02-17:08:52.626763 281472879732200 (Original Log Time 2025/07/02-17:08:52.626684) EVENT_LOG_v1 {"time_micros": 1751447332626668, "job": 6, "event": "compaction_finished", "compaction_time_micros": 2205, "compaction_time_cpu_micros": 667, "output_level": 6, "num_output_files": 1, "total_output_size": 1174, "num_input_records": 5, "num_output_records": 1, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 0, 0, 0, 0, 0, 1]}
2025/07/02-17:08:52.700603 281472879732200 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/000053.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-17:08:52.700668 281472879732200 EVENT_LOG_v1 {"time_micros": 1751447332700634, "job": 6, "event": "table_file_deletion", "file_number": 53}
2025/07/02-17:08:52.701041 281472879732200 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/000057.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-17:08:52.701055 281472879732200 EVENT_LOG_v1 {"time_micros": 1751447332701053, "job": 6, "event": "table_file_deletion", "file_number": 57}
2025/07/02-17:08:52.701427 281472879732200 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/000063.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-17:08:52.701441 281472879732200 EVENT_LOG_v1 {"time_micros": 1751447332701439, "job": 6, "event": "table_file_deletion", "file_number": 63}
2025/07/02-17:08:55.698225 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-17:08:55.698296 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 3.4 total, 3.4 interval
Cumulative writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
  L6      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.6      0.3      0.00              0.00         1    0.004       2      2       0.0       0.0
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.4      0.4      0.01              0.00         2    0.003       2      2       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.4      0.4      0.01              0.00         2    0.003       2      2       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.6      0.3      0.00              0.00         1    0.004       2      2       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.4 total, 3.4 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff852a59f0#1 capacity: 8.00 MB seed: 2130392324 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 1 last_copies: 1 last_secs: 3.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 10 Average: 43.4000  StdDev: 117.41
Min: 1  Median: 1.5000  Max: 395
Percentiles: P50: 1.50 P75: 2.50 P99: 395.00 P99.9: 395.00 P99.99: 395.00
------------------------------------------------------
[       0,       1 ]        3  30.000%  30.000% ######
(       1,       2 ]        4  40.000%  70.000% ########
(       2,       3 ]        1  10.000%  80.000% ##
(      22,      34 ]        1  10.000%  90.000% ##
(     380,     580 ]        1  10.000% 100.000% ##


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0
  L6      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      1.6      0.5      0.00              0.00         1    0.002       5      4       0.0       0.0
 Sum      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.0      0.0      0.09              0.00         2    0.043       5      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.0      0.0      0.09              0.00         2    0.043       5      4       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      1.6      0.5      0.00              0.00         1    0.002       5      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.4 total, 3.4 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff852a59f0#1 capacity: 8.00 MB seed: 2130392324 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 1 last_copies: 1 last_secs: 3.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 14 Average: 50.8571  StdDev: 109.98
Min: 1  Median: 3.5000  Max: 408
Percentiles: P50: 3.50 P75: 18.50 P99: 408.00 P99.9: 408.00 P99.99: 408.00
------------------------------------------------------
[       0,       1 ]        1   7.143%   7.143% #
(       1,       2 ]        4  28.571%  35.714% ######
(       2,       3 ]        1   7.143%  42.857% #
(       3,       4 ]        2  14.286%  57.143% ###
(       4,       6 ]        2  14.286%  71.429% ###
(      15,      22 ]        1   7.143%  78.571% #
(      51,      76 ]        1   7.143%  85.714% #
(     170,     250 ]        1   7.143%  92.857% #
(     380,     580 ]        1   7.143% 100.000% #

** Level 6 read latency histogram (micros):
Count: 11 Average: 38.1818  StdDev: 101.15
Min: 0  Median: 0.9167  Max: 355
Percentiles: P50: 0.92 P75: 4.50 P99: 355.00 P99.9: 355.00 P99.99: 355.00
------------------------------------------------------
[       0,       1 ]        6  54.545%  54.545% ###########
(       1,       2 ]        1   9.091%  63.636% ##
(       3,       4 ]        1   9.091%  72.727% ##
(       4,       6 ]        1   9.091%  81.818% ##
(      34,      51 ]        1   9.091%  90.909% ##
(     250,     380 ]        1   9.091% 100.000% ##

2025/07/02-17:08:55.699146 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 5
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 168
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 168
rocksdb.write.self COUNT : 5
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 5
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 315.000000 P95 : 1039.000000 P99 : 1039.000000 P100 : 1039.000000 COUNT : 5 SUM : 2806
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 484.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 1 SUM : 484
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 5 SUM : 168
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-17:08:56.617020 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-17:08:56.617028 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/07/02-17:08:56.617029 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/07/02-17:18:55.700625 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-17:18:55.702032 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 603.5 total, 600.1 interval
Cumulative writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
  L6      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.6      0.3      0.00              0.00         1    0.004       2      2       0.0       0.0
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.4      0.4      0.01              0.00         2    0.003       2      2       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.6      0.3      0.00              0.00         1    0.004       2      2       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 603.5 total, 600.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff852a59f0#1 capacity: 8.00 MB seed: 2130392324 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 2 last_copies: 1 last_secs: 0.00011 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 10 Average: 43.4000  StdDev: 117.41
Min: 1  Median: 1.5000  Max: 395
Percentiles: P50: 1.50 P75: 2.50 P99: 395.00 P99.9: 395.00 P99.99: 395.00
------------------------------------------------------
[       0,       1 ]        3  30.000%  30.000% ######
(       1,       2 ]        4  40.000%  70.000% ########
(       2,       3 ]        1  10.000%  80.000% ##
(      22,      34 ]        1  10.000%  90.000% ##
(     380,     580 ]        1  10.000% 100.000% ##


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0
  L6      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      1.6      0.5      0.00              0.00         1    0.002       5      4       0.0       0.0
 Sum      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.0      0.0      0.09              0.00         2    0.043       5      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      1.6      0.5      0.00              0.00         1    0.002       5      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 603.5 total, 600.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff852a59f0#1 capacity: 8.00 MB seed: 2130392324 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 2 last_copies: 1 last_secs: 0.00011 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 14 Average: 50.8571  StdDev: 109.98
Min: 1  Median: 3.5000  Max: 408
Percentiles: P50: 3.50 P75: 18.50 P99: 408.00 P99.9: 408.00 P99.99: 408.00
------------------------------------------------------
[       0,       1 ]        1   7.143%   7.143% #
(       1,       2 ]        4  28.571%  35.714% ######
(       2,       3 ]        1   7.143%  42.857% #
(       3,       4 ]        2  14.286%  57.143% ###
(       4,       6 ]        2  14.286%  71.429% ###
(      15,      22 ]        1   7.143%  78.571% #
(      51,      76 ]        1   7.143%  85.714% #
(     170,     250 ]        1   7.143%  92.857% #
(     380,     580 ]        1   7.143% 100.000% #

** Level 6 read latency histogram (micros):
Count: 11 Average: 38.1818  StdDev: 101.15
Min: 0  Median: 0.9167  Max: 355
Percentiles: P50: 0.92 P75: 4.50 P99: 355.00 P99.9: 355.00 P99.99: 355.00
------------------------------------------------------
[       0,       1 ]        6  54.545%  54.545% ###########
(       1,       2 ]        1   9.091%  63.636% ##
(       3,       4 ]        1   9.091%  72.727% ##
(       4,       6 ]        1   9.091%  81.818% ##
(      34,      51 ]        1   9.091%  90.909% ##
(     250,     380 ]        1   9.091% 100.000% ##

2025/07/02-17:18:55.704076 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 5
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 168
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 168
rocksdb.write.self COUNT : 5
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 5
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 315.000000 P95 : 1039.000000 P99 : 1039.000000 P100 : 1039.000000 COUNT : 5 SUM : 2806
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 484.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 1 SUM : 484
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 5 SUM : 168
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-17:18:56.619052 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-17:18:56.619427 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751447936 to in-memory stats history
2025/07/02-17:18:56.619436 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 15127 bytes, slice count: 1
2025/07/02-17:18:56.619438 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 15127 bytes, slice count: 1
2025/07/02-17:28:55.705067 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-17:28:55.705791 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 1203.5 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 2 syncs, 4.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3 writes, 3 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
  L6      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.6      0.3      0.00              0.00         1    0.004       2      2       0.0       0.0
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.4      0.4      0.01              0.00         2    0.003       2      2       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.6      0.3      0.00              0.00         1    0.004       2      2       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1203.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff852a59f0#1 capacity: 8.00 MB seed: 2130392324 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 3 last_copies: 1 last_secs: 9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 10 Average: 43.4000  StdDev: 117.41
Min: 1  Median: 1.5000  Max: 395
Percentiles: P50: 1.50 P75: 2.50 P99: 395.00 P99.9: 395.00 P99.99: 395.00
------------------------------------------------------
[       0,       1 ]        3  30.000%  30.000% ######
(       1,       2 ]        4  40.000%  70.000% ########
(       2,       3 ]        1  10.000%  80.000% ##
(      22,      34 ]        1  10.000%  90.000% ##
(     380,     580 ]        1  10.000% 100.000% ##


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0
  L6      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      1.6      0.5      0.00              0.00         1    0.002       5      4       0.0       0.0
 Sum      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.0      0.0      0.09              0.00         2    0.043       5      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      1.6      0.5      0.00              0.00         1    0.002       5      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1203.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff852a59f0#1 capacity: 8.00 MB seed: 2130392324 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 3 last_copies: 1 last_secs: 9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 14 Average: 50.8571  StdDev: 109.98
Min: 1  Median: 3.5000  Max: 408
Percentiles: P50: 3.50 P75: 18.50 P99: 408.00 P99.9: 408.00 P99.99: 408.00
------------------------------------------------------
[       0,       1 ]        1   7.143%   7.143% #
(       1,       2 ]        4  28.571%  35.714% ######
(       2,       3 ]        1   7.143%  42.857% #
(       3,       4 ]        2  14.286%  57.143% ###
(       4,       6 ]        2  14.286%  71.429% ###
(      15,      22 ]        1   7.143%  78.571% #
(      51,      76 ]        1   7.143%  85.714% #
(     170,     250 ]        1   7.143%  92.857% #
(     380,     580 ]        1   7.143% 100.000% #

** Level 6 read latency histogram (micros):
Count: 11 Average: 38.1818  StdDev: 101.15
Min: 0  Median: 0.9167  Max: 355
Percentiles: P50: 0.92 P75: 4.50 P99: 355.00 P99.9: 355.00 P99.99: 355.00
------------------------------------------------------
[       0,       1 ]        6  54.545%  54.545% ###########
(       1,       2 ]        1   9.091%  63.636% ##
(       3,       4 ]        1   9.091%  72.727% ##
(       4,       6 ]        1   9.091%  81.818% ##
(      34,      51 ]        1   9.091%  90.909% ##
(     250,     380 ]        1   9.091% 100.000% ##

2025/07/02-17:28:55.707965 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 8
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 273
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 2
rocksdb.wal.bytes COUNT : 273
rocksdb.write.self COUNT : 8
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 8
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 250.000000 P95 : 1039.000000 P99 : 1039.000000 P100 : 1039.000000 COUNT : 8 SUM : 3988
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 110.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 2 SUM : 582
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 8 SUM : 273
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-17:28:56.620213 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-17:28:56.620461 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751448536 to in-memory stats history
2025/07/02-17:28:56.620490 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 30254 bytes, slice count: 2
2025/07/02-17:28:56.620492 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 30254 bytes, slice count: 2
2025/07/02-17:38:55.709678 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-17:38:55.710319 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 1803.5 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 2 syncs, 4.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-17:38:55.712144 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 8
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 273
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 2
rocksdb.wal.bytes COUNT : 273
rocksdb.write.self COUNT : 8
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 8
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 250.000000 P95 : 1039.000000 P99 : 1039.000000 P100 : 1039.000000 COUNT : 8 SUM : 3988
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 110.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 2 SUM : 582
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 8 SUM : 273
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-17:38:56.623081 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-17:38:56.635032 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751449136 to in-memory stats history
2025/07/02-17:38:56.635781 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 45381 bytes, slice count: 3
2025/07/02-17:38:56.635794 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 45381 bytes, slice count: 3
2025/07/02-17:48:55.713074 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-17:48:55.713549 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 2403.5 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 2 syncs, 4.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-17:48:55.714298 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 8
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 273
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 2
rocksdb.wal.bytes COUNT : 273
rocksdb.write.self COUNT : 8
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 8
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 250.000000 P95 : 1039.000000 P99 : 1039.000000 P100 : 1039.000000 COUNT : 8 SUM : 3988
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 110.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 2 SUM : 582
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 8 SUM : 273
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-17:48:56.636769 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-17:48:56.636974 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751449736 to in-memory stats history
2025/07/02-17:48:56.637009 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 60508 bytes, slice count: 4
2025/07/02-17:48:56.637011 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 60508 bytes, slice count: 4
2025/07/02-17:58:55.714995 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-17:58:55.715529 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 3003.5 total, 600.0 interval
Cumulative writes: 11 writes, 11 keys, 11 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 11 writes, 3 syncs, 3.67 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3 writes, 3 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-17:58:55.716303 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 11
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 378
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 3
rocksdb.wal.bytes COUNT : 378
rocksdb.write.self COUNT : 11
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 11
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 240.000000 P95 : 1546.000000 P99 : 1546.000000 P100 : 1546.000000 COUNT : 11 SUM : 5912
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 140.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 3 SUM : 715
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 11 SUM : 378
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-17:58:56.637938 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-17:58:56.638550 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751450336 to in-memory stats history
2025/07/02-17:58:56.638612 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 75635 bytes, slice count: 5
2025/07/02-17:58:56.638617 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 75635 bytes, slice count: 5
2025/07/02-18:08:55.717330 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-18:08:55.718840 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 3603.5 total, 600.0 interval
Cumulative writes: 11 writes, 11 keys, 11 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 11 writes, 3 syncs, 3.67 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-18:08:55.719900 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 11
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 378
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 3
rocksdb.wal.bytes COUNT : 378
rocksdb.write.self COUNT : 11
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 11
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 240.000000 P95 : 1546.000000 P99 : 1546.000000 P100 : 1546.000000 COUNT : 11 SUM : 5912
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 140.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 3 SUM : 715
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 11 SUM : 378
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-18:08:56.641163 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-18:08:56.641612 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751450936 to in-memory stats history
2025/07/02-18:08:56.641665 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 90762 bytes, slice count: 6
2025/07/02-18:08:56.641668 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 90762 bytes, slice count: 6
2025/07/02-18:18:55.721881 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-18:18:55.722531 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 4203.5 total, 600.0 interval
Cumulative writes: 11 writes, 11 keys, 11 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 11 writes, 3 syncs, 3.67 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-18:18:55.724315 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 11
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 378
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 3
rocksdb.wal.bytes COUNT : 378
rocksdb.write.self COUNT : 11
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 11
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 240.000000 P95 : 1546.000000 P99 : 1546.000000 P100 : 1546.000000 COUNT : 11 SUM : 5912
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 140.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 3 SUM : 715
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 11 SUM : 378
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-18:18:56.643401 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-18:18:56.643717 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751451536 to in-memory stats history
2025/07/02-18:18:56.643760 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 105889 bytes, slice count: 7
2025/07/02-18:18:56.643761 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 105889 bytes, slice count: 7
2025/07/02-18:28:55.728184 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-18:28:55.728909 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 4803.5 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 4 syncs, 3.50 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3 writes, 3 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-18:28:55.729881 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 14
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 483
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 4
rocksdb.wal.bytes COUNT : 483
rocksdb.write.self COUNT : 14
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 14
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 234.000000 P95 : 1480.000000 P99 : 1546.000000 P100 : 1546.000000 COUNT : 14 SUM : 7540
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 140.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 4 SUM : 852
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 14 SUM : 483
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-18:28:56.645014 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-18:28:56.645354 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751452136 to in-memory stats history
2025/07/02-18:28:56.645405 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 121016 bytes, slice count: 8
2025/07/02-18:28:56.645408 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 121016 bytes, slice count: 8
2025/07/02-18:38:55.730266 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-18:38:55.730299 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 5403.5 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 4 syncs, 3.50 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-18:38:55.731965 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 14
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 483
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 4
rocksdb.wal.bytes COUNT : 483
rocksdb.write.self COUNT : 14
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 14
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 234.000000 P95 : 1480.000000 P99 : 1546.000000 P100 : 1546.000000 COUNT : 14 SUM : 7540
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 140.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 4 SUM : 852
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 14 SUM : 483
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-18:38:56.647596 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-18:38:56.649836 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751452736 to in-memory stats history
2025/07/02-18:38:56.649880 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 136143 bytes, slice count: 9
2025/07/02-18:38:56.649882 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 136143 bytes, slice count: 9
2025/07/02-18:48:55.733160 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-18:48:55.733208 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 6003.5 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 4 syncs, 3.50 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
  L6      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.6      0.3      0.00              0.00         1    0.004       2      2       0.0       0.0
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.4      0.4      0.01              0.00         2    0.003       2      2       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.6      0.3      0.00              0.00         1    0.004       2      2       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6003.5 total, 4800.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff852a59f0#1 capacity: 8.00 MB seed: 2130392324 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 11 last_copies: 1 last_secs: 0.000107 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 10 Average: 43.4000  StdDev: 117.41
Min: 1  Median: 1.5000  Max: 395
Percentiles: P50: 1.50 P75: 2.50 P99: 395.00 P99.9: 395.00 P99.99: 395.00
------------------------------------------------------
[       0,       1 ]        3  30.000%  30.000% ######
(       1,       2 ]        4  40.000%  70.000% ########
(       2,       3 ]        1  10.000%  80.000% ##
(      22,      34 ]        1  10.000%  90.000% ##
(     380,     580 ]        1  10.000% 100.000% ##


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0
  L6      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      1.6      0.5      0.00              0.00         1    0.002       5      4       0.0       0.0
 Sum      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.0      0.0      0.09              0.00         2    0.043       5      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      1.6      0.5      0.00              0.00         1    0.002       5      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6003.5 total, 4800.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff852a59f0#1 capacity: 8.00 MB seed: 2130392324 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 11 last_copies: 1 last_secs: 0.000107 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 14 Average: 50.8571  StdDev: 109.98
Min: 1  Median: 3.5000  Max: 408
Percentiles: P50: 3.50 P75: 18.50 P99: 408.00 P99.9: 408.00 P99.99: 408.00
------------------------------------------------------
[       0,       1 ]        1   7.143%   7.143% #
(       1,       2 ]        4  28.571%  35.714% ######
(       2,       3 ]        1   7.143%  42.857% #
(       3,       4 ]        2  14.286%  57.143% ###
(       4,       6 ]        2  14.286%  71.429% ###
(      15,      22 ]        1   7.143%  78.571% #
(      51,      76 ]        1   7.143%  85.714% #
(     170,     250 ]        1   7.143%  92.857% #
(     380,     580 ]        1   7.143% 100.000% #

** Level 6 read latency histogram (micros):
Count: 11 Average: 38.1818  StdDev: 101.15
Min: 0  Median: 0.9167  Max: 355
Percentiles: P50: 0.92 P75: 4.50 P99: 355.00 P99.9: 355.00 P99.99: 355.00
------------------------------------------------------
[       0,       1 ]        6  54.545%  54.545% ###########
(       1,       2 ]        1   9.091%  63.636% ##
(       3,       4 ]        1   9.091%  72.727% ##
(       4,       6 ]        1   9.091%  81.818% ##
(      34,      51 ]        1   9.091%  90.909% ##
(     250,     380 ]        1   9.091% 100.000% ##

2025/07/02-18:48:55.734620 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 14
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 483
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 4
rocksdb.wal.bytes COUNT : 483
rocksdb.write.self COUNT : 14
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 14
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 234.000000 P95 : 1480.000000 P99 : 1546.000000 P100 : 1546.000000 COUNT : 14 SUM : 7540
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 140.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 4 SUM : 852
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 14 SUM : 483
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-18:48:56.650976 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-18:48:56.652695 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751453336 to in-memory stats history
2025/07/02-18:48:56.652742 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 151270 bytes, slice count: 10
2025/07/02-18:48:56.652745 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 151270 bytes, slice count: 10
2025/07/02-18:58:55.736982 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-18:58:55.737004 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 6603.5 total, 600.0 interval
Cumulative writes: 17 writes, 17 keys, 17 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 17 writes, 5 syncs, 3.40 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3 writes, 3 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-18:58:55.737735 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 17
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 588
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 5
rocksdb.wal.bytes COUNT : 588
rocksdb.write.self COUNT : 17
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 17
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 230.000000 P95 : 1546.000000 P99 : 1546.000000 P100 : 1546.000000 COUNT : 17 SUM : 9271
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 125.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 5 SUM : 942
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 17 SUM : 588
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-18:58:56.654367 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-18:58:56.655673 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751453936 to in-memory stats history
2025/07/02-18:58:56.655722 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 166397 bytes, slice count: 11
2025/07/02-18:58:56.655725 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 166397 bytes, slice count: 11
2025/07/02-19:08:55.738769 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-19:08:55.738826 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 7203.5 total, 600.0 interval
Cumulative writes: 17 writes, 17 keys, 17 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 17 writes, 5 syncs, 3.40 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-19:08:55.741581 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 17
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 588
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 5
rocksdb.wal.bytes COUNT : 588
rocksdb.write.self COUNT : 17
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 17
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 230.000000 P95 : 1546.000000 P99 : 1546.000000 P100 : 1546.000000 COUNT : 17 SUM : 9271
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 125.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 5 SUM : 942
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 17 SUM : 588
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-19:08:56.656554 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-19:08:56.658555 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751454536 to in-memory stats history
2025/07/02-19:08:56.658627 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 181524 bytes, slice count: 12
2025/07/02-19:08:56.658631 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 181524 bytes, slice count: 12
2025/07/02-19:18:55.743399 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-19:18:55.743491 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 7803.5 total, 600.0 interval
Cumulative writes: 17 writes, 17 keys, 17 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 17 writes, 5 syncs, 3.40 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-19:18:55.745349 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 17
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 588
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 5
rocksdb.wal.bytes COUNT : 588
rocksdb.write.self COUNT : 17
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 17
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 230.000000 P95 : 1546.000000 P99 : 1546.000000 P100 : 1546.000000 COUNT : 17 SUM : 9271
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 125.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 5 SUM : 942
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 17 SUM : 588
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-19:18:56.660247 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-19:18:56.661441 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751455136 to in-memory stats history
2025/07/02-19:18:56.661468 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 196651 bytes, slice count: 13
2025/07/02-19:18:56.661470 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 196651 bytes, slice count: 13
2025/07/02-19:28:55.746590 281472847435240 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-19:28:55.746640 281472847435240 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 8403.5 total, 600.0 interval
Cumulative writes: 20 writes, 20 keys, 20 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 20 writes, 6 syncs, 3.33 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3 writes, 3 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/07/02-19:28:55.748113 281472847435240 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 20
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 693
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 6
rocksdb.wal.bytes COUNT : 693
rocksdb.write.self COUNT : 20
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 20
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1463
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 223.333333 P95 : 1546.000000 P99 : 1546.000000 P100 : 1546.000000 COUNT : 20 SUM : 10430
rocksdb.compaction.times.micros P50 : 2900.000000 P95 : 3622.000000 P99 : 3622.000000 P100 : 3622.000000 COUNT : 2 SUM : 5827
rocksdb.compaction.times.cpu_micros P50 : 725.000000 P95 : 796.000000 P99 : 796.000000 P100 : 796.000000 COUNT : 2 SUM : 1463
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 692.000000 P99 : 692.000000 P100 : 692.000000 COUNT : 2 SUM : 970
rocksdb.compaction.outfile.sync.micros P50 : 380.000000 P95 : 451.000000 P99 : 451.000000 P100 : 451.000000 COUNT : 2 SUM : 759
rocksdb.wal.file.sync.micros P50 : 130.000000 P95 : 484.000000 P99 : 484.000000 P100 : 484.000000 COUNT : 6 SUM : 1070
rocksdb.manifest.file.sync.micros P50 : 151.000000 P95 : 167.000000 P99 : 169.000000 P100 : 169.000000 COUNT : 3 SUM : 484
rocksdb.table.open.io.micros P50 : 480.000000 P95 : 643.000000 P99 : 643.000000 P100 : 643.000000 COUNT : 6 SUM : 2487
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 8.000000 P95 : 255.000000 P99 : 255.000000 P100 : 255.000000 COUNT : 17 SUM : 397
rocksdb.write.raw.block.micros P50 : 0.818182 P95 : 263.000000 P99 : 309.000000 P100 : 309.000000 COUNT : 18 SUM : 537
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 1.833333 P95 : 405.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 35 SUM : 1566
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.625000 P95 : 46.750000 P99 : 50.000000 P100 : 50.000000 COUNT : 5 SUM : 52
rocksdb.file.read.db.open.micros P50 : 1.875000 P95 : 408.000000 P99 : 408.000000 P100 : 408.000000 COUNT : 26 SUM : 1320
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 20 SUM : 693
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-19:28:56.662083 281472847435240 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-19:28:56.663723 281472847435240 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751455736 to in-memory stats history
2025/07/02-19:28:56.663770 281472847435240 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 211778 bytes, slice count: 14
2025/07/02-19:28:56.663773 281472847435240 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 211778 bytes, slice count: 14
2025/07/02-19:37:53.723636 281472334300648 [/db_impl/db_impl.cc:487] Shutdown: canceling all background work
2025/07/02-19:37:53.734475 281472334300648 [/db_impl/db_impl.cc:668] Shutdown complete
