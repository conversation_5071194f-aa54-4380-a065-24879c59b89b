2025/06/30-10:05:54.358575 281473575824056 RocksDB version: 8.8.1
2025/06/30-10:05:54.358685 281473575824056 Compile date 2023-11-23 11:07:28
2025/06/30-10:05:54.358687 281473575824056 DB SUMMARY
2025/06/30-10:05:54.358689 281473575824056 Host name (Env):  ab4311f86f33
2025/06/30-10:05:54.358690 281473575824056 DB Session ID:  DJ073KN838NTE1LSV2OM
2025/06/30-10:05:54.359000 281473575824056 CURRENT file:  CURRENT
2025/06/30-10:05:54.359003 281473575824056 IDENTITY file:  IDENTITY
2025/06/30-10:05:54.359198 281473575824056 MANIFEST file:  MANIFEST-000017 size: 738 Bytes
2025/06/30-10:05:54.359245 281473575824056 SST files in /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log dir, Total Num: 1, files: 000021.sst 
2025/06/30-10:05:54.359287 281473575824056 Write Ahead Log file in /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log: 000016.log size: 203 ; 
2025/06/30-10:05:54.359330 281473575824056                         Options.error_if_exists: 0
2025/06/30-10:05:54.359333 281473575824056                       Options.create_if_missing: 1
2025/06/30-10:05:54.359334 281473575824056                         Options.paranoid_checks: 1
2025/06/30-10:05:54.359335 281473575824056             Options.flush_verify_memtable_count: 1
2025/06/30-10:05:54.359336 281473575824056          Options.compaction_verify_record_count: 1
2025/06/30-10:05:54.359337 281473575824056                               Options.track_and_verify_wals_in_manifest: 0
2025/06/30-10:05:54.359338 281473575824056        Options.verify_sst_unique_id_in_manifest: 1
2025/06/30-10:05:54.359339 281473575824056                                     Options.env: 0xffff97a1f980
2025/06/30-10:05:54.359340 281473575824056                                      Options.fs: PosixFileSystem
2025/06/30-10:05:54.359341 281473575824056                                Options.info_log: 0xffff96f2b670
2025/06/30-10:05:54.359342 281473575824056                Options.max_file_opening_threads: 16
2025/06/30-10:05:54.359343 281473575824056                              Options.statistics: 0xffff96e92790
2025/06/30-10:05:54.359345 281473575824056                              Options.statistics stats level: 3
2025/06/30-10:05:54.359345 281473575824056                               Options.use_fsync: 0
2025/06/30-10:05:54.359346 281473575824056                       Options.max_log_file_size: 0
2025/06/30-10:05:54.359347 281473575824056                  Options.max_manifest_file_size: 1073741824
2025/06/30-10:05:54.359348 281473575824056                   Options.log_file_time_to_roll: 0
2025/06/30-10:05:54.359349 281473575824056                       Options.keep_log_file_num: 100
2025/06/30-10:05:54.359374 281473575824056                    Options.recycle_log_file_num: 0
2025/06/30-10:05:54.359376 281473575824056                         Options.allow_fallocate: 1
2025/06/30-10:05:54.359377 281473575824056                        Options.allow_mmap_reads: 0
2025/06/30-10:05:54.359378 281473575824056                       Options.allow_mmap_writes: 0
2025/06/30-10:05:54.359379 281473575824056                        Options.use_direct_reads: 0
2025/06/30-10:05:54.359380 281473575824056                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/30-10:05:54.359381 281473575824056          Options.create_missing_column_families: 1
2025/06/30-10:05:54.359382 281473575824056                              Options.db_log_dir: 
2025/06/30-10:05:54.359383 281473575824056                                 Options.wal_dir: 
2025/06/30-10:05:54.359384 281473575824056                Options.table_cache_numshardbits: 6
2025/06/30-10:05:54.359408 281473575824056                         Options.WAL_ttl_seconds: 0
2025/06/30-10:05:54.359409 281473575824056                       Options.WAL_size_limit_MB: 0
2025/06/30-10:05:54.359410 281473575824056                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/30-10:05:54.359411 281473575824056             Options.manifest_preallocation_size: 4194304
2025/06/30-10:05:54.359412 281473575824056                     Options.is_fd_close_on_exec: 1
2025/06/30-10:05:54.359413 281473575824056                   Options.advise_random_on_open: 1
2025/06/30-10:05:54.359422 281473575824056                    Options.db_write_buffer_size: 0
2025/06/30-10:05:54.359423 281473575824056                    Options.write_buffer_manager: 0xffff96e92aa0
2025/06/30-10:05:54.359424 281473575824056         Options.access_hint_on_compaction_start: 1
2025/06/30-10:05:54.359425 281473575824056           Options.random_access_max_buffer_size: 1048576
2025/06/30-10:05:54.359426 281473575824056                      Options.use_adaptive_mutex: 0
2025/06/30-10:05:54.359427 281473575824056                            Options.rate_limiter: 0
2025/06/30-10:05:54.359429 281473575824056     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/30-10:05:54.359431 281473575824056                       Options.wal_recovery_mode: 2
2025/06/30-10:05:54.359432 281473575824056                  Options.enable_thread_tracking: 0
2025/06/30-10:05:54.359433 281473575824056                  Options.enable_pipelined_write: 0
2025/06/30-10:05:54.359434 281473575824056                  Options.unordered_write: 0
2025/06/30-10:05:54.359435 281473575824056         Options.allow_concurrent_memtable_write: 1
2025/06/30-10:05:54.359436 281473575824056      Options.enable_write_thread_adaptive_yield: 1
2025/06/30-10:05:54.359437 281473575824056             Options.write_thread_max_yield_usec: 100
2025/06/30-10:05:54.359438 281473575824056            Options.write_thread_slow_yield_usec: 3
2025/06/30-10:05:54.359439 281473575824056                               Options.row_cache: None
2025/06/30-10:05:54.359468 281473575824056                              Options.wal_filter: None
2025/06/30-10:05:54.359473 281473575824056             Options.avoid_flush_during_recovery: 0
2025/06/30-10:05:54.359474 281473575824056             Options.allow_ingest_behind: 0
2025/06/30-10:05:54.359475 281473575824056             Options.two_write_queues: 0
2025/06/30-10:05:54.359477 281473575824056             Options.manual_wal_flush: 0
2025/06/30-10:05:54.359477 281473575824056             Options.wal_compression: 0
2025/06/30-10:05:54.359478 281473575824056             Options.atomic_flush: 0
2025/06/30-10:05:54.359480 281473575824056             Options.avoid_unnecessary_blocking_io: 0
2025/06/30-10:05:54.359482 281473575824056                 Options.persist_stats_to_disk: 0
2025/06/30-10:05:54.359483 281473575824056                 Options.write_dbid_to_manifest: 0
2025/06/30-10:05:54.359526 281473575824056                 Options.log_readahead_size: 0
2025/06/30-10:05:54.359528 281473575824056                 Options.file_checksum_gen_factory: Unknown
2025/06/30-10:05:54.359530 281473575824056                 Options.best_efforts_recovery: 0
2025/06/30-10:05:54.359531 281473575824056                Options.max_bgerror_resume_count: 2147483647
2025/06/30-10:05:54.359532 281473575824056            Options.bgerror_resume_retry_interval: 1000000
2025/06/30-10:05:54.359533 281473575824056             Options.allow_data_in_errors: 0
2025/06/30-10:05:54.359540 281473575824056             Options.db_host_id: __hostname__
2025/06/30-10:05:54.359541 281473575824056             Options.enforce_single_del_contracts: true
2025/06/30-10:05:54.359542 281473575824056             Options.max_background_jobs: 2
2025/06/30-10:05:54.359543 281473575824056             Options.max_background_compactions: -1
2025/06/30-10:05:54.359544 281473575824056             Options.max_subcompactions: 1
2025/06/30-10:05:54.359545 281473575824056             Options.avoid_flush_during_shutdown: 0
2025/06/30-10:05:54.359546 281473575824056           Options.writable_file_max_buffer_size: 1048576
2025/06/30-10:05:54.359548 281473575824056             Options.delayed_write_rate : 16777216
2025/06/30-10:05:54.359549 281473575824056             Options.max_total_wal_size: 1073741824
2025/06/30-10:05:54.359550 281473575824056             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/30-10:05:54.359551 281473575824056                   Options.stats_dump_period_sec: 600
2025/06/30-10:05:54.359552 281473575824056                 Options.stats_persist_period_sec: 600
2025/06/30-10:05:54.359556 281473575824056                 Options.stats_history_buffer_size: 1048576
2025/06/30-10:05:54.359557 281473575824056                          Options.max_open_files: -1
2025/06/30-10:05:54.359558 281473575824056                          Options.bytes_per_sync: 0
2025/06/30-10:05:54.359559 281473575824056                      Options.wal_bytes_per_sync: 0
2025/06/30-10:05:54.359560 281473575824056                   Options.strict_bytes_per_sync: 0
2025/06/30-10:05:54.359561 281473575824056       Options.compaction_readahead_size: 2097152
2025/06/30-10:05:54.359562 281473575824056                  Options.max_background_flushes: -1
2025/06/30-10:05:54.359563 281473575824056 Options.daily_offpeak_time_utc: 
2025/06/30-10:05:54.359565 281473575824056 Compression algorithms supported:
2025/06/30-10:05:54.359566 281473575824056 	kZSTDNotFinalCompression supported: 1
2025/06/30-10:05:54.359569 281473575824056 	kZSTD supported: 1
2025/06/30-10:05:54.359570 281473575824056 	kXpressCompression supported: 0
2025/06/30-10:05:54.359572 281473575824056 	kLZ4HCCompression supported: 1
2025/06/30-10:05:54.359573 281473575824056 	kLZ4Compression supported: 1
2025/06/30-10:05:54.359574 281473575824056 	kBZip2Compression supported: 1
2025/06/30-10:05:54.359575 281473575824056 	kZlibCompression supported: 1
2025/06/30-10:05:54.359588 281473575824056 	kSnappyCompression supported: 1
2025/06/30-10:05:54.359593 281473575824056 Fast CRC32 supported: Supported on Arm64
2025/06/30-10:05:54.359594 281473575824056 DMutex implementation: pthread_mutex_t
2025/06/30-10:05:54.360106 281473575824056 [/version_set.cc:5906] Recovering from manifest file: /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/MANIFEST-000017
2025/06/30-10:05:54.360312 281473575824056 [/column_family.cc:618] --------------- Options for column family [default]:
2025/06/30-10:05:54.360314 281473575824056               Options.comparator: leveldb.BytewiseComparator
2025/06/30-10:05:54.360315 281473575824056           Options.merge_operator: StringAppendOperator
2025/06/30-10:05:54.360316 281473575824056        Options.compaction_filter: None
2025/06/30-10:05:54.360317 281473575824056        Options.compaction_filter_factory: None
2025/06/30-10:05:54.360317 281473575824056  Options.sst_partitioner_factory: None
2025/06/30-10:05:54.360318 281473575824056         Options.memtable_factory: SkipListFactory
2025/06/30-10:05:54.360319 281473575824056            Options.table_factory: BlockBasedTable
2025/06/30-10:05:54.360417 281473575824056            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff96e366a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff96e92920
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-10:05:54.360424 281473575824056        Options.write_buffer_size: 134217728
2025/06/30-10:05:54.360425 281473575824056  Options.max_write_buffer_number: 6
2025/06/30-10:05:54.360426 281473575824056        Options.compression[0]: NoCompression
2025/06/30-10:05:54.360427 281473575824056        Options.compression[1]: NoCompression
2025/06/30-10:05:54.360428 281473575824056        Options.compression[2]: LZ4
2025/06/30-10:05:54.360429 281473575824056        Options.compression[3]: LZ4
2025/06/30-10:05:54.360429 281473575824056        Options.compression[4]: LZ4
2025/06/30-10:05:54.360430 281473575824056        Options.compression[5]: LZ4
2025/06/30-10:05:54.360431 281473575824056        Options.compression[6]: LZ4
2025/06/30-10:05:54.360431 281473575824056                  Options.bottommost_compression: Disabled
2025/06/30-10:05:54.360432 281473575824056       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/30-10:05:54.360433 281473575824056   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-10:05:54.360434 281473575824056             Options.num_levels: 7
2025/06/30-10:05:54.360434 281473575824056        Options.min_write_buffer_number_to_merge: 2
2025/06/30-10:05:54.360435 281473575824056     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-10:05:54.360435 281473575824056     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-10:05:54.360436 281473575824056            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-10:05:54.360436 281473575824056                  Options.bottommost_compression_opts.level: 32767
2025/06/30-10:05:54.360437 281473575824056               Options.bottommost_compression_opts.strategy: 0
2025/06/30-10:05:54.360437 281473575824056         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-10:05:54.360438 281473575824056         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-10:05:54.360439 281473575824056         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-10:05:54.360439 281473575824056                  Options.bottommost_compression_opts.enabled: false
2025/06/30-10:05:54.360440 281473575824056         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-10:05:54.360440 281473575824056         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-10:05:54.360441 281473575824056            Options.compression_opts.window_bits: -14
2025/06/30-10:05:54.360442 281473575824056                  Options.compression_opts.level: 32767
2025/06/30-10:05:54.360442 281473575824056               Options.compression_opts.strategy: 0
2025/06/30-10:05:54.360443 281473575824056         Options.compression_opts.max_dict_bytes: 0
2025/06/30-10:05:54.360443 281473575824056         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-10:05:54.360444 281473575824056         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-10:05:54.360444 281473575824056         Options.compression_opts.parallel_threads: 1
2025/06/30-10:05:54.360445 281473575824056                  Options.compression_opts.enabled: false
2025/06/30-10:05:54.360445 281473575824056         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-10:05:54.360446 281473575824056      Options.level0_file_num_compaction_trigger: 2
2025/06/30-10:05:54.360447 281473575824056          Options.level0_slowdown_writes_trigger: 20
2025/06/30-10:05:54.360447 281473575824056              Options.level0_stop_writes_trigger: 40
2025/06/30-10:05:54.360448 281473575824056                   Options.target_file_size_base: 67108864
2025/06/30-10:05:54.360448 281473575824056             Options.target_file_size_multiplier: 1
2025/06/30-10:05:54.360449 281473575824056                Options.max_bytes_for_level_base: 536870912
2025/06/30-10:05:54.360449 281473575824056 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-10:05:54.360450 281473575824056          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-10:05:54.360451 281473575824056 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-10:05:54.360451 281473575824056 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-10:05:54.360452 281473575824056 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-10:05:54.360452 281473575824056 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-10:05:54.360453 281473575824056 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-10:05:54.360454 281473575824056 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-10:05:54.360455 281473575824056 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-10:05:54.360455 281473575824056       Options.max_sequential_skip_in_iterations: 8
2025/06/30-10:05:54.360456 281473575824056                    Options.max_compaction_bytes: 1677721600
2025/06/30-10:05:54.360456 281473575824056   Options.ignore_max_compaction_bytes_for_input: true
2025/06/30-10:05:54.360457 281473575824056                        Options.arena_block_size: 1048576
2025/06/30-10:05:54.360457 281473575824056   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-10:05:54.360458 281473575824056   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-10:05:54.360458 281473575824056                Options.disable_auto_compactions: 0
2025/06/30-10:05:54.360460 281473575824056                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-10:05:54.360460 281473575824056                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-10:05:54.360461 281473575824056 Options.compaction_options_universal.size_ratio: 1
2025/06/30-10:05:54.360462 281473575824056 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-10:05:54.360462 281473575824056 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-10:05:54.360463 281473575824056 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-10:05:54.360463 281473575824056 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-10:05:54.360464 281473575824056 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-10:05:54.360465 281473575824056 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-10:05:54.360465 281473575824056 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-10:05:54.360468 281473575824056                   Options.table_properties_collectors: 
2025/06/30-10:05:54.360469 281473575824056                   Options.inplace_update_support: 0
2025/06/30-10:05:54.360470 281473575824056                 Options.inplace_update_num_locks: 10000
2025/06/30-10:05:54.360470 281473575824056               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/30-10:05:54.360471 281473575824056               Options.memtable_whole_key_filtering: 0
2025/06/30-10:05:54.360472 281473575824056   Options.memtable_huge_page_size: 0
2025/06/30-10:05:54.360472 281473575824056                           Options.bloom_locality: 0
2025/06/30-10:05:54.360473 281473575824056                    Options.max_successive_merges: 0
2025/06/30-10:05:54.360473 281473575824056                Options.optimize_filters_for_hits: 0
2025/06/30-10:05:54.360474 281473575824056                Options.paranoid_file_checks: 0
2025/06/30-10:05:54.360474 281473575824056                Options.force_consistency_checks: 1
2025/06/30-10:05:54.360475 281473575824056                Options.report_bg_io_stats: 0
2025/06/30-10:05:54.360475 281473575824056                               Options.ttl: 2592000
2025/06/30-10:05:54.360476 281473575824056          Options.periodic_compaction_seconds: 0
2025/06/30-10:05:54.360476 281473575824056                        Options.default_temperature: kUnknown
2025/06/30-10:05:54.360477 281473575824056  Options.preclude_last_level_data_seconds: 0
2025/06/30-10:05:54.360478 281473575824056    Options.preserve_internal_time_seconds: 0
2025/06/30-10:05:54.360478 281473575824056                       Options.enable_blob_files: false
2025/06/30-10:05:54.360479 281473575824056                           Options.min_blob_size: 0
2025/06/30-10:05:54.360479 281473575824056                          Options.blob_file_size: 268435456
2025/06/30-10:05:54.360480 281473575824056                   Options.blob_compression_type: NoCompression
2025/06/30-10:05:54.360481 281473575824056          Options.enable_blob_garbage_collection: false
2025/06/30-10:05:54.360481 281473575824056      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-10:05:54.360483 281473575824056 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-10:05:54.360484 281473575824056          Options.blob_compaction_readahead_size: 0
2025/06/30-10:05:54.360485 281473575824056                Options.blob_file_starting_level: 0
2025/06/30-10:05:54.360485 281473575824056         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-10:05:54.360486 281473575824056            Options.memtable_max_range_deletions: 0
2025/06/30-10:05:54.365003 281473575824056 [/column_family.cc:618] --------------- Options for column family [Configuration]:
2025/06/30-10:05:54.365007 281473575824056               Options.comparator: leveldb.BytewiseComparator
2025/06/30-10:05:54.365009 281473575824056           Options.merge_operator: StringAppendOperator
2025/06/30-10:05:54.365009 281473575824056        Options.compaction_filter: None
2025/06/30-10:05:54.365010 281473575824056        Options.compaction_filter_factory: None
2025/06/30-10:05:54.365011 281473575824056  Options.sst_partitioner_factory: None
2025/06/30-10:05:54.365011 281473575824056         Options.memtable_factory: SkipListFactory
2025/06/30-10:05:54.365012 281473575824056            Options.table_factory: BlockBasedTable
2025/06/30-10:05:54.365049 281473575824056            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff96e366a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff96e92920
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-10:05:54.365057 281473575824056        Options.write_buffer_size: 134217728
2025/06/30-10:05:54.365058 281473575824056  Options.max_write_buffer_number: 6
2025/06/30-10:05:54.365059 281473575824056        Options.compression[0]: NoCompression
2025/06/30-10:05:54.365059 281473575824056        Options.compression[1]: NoCompression
2025/06/30-10:05:54.365060 281473575824056        Options.compression[2]: LZ4
2025/06/30-10:05:54.365061 281473575824056        Options.compression[3]: LZ4
2025/06/30-10:05:54.365061 281473575824056        Options.compression[4]: LZ4
2025/06/30-10:05:54.365062 281473575824056        Options.compression[5]: LZ4
2025/06/30-10:05:54.365062 281473575824056        Options.compression[6]: LZ4
2025/06/30-10:05:54.365063 281473575824056                  Options.bottommost_compression: Disabled
2025/06/30-10:05:54.365064 281473575824056       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/30-10:05:54.365065 281473575824056   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-10:05:54.365065 281473575824056             Options.num_levels: 7
2025/06/30-10:05:54.365066 281473575824056        Options.min_write_buffer_number_to_merge: 2
2025/06/30-10:05:54.365066 281473575824056     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-10:05:54.365068 281473575824056     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-10:05:54.365069 281473575824056            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-10:05:54.365070 281473575824056                  Options.bottommost_compression_opts.level: 32767
2025/06/30-10:05:54.365070 281473575824056               Options.bottommost_compression_opts.strategy: 0
2025/06/30-10:05:54.365071 281473575824056         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-10:05:54.365072 281473575824056         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-10:05:54.365072 281473575824056         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-10:05:54.365073 281473575824056                  Options.bottommost_compression_opts.enabled: false
2025/06/30-10:05:54.365074 281473575824056         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-10:05:54.365074 281473575824056         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-10:05:54.365076 281473575824056            Options.compression_opts.window_bits: -14
2025/06/30-10:05:54.365077 281473575824056                  Options.compression_opts.level: 32767
2025/06/30-10:05:54.365077 281473575824056               Options.compression_opts.strategy: 0
2025/06/30-10:05:54.365078 281473575824056         Options.compression_opts.max_dict_bytes: 0
2025/06/30-10:05:54.365079 281473575824056         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-10:05:54.365079 281473575824056         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-10:05:54.365080 281473575824056         Options.compression_opts.parallel_threads: 1
2025/06/30-10:05:54.365080 281473575824056                  Options.compression_opts.enabled: false
2025/06/30-10:05:54.365081 281473575824056         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-10:05:54.365081 281473575824056      Options.level0_file_num_compaction_trigger: 2
2025/06/30-10:05:54.365082 281473575824056          Options.level0_slowdown_writes_trigger: 20
2025/06/30-10:05:54.365084 281473575824056              Options.level0_stop_writes_trigger: 40
2025/06/30-10:05:54.365086 281473575824056                   Options.target_file_size_base: 67108864
2025/06/30-10:05:54.365087 281473575824056             Options.target_file_size_multiplier: 1
2025/06/30-10:05:54.365087 281473575824056                Options.max_bytes_for_level_base: 536870912
2025/06/30-10:05:54.365088 281473575824056 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-10:05:54.365088 281473575824056          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-10:05:54.365089 281473575824056 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-10:05:54.365090 281473575824056 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-10:05:54.365091 281473575824056 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-10:05:54.365091 281473575824056 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-10:05:54.365092 281473575824056 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-10:05:54.365092 281473575824056 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-10:05:54.365093 281473575824056 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-10:05:54.365094 281473575824056       Options.max_sequential_skip_in_iterations: 8
2025/06/30-10:05:54.365094 281473575824056                    Options.max_compaction_bytes: 1677721600
2025/06/30-10:05:54.365095 281473575824056   Options.ignore_max_compaction_bytes_for_input: true
2025/06/30-10:05:54.365095 281473575824056                        Options.arena_block_size: 1048576
2025/06/30-10:05:54.365096 281473575824056   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-10:05:54.365096 281473575824056   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-10:05:54.365097 281473575824056                Options.disable_auto_compactions: 0
2025/06/30-10:05:54.365098 281473575824056                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-10:05:54.365113 281473575824056                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-10:05:54.365115 281473575824056 Options.compaction_options_universal.size_ratio: 1
2025/06/30-10:05:54.365115 281473575824056 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-10:05:54.365116 281473575824056 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-10:05:54.365116 281473575824056 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-10:05:54.365117 281473575824056 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-10:05:54.365118 281473575824056 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-10:05:54.365119 281473575824056 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-10:05:54.365119 281473575824056 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-10:05:54.365122 281473575824056                   Options.table_properties_collectors: 
2025/06/30-10:05:54.365123 281473575824056                   Options.inplace_update_support: 0
2025/06/30-10:05:54.365123 281473575824056                 Options.inplace_update_num_locks: 10000
2025/06/30-10:05:54.365124 281473575824056               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/30-10:05:54.365125 281473575824056               Options.memtable_whole_key_filtering: 0
2025/06/30-10:05:54.365125 281473575824056   Options.memtable_huge_page_size: 0
2025/06/30-10:05:54.365127 281473575824056                           Options.bloom_locality: 0
2025/06/30-10:05:54.365128 281473575824056                    Options.max_successive_merges: 0
2025/06/30-10:05:54.365128 281473575824056                Options.optimize_filters_for_hits: 0
2025/06/30-10:05:54.365129 281473575824056                Options.paranoid_file_checks: 0
2025/06/30-10:05:54.365130 281473575824056                Options.force_consistency_checks: 1
2025/06/30-10:05:54.365130 281473575824056                Options.report_bg_io_stats: 0
2025/06/30-10:05:54.365131 281473575824056                               Options.ttl: 2592000
2025/06/30-10:05:54.365131 281473575824056          Options.periodic_compaction_seconds: 0
2025/06/30-10:05:54.365132 281473575824056                        Options.default_temperature: kUnknown
2025/06/30-10:05:54.365133 281473575824056  Options.preclude_last_level_data_seconds: 0
2025/06/30-10:05:54.365133 281473575824056    Options.preserve_internal_time_seconds: 0
2025/06/30-10:05:54.365134 281473575824056                       Options.enable_blob_files: false
2025/06/30-10:05:54.365134 281473575824056                           Options.min_blob_size: 0
2025/06/30-10:05:54.365135 281473575824056                          Options.blob_file_size: 268435456
2025/06/30-10:05:54.365136 281473575824056                   Options.blob_compression_type: NoCompression
2025/06/30-10:05:54.365136 281473575824056          Options.enable_blob_garbage_collection: false
2025/06/30-10:05:54.365137 281473575824056      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-10:05:54.365137 281473575824056 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-10:05:54.365138 281473575824056          Options.blob_compaction_readahead_size: 0
2025/06/30-10:05:54.365139 281473575824056                Options.blob_file_starting_level: 0
2025/06/30-10:05:54.365139 281473575824056         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-10:05:54.365140 281473575824056            Options.memtable_max_range_deletions: 0
2025/06/30-10:05:54.372564 281473575824056 [/version_set.cc:5957] Recovered from manifest file:/home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/MANIFEST-000017 succeeded,manifest_file_number is 17, next_file_number is 23, last_sequence is 38, log_number is 11,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 11
2025/06/30-10:05:54.372569 281473575824056 [/version_set.cc:5966] Column family [default] (ID 0), log number is 11
2025/06/30-10:05:54.372573 281473575824056 [/version_set.cc:5966] Column family [Configuration] (ID 1), log number is 11
2025/06/30-10:05:54.372809 281473575824056 [/db_impl/db_impl_open.cc:646] DB ID: fc6e7391-20f8-4f84-9c2f-8e24a848de88
2025/06/30-10:05:54.373300 281473575824056 EVENT_LOG_v1 {"time_micros": 1751249154373296, "job": 1, "event": "recovery_started", "wal_files": [16]}
2025/06/30-10:05:54.373302 281473575824056 [/db_impl/db_impl_open.cc:1145] Recovering log #16 mode 2
2025/06/30-10:05:54.457828 281473575824056 EVENT_LOG_v1 {"time_micros": 1751249154457768, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 24, "file_size": 1067, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 40, "largest_seqno": 40, "table_properties": {"data_size": 0, "index_size": 13, "index_partitions": 0, "top_level_index_size": 8, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 0, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751249154, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "fc6e7391-20f8-4f84-9c2f-8e24a848de88", "db_session_id": "DJ073KN838NTE1LSV2OM", "orig_file_number": 24, "seqno_to_time_mapping": "N/A"}}
2025/06/30-10:05:54.460789 281473575824056 EVENT_LOG_v1 {"time_micros": 1751249154460619, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 25, "file_size": 1218, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 41, "largest_seqno": 42, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 42, "raw_average_key_size": 21, "raw_value_size": 16, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751249154, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "fc6e7391-20f8-4f84-9c2f-8e24a848de88", "db_session_id": "DJ073KN838NTE1LSV2OM", "orig_file_number": 25, "seqno_to_time_mapping": "N/A"}}
2025/06/30-10:05:54.562446 281473575824056 EVENT_LOG_v1 {"time_micros": 1751249154562438, "job": 1, "event": "recovery_finished"}
2025/06/30-10:05:54.564515 281473575824056 [/version_set.cc:5417] Creating manifest 27
2025/06/30-10:05:54.570304 281473575824056 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_persistent_service_v2/log/000016.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/30-10:05:54.570438 281473575824056 [/db_impl/db_impl_open.cc:2150] SstFileManager instance 0xffff966ac810
2025/06/30-10:05:54.570586 281473575824056 DB pointer 0xffff96b25100
2025/06/30-10:05:57.573119 281473218246120 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/06/30-10:05:57.573144 281473218246120 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 3.2 total, 3.2 interval
Cumulative writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.04 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0
 Sum      1/0    1.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.2 total, 3.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff96e92920#1 capacity: 8.00 MB seed: 775045006 usage: 1.35 KB table_size: 256 occupancy: 7 collections: 1 last_copies: 1 last_secs: 4e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(4,0.52 KB,0.00634193%) IndexBlock(2,0.24 KB,0.00293255%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 5 Average: 33.4000  StdDev: 60.82
Min: 1  Median: 2.5000  Max: 155
Percentiles: P50: 2.50 P75: 5.50 P99: 155.00 P99.9: 155.00 P99.99: 155.00
------------------------------------------------------
[       0,       1 ]        1  20.000%  20.000% ####
(       1,       2 ]        1  20.000%  40.000% ####
(       2,       3 ]        1  20.000%  60.000% ####
(       4,       6 ]        1  20.000%  80.000% ####
(     110,     170 ]        1  20.000% 100.000% ####


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.19 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0
  L6      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      2/0    2.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.2 total, 3.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff96e92920#1 capacity: 8.00 MB seed: 775045006 usage: 1.35 KB table_size: 256 occupancy: 7 collections: 1 last_copies: 1 last_secs: 4e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(4,0.52 KB,0.00634193%) IndexBlock(2,0.24 KB,0.00293255%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 7 Average: 114.0000  StdDev: 228.65
Min: 2  Median: 5.0000  Max: 671
Percentiles: P50: 5.00 P75: 57.25 P99: 671.00 P99.9: 671.00 P99.99: 671.00
------------------------------------------------------
(       1,       2 ]        1  14.286%  14.286% ###
(       2,       3 ]        2  28.571%  42.857% ######
(       4,       6 ]        1  14.286%  57.143% ###
(      34,      51 ]        1  14.286%  71.429% ###
(      51,      76 ]        1  14.286%  85.714% ###
(     580,     870 ]        1  14.286% 100.000% ###

** Level 6 read latency histogram (micros):
Count: 6 Average: 14.6667  StdDev: 28.34
Min: 0  Median: 2.0000  Max: 78
Percentiles: P50: 2.00 P75: 2.75 P99: 78.00 P99.9: 78.00 P99.99: 78.00
------------------------------------------------------
[       0,       1 ]        1  16.667%  16.667% ###
(       1,       2 ]        2  33.333%  50.000% #######
(       2,       3 ]        2  33.333%  83.333% #######
(      76,     110 ]        1  16.667% 100.000% ###

2025/06/30-10:05:57.574542 281473218246120 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 6
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 6
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 2
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 2
rocksdb.block.cache.index.bytes.insert COUNT : 246
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 4
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 4
rocksdb.block.cache.data.bytes.insert COUNT : 532
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 778
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 5
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 168
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 3
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 168
rocksdb.write.self COUNT : 5
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 5
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2285
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 1
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 1174
rocksdb.last.level.read.count COUNT : 6
rocksdb.non.last.level.read.bytes COUNT : 3468
rocksdb.non.last.level.read.count COUNT : 12
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 1
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 12
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 113
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 4
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 35
rocksdb.prefetch.hits COUNT : 1
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 480.000000 P95 : 1192.500000 P99 : 1232.000000 P100 : 1232.000000 COUNT : 5 SUM : 2525
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 170.000000 P95 : 307.000000 P99 : 307.000000 P100 : 307.000000 COUNT : 2 SUM : 441
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 264.000000 P95 : 264.000000 P99 : 264.000000 P100 : 264.000000 COUNT : 1 SUM : 264
rocksdb.manifest.file.sync.micros P50 : 51.000000 P95 : 51.000000 P99 : 51.000000 P100 : 51.000000 COUNT : 1 SUM : 51
rocksdb.table.open.io.micros P50 : 361.000000 P95 : 398.000000 P99 : 398.000000 P100 : 398.000000 COUNT : 3 SUM : 1124
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 9.000000 P95 : 685.000000 P99 : 685.000000 P100 : 685.000000 COUNT : 9 SUM : 853
rocksdb.write.raw.block.micros P50 : 0.555556 P95 : 8.000000 P99 : 9.600000 P100 : 10.000000 COUNT : 10 SUM : 12
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 2.600000 P95 : 609.000000 P99 : 671.000000 P100 : 671.000000 COUNT : 18 SUM : 1053
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.open.micros P50 : 2.625000 P95 : 125.000000 P99 : 155.000000 P100 : 155.000000 COUNT : 15 SUM : 379
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 5 SUM : 168
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1133.000000 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 1 SUM : 1133
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/30-10:05:58.571768 281473218246120 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/06/30-10:05:58.571777 281473218246120 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/06/30-10:05:58.571778 281473218246120 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/06/30-10:07:42.964819 281473162675688 [/db_impl/db_impl.cc:487] Shutdown: canceling all background work
2025/06/30-10:07:42.969750 281473162675688 [/db_impl/db_impl.cc:668] Shutdown complete
