2025/07/02-14:19:04.578238 281473296902840 RocksDB version: 8.8.1
2025/07/02-14:19:04.578328 281473296902840 Compile date 2023-11-23 11:07:28
2025/07/02-14:19:04.578331 281473296902840 DB SUMMARY
2025/07/02-14:19:04.578333 281473296902840 Host name (Env):  c12ff0a377ae
2025/07/02-14:19:04.578334 281473296902840 DB Session ID:  LR5P8XSGRSWGWUKMX0PT
2025/07/02-14:19:04.578564 281473296902840 CURRENT file:  CURRENT
2025/07/02-14:19:04.578566 281473296902840 IDENTITY file:  IDENTITY
2025/07/02-14:19:04.578600 281473296902840 MANIFEST file:  MANIFEST-000043 size: 497 Bytes
2025/07/02-14:19:04.578602 281473296902840 SST files in /home/<USER>/data/protocol/raft/naming_service_metadata/log dir, Total Num: 3, files: 000037.sst 000040.sst 000041.sst 
2025/07/02-14:19:04.578603 281473296902840 Write Ahead Log file in /home/<USER>/data/protocol/raft/naming_service_metadata/log: 000042.log size: 455 ; 
2025/07/02-14:19:04.578604 281473296902840                         Options.error_if_exists: 0
2025/07/02-14:19:04.578605 281473296902840                       Options.create_if_missing: 1
2025/07/02-14:19:04.578605 281473296902840                         Options.paranoid_checks: 1
2025/07/02-14:19:04.578606 281473296902840             Options.flush_verify_memtable_count: 1
2025/07/02-14:19:04.578606 281473296902840          Options.compaction_verify_record_count: 1
2025/07/02-14:19:04.578607 281473296902840                               Options.track_and_verify_wals_in_manifest: 0
2025/07/02-14:19:04.578607 281473296902840        Options.verify_sst_unique_id_in_manifest: 1
2025/07/02-14:19:04.578608 281473296902840                                     Options.env: 0xffff8920e4d0
2025/07/02-14:19:04.578609 281473296902840                                      Options.fs: PosixFileSystem
2025/07/02-14:19:04.578609 281473296902840                                Options.info_log: 0xffff89e94fd0
2025/07/02-14:19:04.578610 281473296902840                Options.max_file_opening_threads: 16
2025/07/02-14:19:04.578611 281473296902840                              Options.statistics: 0xffff87ac94a0
2025/07/02-14:19:04.578611 281473296902840                              Options.statistics stats level: 3
2025/07/02-14:19:04.578612 281473296902840                               Options.use_fsync: 0
2025/07/02-14:19:04.578612 281473296902840                       Options.max_log_file_size: 0
2025/07/02-14:19:04.578613 281473296902840                  Options.max_manifest_file_size: 1073741824
2025/07/02-14:19:04.578614 281473296902840                   Options.log_file_time_to_roll: 0
2025/07/02-14:19:04.578614 281473296902840                       Options.keep_log_file_num: 100
2025/07/02-14:19:04.578615 281473296902840                    Options.recycle_log_file_num: 0
2025/07/02-14:19:04.578615 281473296902840                         Options.allow_fallocate: 1
2025/07/02-14:19:04.578616 281473296902840                        Options.allow_mmap_reads: 0
2025/07/02-14:19:04.578616 281473296902840                       Options.allow_mmap_writes: 0
2025/07/02-14:19:04.578617 281473296902840                        Options.use_direct_reads: 0
2025/07/02-14:19:04.578617 281473296902840                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/02-14:19:04.578618 281473296902840          Options.create_missing_column_families: 1
2025/07/02-14:19:04.578618 281473296902840                              Options.db_log_dir: 
2025/07/02-14:19:04.578619 281473296902840                                 Options.wal_dir: 
2025/07/02-14:19:04.578619 281473296902840                Options.table_cache_numshardbits: 6
2025/07/02-14:19:04.578620 281473296902840                         Options.WAL_ttl_seconds: 0
2025/07/02-14:19:04.578620 281473296902840                       Options.WAL_size_limit_MB: 0
2025/07/02-14:19:04.578621 281473296902840                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/02-14:19:04.578622 281473296902840             Options.manifest_preallocation_size: 4194304
2025/07/02-14:19:04.578622 281473296902840                     Options.is_fd_close_on_exec: 1
2025/07/02-14:19:04.578623 281473296902840                   Options.advise_random_on_open: 1
2025/07/02-14:19:04.578625 281473296902840                    Options.db_write_buffer_size: 0
2025/07/02-14:19:04.578625 281473296902840                    Options.write_buffer_manager: 0xffff87ac47a0
2025/07/02-14:19:04.578626 281473296902840         Options.access_hint_on_compaction_start: 1
2025/07/02-14:19:04.578626 281473296902840           Options.random_access_max_buffer_size: 1048576
2025/07/02-14:19:04.578627 281473296902840                      Options.use_adaptive_mutex: 0
2025/07/02-14:19:04.578627 281473296902840                            Options.rate_limiter: 0
2025/07/02-14:19:04.578628 281473296902840     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/02-14:19:04.578629 281473296902840                       Options.wal_recovery_mode: 2
2025/07/02-14:19:04.578629 281473296902840                  Options.enable_thread_tracking: 0
2025/07/02-14:19:04.578630 281473296902840                  Options.enable_pipelined_write: 0
2025/07/02-14:19:04.578630 281473296902840                  Options.unordered_write: 0
2025/07/02-14:19:04.578631 281473296902840         Options.allow_concurrent_memtable_write: 1
2025/07/02-14:19:04.578631 281473296902840      Options.enable_write_thread_adaptive_yield: 1
2025/07/02-14:19:04.578632 281473296902840             Options.write_thread_max_yield_usec: 100
2025/07/02-14:19:04.578634 281473296902840            Options.write_thread_slow_yield_usec: 3
2025/07/02-14:19:04.578634 281473296902840                               Options.row_cache: None
2025/07/02-14:19:04.578635 281473296902840                              Options.wal_filter: None
2025/07/02-14:19:04.578636 281473296902840             Options.avoid_flush_during_recovery: 0
2025/07/02-14:19:04.578636 281473296902840             Options.allow_ingest_behind: 0
2025/07/02-14:19:04.578637 281473296902840             Options.two_write_queues: 0
2025/07/02-14:19:04.578637 281473296902840             Options.manual_wal_flush: 0
2025/07/02-14:19:04.578638 281473296902840             Options.wal_compression: 0
2025/07/02-14:19:04.578638 281473296902840             Options.atomic_flush: 0
2025/07/02-14:19:04.578639 281473296902840             Options.avoid_unnecessary_blocking_io: 0
2025/07/02-14:19:04.578639 281473296902840                 Options.persist_stats_to_disk: 0
2025/07/02-14:19:04.578640 281473296902840                 Options.write_dbid_to_manifest: 0
2025/07/02-14:19:04.578640 281473296902840                 Options.log_readahead_size: 0
2025/07/02-14:19:04.578641 281473296902840                 Options.file_checksum_gen_factory: Unknown
2025/07/02-14:19:04.578642 281473296902840                 Options.best_efforts_recovery: 0
2025/07/02-14:19:04.578643 281473296902840                Options.max_bgerror_resume_count: 2147483647
2025/07/02-14:19:04.578643 281473296902840            Options.bgerror_resume_retry_interval: 1000000
2025/07/02-14:19:04.578644 281473296902840             Options.allow_data_in_errors: 0
2025/07/02-14:19:04.578644 281473296902840             Options.db_host_id: __hostname__
2025/07/02-14:19:04.578645 281473296902840             Options.enforce_single_del_contracts: true
2025/07/02-14:19:04.578645 281473296902840             Options.max_background_jobs: 2
2025/07/02-14:19:04.578646 281473296902840             Options.max_background_compactions: -1
2025/07/02-14:19:04.578647 281473296902840             Options.max_subcompactions: 1
2025/07/02-14:19:04.578647 281473296902840             Options.avoid_flush_during_shutdown: 0
2025/07/02-14:19:04.578648 281473296902840           Options.writable_file_max_buffer_size: 1048576
2025/07/02-14:19:04.578648 281473296902840             Options.delayed_write_rate : 16777216
2025/07/02-14:19:04.578649 281473296902840             Options.max_total_wal_size: 1073741824
2025/07/02-14:19:04.578649 281473296902840             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/02-14:19:04.578650 281473296902840                   Options.stats_dump_period_sec: 600
2025/07/02-14:19:04.578654 281473296902840                 Options.stats_persist_period_sec: 600
2025/07/02-14:19:04.578655 281473296902840                 Options.stats_history_buffer_size: 1048576
2025/07/02-14:19:04.578656 281473296902840                          Options.max_open_files: -1
2025/07/02-14:19:04.578656 281473296902840                          Options.bytes_per_sync: 0
2025/07/02-14:19:04.578657 281473296902840                      Options.wal_bytes_per_sync: 0
2025/07/02-14:19:04.578657 281473296902840                   Options.strict_bytes_per_sync: 0
2025/07/02-14:19:04.578658 281473296902840       Options.compaction_readahead_size: 2097152
2025/07/02-14:19:04.578658 281473296902840                  Options.max_background_flushes: -1
2025/07/02-14:19:04.578659 281473296902840 Options.daily_offpeak_time_utc: 
2025/07/02-14:19:04.578659 281473296902840 Compression algorithms supported:
2025/07/02-14:19:04.578660 281473296902840 	kZSTDNotFinalCompression supported: 1
2025/07/02-14:19:04.578661 281473296902840 	kZSTD supported: 1
2025/07/02-14:19:04.578662 281473296902840 	kXpressCompression supported: 0
2025/07/02-14:19:04.578663 281473296902840 	kLZ4HCCompression supported: 1
2025/07/02-14:19:04.578663 281473296902840 	kLZ4Compression supported: 1
2025/07/02-14:19:04.578664 281473296902840 	kBZip2Compression supported: 1
2025/07/02-14:19:04.578664 281473296902840 	kZlibCompression supported: 1
2025/07/02-14:19:04.578665 281473296902840 	kSnappyCompression supported: 1
2025/07/02-14:19:04.578667 281473296902840 Fast CRC32 supported: Supported on Arm64
2025/07/02-14:19:04.578667 281473296902840 DMutex implementation: pthread_mutex_t
2025/07/02-14:19:04.579262 281473296902840 [/version_set.cc:5906] Recovering from manifest file: /home/<USER>/data/protocol/raft/naming_service_metadata/log/MANIFEST-000043
2025/07/02-14:19:04.579406 281473296902840 [/column_family.cc:618] --------------- Options for column family [default]:
2025/07/02-14:19:04.579408 281473296902840               Options.comparator: leveldb.BytewiseComparator
2025/07/02-14:19:04.579409 281473296902840           Options.merge_operator: StringAppendOperator
2025/07/02-14:19:04.579409 281473296902840        Options.compaction_filter: None
2025/07/02-14:19:04.579410 281473296902840        Options.compaction_filter_factory: None
2025/07/02-14:19:04.579411 281473296902840  Options.sst_partitioner_factory: None
2025/07/02-14:19:04.579411 281473296902840         Options.memtable_factory: SkipListFactory
2025/07/02-14:19:04.579412 281473296902840            Options.table_factory: BlockBasedTable
2025/07/02-14:19:04.579452 281473296902840            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff86dd7680)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff87ac9620
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/02-14:19:04.579459 281473296902840        Options.write_buffer_size: 134217728
2025/07/02-14:19:04.579459 281473296902840  Options.max_write_buffer_number: 6
2025/07/02-14:19:04.579460 281473296902840        Options.compression[0]: NoCompression
2025/07/02-14:19:04.579461 281473296902840        Options.compression[1]: NoCompression
2025/07/02-14:19:04.579462 281473296902840        Options.compression[2]: LZ4
2025/07/02-14:19:04.579463 281473296902840        Options.compression[3]: LZ4
2025/07/02-14:19:04.579463 281473296902840        Options.compression[4]: LZ4
2025/07/02-14:19:04.579464 281473296902840        Options.compression[5]: LZ4
2025/07/02-14:19:04.579464 281473296902840        Options.compression[6]: LZ4
2025/07/02-14:19:04.579465 281473296902840                  Options.bottommost_compression: Disabled
2025/07/02-14:19:04.579466 281473296902840       Options.prefix_extractor: rocksdb.FixedPrefix
2025/07/02-14:19:04.579467 281473296902840   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/02-14:19:04.579467 281473296902840             Options.num_levels: 7
2025/07/02-14:19:04.579468 281473296902840        Options.min_write_buffer_number_to_merge: 2
2025/07/02-14:19:04.579468 281473296902840     Options.max_write_buffer_number_to_maintain: 0
2025/07/02-14:19:04.579469 281473296902840     Options.max_write_buffer_size_to_maintain: 0
2025/07/02-14:19:04.579469 281473296902840            Options.bottommost_compression_opts.window_bits: -14
2025/07/02-14:19:04.579470 281473296902840                  Options.bottommost_compression_opts.level: 32767
2025/07/02-14:19:04.579471 281473296902840               Options.bottommost_compression_opts.strategy: 0
2025/07/02-14:19:04.579471 281473296902840         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/02-14:19:04.579472 281473296902840         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:04.579472 281473296902840         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/02-14:19:04.579473 281473296902840                  Options.bottommost_compression_opts.enabled: false
2025/07/02-14:19:04.579473 281473296902840         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:04.579474 281473296902840         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:04.579475 281473296902840            Options.compression_opts.window_bits: -14
2025/07/02-14:19:04.579475 281473296902840                  Options.compression_opts.level: 32767
2025/07/02-14:19:04.579476 281473296902840               Options.compression_opts.strategy: 0
2025/07/02-14:19:04.579476 281473296902840         Options.compression_opts.max_dict_bytes: 0
2025/07/02-14:19:04.579477 281473296902840         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:04.579478 281473296902840         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:04.579478 281473296902840         Options.compression_opts.parallel_threads: 1
2025/07/02-14:19:04.579479 281473296902840                  Options.compression_opts.enabled: false
2025/07/02-14:19:04.579479 281473296902840         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:04.579480 281473296902840      Options.level0_file_num_compaction_trigger: 2
2025/07/02-14:19:04.579480 281473296902840          Options.level0_slowdown_writes_trigger: 20
2025/07/02-14:19:04.579481 281473296902840              Options.level0_stop_writes_trigger: 40
2025/07/02-14:19:04.579481 281473296902840                   Options.target_file_size_base: 67108864
2025/07/02-14:19:04.579482 281473296902840             Options.target_file_size_multiplier: 1
2025/07/02-14:19:04.579483 281473296902840                Options.max_bytes_for_level_base: 536870912
2025/07/02-14:19:04.579483 281473296902840 Options.level_compaction_dynamic_level_bytes: 1
2025/07/02-14:19:04.579484 281473296902840          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/02-14:19:04.579485 281473296902840 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/02-14:19:04.579485 281473296902840 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/02-14:19:04.579486 281473296902840 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/02-14:19:04.579486 281473296902840 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/02-14:19:04.579487 281473296902840 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/02-14:19:04.579488 281473296902840 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/02-14:19:04.579488 281473296902840 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/02-14:19:04.579489 281473296902840       Options.max_sequential_skip_in_iterations: 8
2025/07/02-14:19:04.579490 281473296902840                    Options.max_compaction_bytes: 1677721600
2025/07/02-14:19:04.579490 281473296902840   Options.ignore_max_compaction_bytes_for_input: true
2025/07/02-14:19:04.579491 281473296902840                        Options.arena_block_size: 1048576
2025/07/02-14:19:04.579491 281473296902840   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/02-14:19:04.579492 281473296902840   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/02-14:19:04.579492 281473296902840                Options.disable_auto_compactions: 0
2025/07/02-14:19:04.579493 281473296902840                        Options.compaction_style: kCompactionStyleLevel
2025/07/02-14:19:04.579494 281473296902840                          Options.compaction_pri: kMinOverlappingRatio
2025/07/02-14:19:04.579495 281473296902840 Options.compaction_options_universal.size_ratio: 1
2025/07/02-14:19:04.579495 281473296902840 Options.compaction_options_universal.min_merge_width: 2
2025/07/02-14:19:04.579496 281473296902840 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/02-14:19:04.579496 281473296902840 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/02-14:19:04.579497 281473296902840 Options.compaction_options_universal.compression_size_percent: -1
2025/07/02-14:19:04.579498 281473296902840 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/02-14:19:04.579498 281473296902840 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/02-14:19:04.579499 281473296902840 Options.compaction_options_fifo.allow_compaction: 0
2025/07/02-14:19:04.579501 281473296902840                   Options.table_properties_collectors: 
2025/07/02-14:19:04.579502 281473296902840                   Options.inplace_update_support: 0
2025/07/02-14:19:04.579503 281473296902840                 Options.inplace_update_num_locks: 10000
2025/07/02-14:19:04.579503 281473296902840               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/07/02-14:19:04.579504 281473296902840               Options.memtable_whole_key_filtering: 0
2025/07/02-14:19:04.579504 281473296902840   Options.memtable_huge_page_size: 0
2025/07/02-14:19:04.579505 281473296902840                           Options.bloom_locality: 0
2025/07/02-14:19:04.579505 281473296902840                    Options.max_successive_merges: 0
2025/07/02-14:19:04.579506 281473296902840                Options.optimize_filters_for_hits: 0
2025/07/02-14:19:04.579507 281473296902840                Options.paranoid_file_checks: 0
2025/07/02-14:19:04.579507 281473296902840                Options.force_consistency_checks: 1
2025/07/02-14:19:04.579508 281473296902840                Options.report_bg_io_stats: 0
2025/07/02-14:19:04.579508 281473296902840                               Options.ttl: 2592000
2025/07/02-14:19:04.579509 281473296902840          Options.periodic_compaction_seconds: 0
2025/07/02-14:19:04.579509 281473296902840                        Options.default_temperature: kUnknown
2025/07/02-14:19:04.579510 281473296902840  Options.preclude_last_level_data_seconds: 0
2025/07/02-14:19:04.579511 281473296902840    Options.preserve_internal_time_seconds: 0
2025/07/02-14:19:04.579511 281473296902840                       Options.enable_blob_files: false
2025/07/02-14:19:04.579512 281473296902840                           Options.min_blob_size: 0
2025/07/02-14:19:04.579512 281473296902840                          Options.blob_file_size: 268435456
2025/07/02-14:19:04.579513 281473296902840                   Options.blob_compression_type: NoCompression
2025/07/02-14:19:04.579514 281473296902840          Options.enable_blob_garbage_collection: false
2025/07/02-14:19:04.579514 281473296902840      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/02-14:19:04.579515 281473296902840 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/02-14:19:04.579516 281473296902840          Options.blob_compaction_readahead_size: 0
2025/07/02-14:19:04.579516 281473296902840                Options.blob_file_starting_level: 0
2025/07/02-14:19:04.579517 281473296902840         Options.experimental_mempurge_threshold: 0.000000
2025/07/02-14:19:04.579517 281473296902840            Options.memtable_max_range_deletions: 0
2025/07/02-14:19:04.583293 281473296902840 [/column_family.cc:618] --------------- Options for column family [Configuration]:
2025/07/02-14:19:04.583302 281473296902840               Options.comparator: leveldb.BytewiseComparator
2025/07/02-14:19:04.583304 281473296902840           Options.merge_operator: StringAppendOperator
2025/07/02-14:19:04.583305 281473296902840        Options.compaction_filter: None
2025/07/02-14:19:04.583306 281473296902840        Options.compaction_filter_factory: None
2025/07/02-14:19:04.583307 281473296902840  Options.sst_partitioner_factory: None
2025/07/02-14:19:04.583309 281473296902840         Options.memtable_factory: SkipListFactory
2025/07/02-14:19:04.583310 281473296902840            Options.table_factory: BlockBasedTable
2025/07/02-14:19:04.583345 281473296902840            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff86dd7680)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff87ac9620
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/02-14:19:04.583353 281473296902840        Options.write_buffer_size: 134217728
2025/07/02-14:19:04.583354 281473296902840  Options.max_write_buffer_number: 6
2025/07/02-14:19:04.583355 281473296902840        Options.compression[0]: NoCompression
2025/07/02-14:19:04.583356 281473296902840        Options.compression[1]: NoCompression
2025/07/02-14:19:04.583357 281473296902840        Options.compression[2]: LZ4
2025/07/02-14:19:04.583358 281473296902840        Options.compression[3]: LZ4
2025/07/02-14:19:04.583359 281473296902840        Options.compression[4]: LZ4
2025/07/02-14:19:04.583360 281473296902840        Options.compression[5]: LZ4
2025/07/02-14:19:04.583361 281473296902840        Options.compression[6]: LZ4
2025/07/02-14:19:04.583362 281473296902840                  Options.bottommost_compression: Disabled
2025/07/02-14:19:04.583364 281473296902840       Options.prefix_extractor: rocksdb.FixedPrefix
2025/07/02-14:19:04.583365 281473296902840   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/02-14:19:04.583366 281473296902840             Options.num_levels: 7
2025/07/02-14:19:04.583367 281473296902840        Options.min_write_buffer_number_to_merge: 2
2025/07/02-14:19:04.583368 281473296902840     Options.max_write_buffer_number_to_maintain: 0
2025/07/02-14:19:04.583368 281473296902840     Options.max_write_buffer_size_to_maintain: 0
2025/07/02-14:19:04.583369 281473296902840            Options.bottommost_compression_opts.window_bits: -14
2025/07/02-14:19:04.583372 281473296902840                  Options.bottommost_compression_opts.level: 32767
2025/07/02-14:19:04.583373 281473296902840               Options.bottommost_compression_opts.strategy: 0
2025/07/02-14:19:04.583374 281473296902840         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/02-14:19:04.583375 281473296902840         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:04.583376 281473296902840         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/02-14:19:04.583377 281473296902840                  Options.bottommost_compression_opts.enabled: false
2025/07/02-14:19:04.583378 281473296902840         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:04.583379 281473296902840         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:04.583380 281473296902840            Options.compression_opts.window_bits: -14
2025/07/02-14:19:04.583381 281473296902840                  Options.compression_opts.level: 32767
2025/07/02-14:19:04.583382 281473296902840               Options.compression_opts.strategy: 0
2025/07/02-14:19:04.583383 281473296902840         Options.compression_opts.max_dict_bytes: 0
2025/07/02-14:19:04.583384 281473296902840         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/02-14:19:04.583385 281473296902840         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/02-14:19:04.583386 281473296902840         Options.compression_opts.parallel_threads: 1
2025/07/02-14:19:04.583387 281473296902840                  Options.compression_opts.enabled: false
2025/07/02-14:19:04.583388 281473296902840         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/02-14:19:04.583388 281473296902840      Options.level0_file_num_compaction_trigger: 2
2025/07/02-14:19:04.583389 281473296902840          Options.level0_slowdown_writes_trigger: 20
2025/07/02-14:19:04.583390 281473296902840              Options.level0_stop_writes_trigger: 40
2025/07/02-14:19:04.583391 281473296902840                   Options.target_file_size_base: 67108864
2025/07/02-14:19:04.583392 281473296902840             Options.target_file_size_multiplier: 1
2025/07/02-14:19:04.583393 281473296902840                Options.max_bytes_for_level_base: 536870912
2025/07/02-14:19:04.583394 281473296902840 Options.level_compaction_dynamic_level_bytes: 1
2025/07/02-14:19:04.583395 281473296902840          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/02-14:19:04.583396 281473296902840 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/02-14:19:04.583397 281473296902840 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/02-14:19:04.583398 281473296902840 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/02-14:19:04.583399 281473296902840 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/02-14:19:04.583400 281473296902840 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/02-14:19:04.583401 281473296902840 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/02-14:19:04.583402 281473296902840 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/02-14:19:04.583403 281473296902840       Options.max_sequential_skip_in_iterations: 8
2025/07/02-14:19:04.583404 281473296902840                    Options.max_compaction_bytes: 1677721600
2025/07/02-14:19:04.583405 281473296902840   Options.ignore_max_compaction_bytes_for_input: true
2025/07/02-14:19:04.583406 281473296902840                        Options.arena_block_size: 1048576
2025/07/02-14:19:04.583407 281473296902840   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/02-14:19:04.583408 281473296902840   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/02-14:19:04.583409 281473296902840                Options.disable_auto_compactions: 0
2025/07/02-14:19:04.583411 281473296902840                        Options.compaction_style: kCompactionStyleLevel
2025/07/02-14:19:04.583412 281473296902840                          Options.compaction_pri: kMinOverlappingRatio
2025/07/02-14:19:04.583414 281473296902840 Options.compaction_options_universal.size_ratio: 1
2025/07/02-14:19:04.583415 281473296902840 Options.compaction_options_universal.min_merge_width: 2
2025/07/02-14:19:04.583416 281473296902840 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/02-14:19:04.583417 281473296902840 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/02-14:19:04.583418 281473296902840 Options.compaction_options_universal.compression_size_percent: -1
2025/07/02-14:19:04.583419 281473296902840 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/02-14:19:04.583420 281473296902840 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/02-14:19:04.583421 281473296902840 Options.compaction_options_fifo.allow_compaction: 0
2025/07/02-14:19:04.583425 281473296902840                   Options.table_properties_collectors: 
2025/07/02-14:19:04.583426 281473296902840                   Options.inplace_update_support: 0
2025/07/02-14:19:04.583427 281473296902840                 Options.inplace_update_num_locks: 10000
2025/07/02-14:19:04.583428 281473296902840               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/07/02-14:19:04.583429 281473296902840               Options.memtable_whole_key_filtering: 0
2025/07/02-14:19:04.583430 281473296902840   Options.memtable_huge_page_size: 0
2025/07/02-14:19:04.583431 281473296902840                           Options.bloom_locality: 0
2025/07/02-14:19:04.583432 281473296902840                    Options.max_successive_merges: 0
2025/07/02-14:19:04.583433 281473296902840                Options.optimize_filters_for_hits: 0
2025/07/02-14:19:04.583434 281473296902840                Options.paranoid_file_checks: 0
2025/07/02-14:19:04.583435 281473296902840                Options.force_consistency_checks: 1
2025/07/02-14:19:04.583436 281473296902840                Options.report_bg_io_stats: 0
2025/07/02-14:19:04.583436 281473296902840                               Options.ttl: 2592000
2025/07/02-14:19:04.583437 281473296902840          Options.periodic_compaction_seconds: 0
2025/07/02-14:19:04.583439 281473296902840                        Options.default_temperature: kUnknown
2025/07/02-14:19:04.583439 281473296902840  Options.preclude_last_level_data_seconds: 0
2025/07/02-14:19:04.583472 281473296902840    Options.preserve_internal_time_seconds: 0
2025/07/02-14:19:04.583474 281473296902840                       Options.enable_blob_files: false
2025/07/02-14:19:04.583475 281473296902840                           Options.min_blob_size: 0
2025/07/02-14:19:04.583476 281473296902840                          Options.blob_file_size: 268435456
2025/07/02-14:19:04.583477 281473296902840                   Options.blob_compression_type: NoCompression
2025/07/02-14:19:04.583478 281473296902840          Options.enable_blob_garbage_collection: false
2025/07/02-14:19:04.583479 281473296902840      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/02-14:19:04.583480 281473296902840 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/02-14:19:04.583481 281473296902840          Options.blob_compaction_readahead_size: 0
2025/07/02-14:19:04.583482 281473296902840                Options.blob_file_starting_level: 0
2025/07/02-14:19:04.583483 281473296902840         Options.experimental_mempurge_threshold: 0.000000
2025/07/02-14:19:04.583484 281473296902840            Options.memtable_max_range_deletions: 0
2025/07/02-14:19:04.591613 281473296902840 [/version_set.cc:5957] Recovered from manifest file:/home/<USER>/data/protocol/raft/naming_service_metadata/log/MANIFEST-000043 succeeded,manifest_file_number is 43, next_file_number is 45, last_sequence is 113, log_number is 33,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 33
2025/07/02-14:19:04.591617 281473296902840 [/version_set.cc:5966] Column family [default] (ID 0), log number is 33
2025/07/02-14:19:04.591620 281473296902840 [/version_set.cc:5966] Column family [Configuration] (ID 1), log number is 33
2025/07/02-14:19:04.591967 281473296902840 [/db_impl/db_impl_open.cc:646] DB ID: e9336bcf-bc16-41a2-95a2-12811a2eff46
2025/07/02-14:19:04.592513 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437144592510, "job": 1, "event": "recovery_started", "wal_files": [42]}
2025/07/02-14:19:04.592517 281473296902840 [/db_impl/db_impl_open.cc:1145] Recovering log #42 mode 2
2025/07/02-14:19:04.593373 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437144593361, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 46, "file_size": 1067, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 115, "largest_seqno": 115, "table_properties": {"data_size": 0, "index_size": 13, "index_partitions": 0, "top_level_index_size": 8, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 0, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751437144, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "e9336bcf-bc16-41a2-95a2-12811a2eff46", "db_session_id": "LR5P8XSGRSWGWUKMX0PT", "orig_file_number": 46, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:19:04.678520 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437144678467, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 47, "file_size": 1218, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 116, "largest_seqno": 123, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 42, "raw_average_key_size": 21, "raw_value_size": 16, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751437144, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "e9336bcf-bc16-41a2-95a2-12811a2eff46", "db_session_id": "LR5P8XSGRSWGWUKMX0PT", "orig_file_number": 47, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:19:04.679810 281473296902840 EVENT_LOG_v1 {"time_micros": 1751437144679805, "job": 1, "event": "recovery_finished"}
2025/07/02-14:19:04.680173 281473296902840 [/version_set.cc:5417] Creating manifest 49
2025/07/02-14:19:04.687493 281473296902840 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_service_metadata/log/000042.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.687720 281473296902840 [/db_impl/db_impl_open.cc:2150] SstFileManager instance 0xffff86dd9ee0
2025/07/02-14:19:04.687801 281472957408744 [/compaction/compaction_job.cc:2080] [default] [JOB 3] Compacting 2@0 files to L6, score 1.00
2025/07/02-14:19:04.687808 281472957408744 [/compaction/compaction_job.cc:2084] [default]: Compaction start summary: Base version 4 Base level 0, inputs: [46(1067B) 40(1067B)]
2025/07/02-14:19:04.687825 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144687816, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [46, 40], "score": 1, "input_data_size": 2134, "oldest_snapshot_seqno": -1}
2025/07/02-14:19:04.688208 281473296902840 DB pointer 0xffff8695e180
2025/07/02-14:19:04.777421 281472957408744 (Original Log Time 2025/07/02-14:19:04.691831) [/compaction/compaction_job.cc:1733] [default] [JOB 3] Compacted 2@0 files to L6 => 1024 bytes
2025/07/02-14:19:04.777427 281472957408744 (Original Log Time 2025/07/02-14:19:04.777256) [/compaction/compaction_job.cc:926] [default] compacted to: files[0 0 0 0 0 0 0] max score 0.00, MB/sec: 0.5 rd, 0.3 wr, level 6, files in(2, 0) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(1.5) write-amplify(0.5) OK, records in: 2, records dropped: 2 output_compression: NoCompression
2025/07/02-14:19:04.777458 281472957408744 (Original Log Time 2025/07/02-14:19:04.777324) EVENT_LOG_v1 {"time_micros": 1751437144777275, "job": 3, "event": "compaction_finished", "compaction_time_micros": 3936, "compaction_time_cpu_micros": 735, "output_level": 6, "num_output_files": 1, "total_output_size": 1024, "num_input_records": 2, "num_output_records": 0, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 0, 0, 0, 0, 0, 0]}
2025/07/02-14:19:04.778407 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_service_metadata/log/000040.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.778419 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144778417, "job": 3, "event": "table_file_deletion", "file_number": 40}
2025/07/02-14:19:04.778748 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_service_metadata/log/000046.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.778756 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144778754, "job": 3, "event": "table_file_deletion", "file_number": 46}
2025/07/02-14:19:04.779169 281472957408744 [/compaction/compaction_job.cc:2080] [Configuration] [JOB 6] Compacting 2@0 + 1@6 files to L6, score 1.00
2025/07/02-14:19:04.779173 281472957408744 [/compaction/compaction_job.cc:2084] [Configuration]: Compaction start summary: Base version 5 Base level 0, inputs: [47(1218B) 41(1218B)], [37(1174B)]
2025/07/02-14:19:04.779179 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144779174, "job": 6, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [47, 41], "files_L6": [37], "score": 1, "input_data_size": 3610, "oldest_snapshot_seqno": -1}
2025/07/02-14:19:04.784286 281472957408744 [/compaction/compaction_job.cc:1661] [Configuration] [JOB 6] Generated table #53: 1 keys, 1174 bytes, temperature: kUnknown
2025/07/02-14:19:04.784361 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144784307, "cf_name": "Configuration", "job": 6, "event": "table_file_creation", "file_number": 53, "file_size": 1174, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 0, "largest_seqno": 0, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 26, "raw_average_key_size": 26, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751248198, "oldest_key_time": 0, "file_creation_time": 1751437144, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "e9336bcf-bc16-41a2-95a2-12811a2eff46", "db_session_id": "LR5P8XSGRSWGWUKMX0PT", "orig_file_number": 53, "seqno_to_time_mapping": "N/A"}}
2025/07/02-14:19:04.786677 281472957408744 (Original Log Time 2025/07/02-14:19:04.786127) [/compaction/compaction_job.cc:1733] [Configuration] [JOB 6] Compacted 2@0 + 1@6 files to L6 => 1174 bytes
2025/07/02-14:19:04.786679 281472957408744 (Original Log Time 2025/07/02-14:19:04.786620) [/compaction/compaction_job.cc:926] [Configuration] compacted to: base level 6 level multiplier 10.00 max bytes base 536870912 files[0 0 0 0 0 0 1] max score 0.00, MB/sec: 0.6 rd, 0.2 wr, level 6, files in(2, 1) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(2.0) write-amplify(0.5) OK, records in: 5, records dropped: 4 output_compression: NoCompression
2025/07/02-14:19:04.786686 281472957408744 (Original Log Time 2025/07/02-14:19:04.786638) EVENT_LOG_v1 {"time_micros": 1751437144786631, "job": 6, "event": "compaction_finished", "compaction_time_micros": 5848, "compaction_time_cpu_micros": 735, "output_level": 6, "num_output_files": 1, "total_output_size": 1174, "num_input_records": 5, "num_output_records": 1, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 0, 0, 0, 0, 0, 1]}
2025/07/02-14:19:04.787031 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_service_metadata/log/000037.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.787074 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144787069, "job": 6, "event": "table_file_deletion", "file_number": 37}
2025/07/02-14:19:04.787423 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_service_metadata/log/000041.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.787433 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144787431, "job": 6, "event": "table_file_deletion", "file_number": 41}
2025/07/02-14:19:04.787828 281472957408744 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_service_metadata/log/000047.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/02-14:19:04.787878 281472957408744 EVENT_LOG_v1 {"time_micros": 1751437144787873, "job": 6, "event": "table_file_deletion", "file_number": 47}
2025/07/02-14:19:13.689697 281472947103208 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/07/02-14:19:13.689810 281472947103208 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 9.1 total, 9.1 interval
Cumulative writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0
  L6      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.5      0.2      0.00              0.00         1    0.004       2      2       0.0       0.0
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.0      0.0      0.09              0.00         2    0.044       2      2       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.0      0.0      0.09              0.00         2    0.044       2      2       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.5      0.2      0.00              0.00         1    0.004       2      2       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.084       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 9.1 total, 9.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff87ac9620#1 capacity: 8.00 MB seed: 1424354510 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 1 last_copies: 1 last_secs: 2.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 10 Average: 32.2000  StdDev: 87.37
Min: 0  Median: 0.8333  Max: 294
Percentiles: P50: 0.83 P75: 5.00 P99: 294.00 P99.9: 294.00 P99.99: 294.00
------------------------------------------------------
[       0,       1 ]        6  60.000%  60.000% ############
(       3,       4 ]        1  10.000%  70.000% ##
(       4,       6 ]        1  10.000%  80.000% ##
(      10,      15 ]        1  10.000%  90.000% ##
(     250,     380 ]        1  10.000% 100.000% ##


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.001       0      0       0.0       0.0
  L6      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.6      0.2      0.01              0.00         1    0.006       5      4       0.0       0.0
 Sum      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.5      0.3      0.01              0.00         2    0.004       5      4       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.5      0.3      0.01              0.00         2    0.004       5      4       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.6      0.2      0.01              0.00         1    0.006       5      4       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.0      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 9.1 total, 9.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff87ac9620#1 capacity: 8.00 MB seed: 1424354510 usage: 2.40 KB table_size: 256 occupancy: 12 collections: 1 last_copies: 1 last_secs: 2.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(7,0.90 KB,0.0110388%) IndexBlock(4,0.48 KB,0.0058651%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 14 Average: 53.2857  StdDev: 171.84
Min: 0  Median: 1.5000  Max: 672
Percentiles: P50: 1.50 P75: 3.75 P99: 672.00 P99.9: 672.00 P99.99: 672.00
------------------------------------------------------
[       0,       1 ]        6  42.857%  42.857% #########
(       1,       2 ]        2  14.286%  57.143% ###
(       2,       3 ]        1   7.143%  64.286% #
(       3,       4 ]        2  14.286%  78.571% ###
(      22,      34 ]        2  14.286%  92.857% ###
(     580,     870 ]        1   7.143% 100.000% #

** Level 6 read latency histogram (micros):
Count: 11 Average: 74.2727  StdDev: 190.00
Min: 1  Median: 4.5000  Max: 670
Percentiles: P50: 4.50 P75: 25.00 P99: 670.00 P99.9: 670.00 P99.99: 670.00
------------------------------------------------------
[       0,       1 ]        1   9.091%   9.091% ##
(       1,       2 ]        1   9.091%  18.182% ##
(       2,       3 ]        3  27.273%  45.455% #####
(       4,       6 ]        2  18.182%  63.636% ####
(       6,      10 ]        1   9.091%  72.727% ##
(      22,      34 ]        1   9.091%  81.818% ##
(      76,     110 ]        1   9.091%  90.909% ##
(     580,     870 ]        1   9.091% 100.000% ##

2025/07/02-14:19:13.690466 281472947103208 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 11
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 11
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 1
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 492
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 3
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 926
rocksdb.block.cache.bytes.read COUNT : 537
rocksdb.block.cache.bytes.write COUNT : 1418
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 2
rocksdb.compaction.key.drop.obsolete COUNT : 2
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 2
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 5
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 168
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 168
rocksdb.write.self COUNT : 5
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 5
rocksdb.compact.read.bytes COUNT : 2263
rocksdb.compact.write.bytes COUNT : 4483
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 1470
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 2
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 5
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 3437
rocksdb.last.level.read.count COUNT : 11
rocksdb.non.last.level.read.bytes COUNT : 6936
rocksdb.non.last.level.read.count COUNT : 24
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 2
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 23
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 226
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 8
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 105
rocksdb.prefetch.hits COUNT : 3
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 347.500000 P95 : 2044.000000 P99 : 2044.000000 P100 : 2044.000000 COUNT : 5 SUM : 3300
rocksdb.compaction.times.micros P50 : 4400.000000 P95 : 5848.000000 P99 : 5848.000000 P100 : 5848.000000 COUNT : 2 SUM : 9784
rocksdb.compaction.times.cpu_micros P50 : 735.000000 P95 : 735.000000 P99 : 735.000000 P100 : 735.000000 COUNT : 2 SUM : 1470
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 110.000000 P95 : 200.000000 P99 : 200.000000 P100 : 200.000000 COUNT : 2 SUM : 310
rocksdb.compaction.outfile.sync.micros P50 : 306.000000 P95 : 306.000000 P99 : 306.000000 P100 : 306.000000 COUNT : 2 SUM : 611
rocksdb.wal.file.sync.micros P50 : 185.000000 P95 : 185.000000 P99 : 185.000000 P100 : 185.000000 COUNT : 1 SUM : 185
rocksdb.manifest.file.sync.micros P50 : 210.000000 P95 : 312.000000 P99 : 312.000000 P100 : 312.000000 COUNT : 3 SUM : 609
rocksdb.table.open.io.micros P50 : 380.000000 P95 : 1058.000000 P99 : 1058.000000 P100 : 1058.000000 COUNT : 6 SUM : 3014
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 3.500000 P95 : 16.050000 P99 : 17.000000 P100 : 17.000000 COUNT : 17 SUM : 106
rocksdb.write.raw.block.micros P50 : 0.692308 P95 : 1360.000000 P99 : 1792.000000 P100 : 1868.000000 COUNT : 18 SUM : 2065
rocksdb.numfiles.in.singlecompaction P50 : 2.000000 P95 : 2.900000 P99 : 2.980000 P100 : 3.000000 COUNT : 2 SUM : 5
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 2.375000 P95 : 616.250000 P99 : 672.000000 P100 : 672.000000 COUNT : 35 SUM : 1885
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 2.750000 P95 : 89.000000 P99 : 89.000000 P100 : 89.000000 COUNT : 5 SUM : 102
rocksdb.file.read.db.open.micros P50 : 2.000000 P95 : 672.000000 P99 : 672.000000 P100 : 672.000000 COUNT : 26 SUM : 1773
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 5 SUM : 168
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1013.333333 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 4 SUM : 3355
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/07/02-14:19:14.688589 281472947103208 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/07/02-14:19:14.688600 281472947103208 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/07/02-14:19:14.688601 281472947103208 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/07/02-14:19:23.601284 281472921503208 [/db_impl/db_impl.cc:487] Shutdown: canceling all background work
2025/07/02-14:19:23.606432 281472921503208 [/db_impl/db_impl.cc:668] Shutdown complete
