2025/06/30-09:49:58.146202 281473017981624 RocksDB version: 8.8.1
2025/06/30-09:49:58.146275 281473017981624 Compile date 2023-11-23 11:07:28
2025/06/30-09:49:58.146278 281473017981624 DB SUMMARY
2025/06/30-09:49:58.146280 281473017981624 Host name (Env):  69d1372afb37
2025/06/30-09:49:58.146281 281473017981624 DB Session ID:  U9M4M1L8JZU0AV343NRV
2025/06/30-09:49:58.146459 281473017981624 CURRENT file:  CURRENT
2025/06/30-09:49:58.146461 281473017981624 IDENTITY file:  IDENTITY
2025/06/30-09:49:58.146488 281473017981624 MANIFEST file:  MANIFEST-000005 size: 134 Bytes
2025/06/30-09:49:58.146489 281473017981624 SST files in /home/<USER>/data/protocol/raft/naming_service_metadata/log dir, Total Num: 0, files: 
2025/06/30-09:49:58.146491 281473017981624 Write Ahead Log file in /home/<USER>/data/protocol/raft/naming_service_metadata/log: 000004.log size: 1543 ; 
2025/06/30-09:49:58.146492 281473017981624                         Options.error_if_exists: 0
2025/06/30-09:49:58.146493 281473017981624                       Options.create_if_missing: 1
2025/06/30-09:49:58.146493 281473017981624                         Options.paranoid_checks: 1
2025/06/30-09:49:58.146494 281473017981624             Options.flush_verify_memtable_count: 1
2025/06/30-09:49:58.146495 281473017981624          Options.compaction_verify_record_count: 1
2025/06/30-09:49:58.146495 281473017981624                               Options.track_and_verify_wals_in_manifest: 0
2025/06/30-09:49:58.146496 281473017981624        Options.verify_sst_unique_id_in_manifest: 1
2025/06/30-09:49:58.146496 281473017981624                                     Options.env: 0xffff7887b000
2025/06/30-09:49:58.146497 281473017981624                                      Options.fs: PosixFileSystem
2025/06/30-09:49:58.146498 281473017981624                                Options.info_log: 0xffff78404de0
2025/06/30-09:49:58.146499 281473017981624                Options.max_file_opening_threads: 16
2025/06/30-09:49:58.146499 281473017981624                              Options.statistics: 0xffff76a04560
2025/06/30-09:49:58.146500 281473017981624                              Options.statistics stats level: 3
2025/06/30-09:49:58.146500 281473017981624                               Options.use_fsync: 0
2025/06/30-09:49:58.146501 281473017981624                       Options.max_log_file_size: 0
2025/06/30-09:49:58.146501 281473017981624                  Options.max_manifest_file_size: 1073741824
2025/06/30-09:49:58.146502 281473017981624                   Options.log_file_time_to_roll: 0
2025/06/30-09:49:58.146502 281473017981624                       Options.keep_log_file_num: 100
2025/06/30-09:49:58.146503 281473017981624                    Options.recycle_log_file_num: 0
2025/06/30-09:49:58.146503 281473017981624                         Options.allow_fallocate: 1
2025/06/30-09:49:58.146504 281473017981624                        Options.allow_mmap_reads: 0
2025/06/30-09:49:58.146505 281473017981624                       Options.allow_mmap_writes: 0
2025/06/30-09:49:58.146506 281473017981624                        Options.use_direct_reads: 0
2025/06/30-09:49:58.146506 281473017981624                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/30-09:49:58.146507 281473017981624          Options.create_missing_column_families: 1
2025/06/30-09:49:58.146507 281473017981624                              Options.db_log_dir: 
2025/06/30-09:49:58.146508 281473017981624                                 Options.wal_dir: 
2025/06/30-09:49:58.146508 281473017981624                Options.table_cache_numshardbits: 6
2025/06/30-09:49:58.146509 281473017981624                         Options.WAL_ttl_seconds: 0
2025/06/30-09:49:58.146509 281473017981624                       Options.WAL_size_limit_MB: 0
2025/06/30-09:49:58.146510 281473017981624                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/30-09:49:58.146510 281473017981624             Options.manifest_preallocation_size: 4194304
2025/06/30-09:49:58.146511 281473017981624                     Options.is_fd_close_on_exec: 1
2025/06/30-09:49:58.146512 281473017981624                   Options.advise_random_on_open: 1
2025/06/30-09:49:58.146517 281473017981624                    Options.db_write_buffer_size: 0
2025/06/30-09:49:58.146517 281473017981624                    Options.write_buffer_manager: 0xffff788bf010
2025/06/30-09:49:58.146518 281473017981624         Options.access_hint_on_compaction_start: 1
2025/06/30-09:49:58.146518 281473017981624           Options.random_access_max_buffer_size: 1048576
2025/06/30-09:49:58.146519 281473017981624                      Options.use_adaptive_mutex: 0
2025/06/30-09:49:58.146520 281473017981624                            Options.rate_limiter: 0
2025/06/30-09:49:58.146521 281473017981624     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/30-09:49:58.146521 281473017981624                       Options.wal_recovery_mode: 2
2025/06/30-09:49:58.146522 281473017981624                  Options.enable_thread_tracking: 0
2025/06/30-09:49:58.146522 281473017981624                  Options.enable_pipelined_write: 0
2025/06/30-09:49:58.146523 281473017981624                  Options.unordered_write: 0
2025/06/30-09:49:58.146523 281473017981624         Options.allow_concurrent_memtable_write: 1
2025/06/30-09:49:58.146524 281473017981624      Options.enable_write_thread_adaptive_yield: 1
2025/06/30-09:49:58.146524 281473017981624             Options.write_thread_max_yield_usec: 100
2025/06/30-09:49:58.146525 281473017981624            Options.write_thread_slow_yield_usec: 3
2025/06/30-09:49:58.146525 281473017981624                               Options.row_cache: None
2025/06/30-09:49:58.146526 281473017981624                              Options.wal_filter: None
2025/06/30-09:49:58.146527 281473017981624             Options.avoid_flush_during_recovery: 0
2025/06/30-09:49:58.146528 281473017981624             Options.allow_ingest_behind: 0
2025/06/30-09:49:58.146528 281473017981624             Options.two_write_queues: 0
2025/06/30-09:49:58.146529 281473017981624             Options.manual_wal_flush: 0
2025/06/30-09:49:58.146529 281473017981624             Options.wal_compression: 0
2025/06/30-09:49:58.146530 281473017981624             Options.atomic_flush: 0
2025/06/30-09:49:58.146530 281473017981624             Options.avoid_unnecessary_blocking_io: 0
2025/06/30-09:49:58.146531 281473017981624                 Options.persist_stats_to_disk: 0
2025/06/30-09:49:58.146531 281473017981624                 Options.write_dbid_to_manifest: 0
2025/06/30-09:49:58.146532 281473017981624                 Options.log_readahead_size: 0
2025/06/30-09:49:58.146533 281473017981624                 Options.file_checksum_gen_factory: Unknown
2025/06/30-09:49:58.146533 281473017981624                 Options.best_efforts_recovery: 0
2025/06/30-09:49:58.146534 281473017981624                Options.max_bgerror_resume_count: 2147483647
2025/06/30-09:49:58.146534 281473017981624            Options.bgerror_resume_retry_interval: 1000000
2025/06/30-09:49:58.146535 281473017981624             Options.allow_data_in_errors: 0
2025/06/30-09:49:58.146535 281473017981624             Options.db_host_id: __hostname__
2025/06/30-09:49:58.146536 281473017981624             Options.enforce_single_del_contracts: true
2025/06/30-09:49:58.146536 281473017981624             Options.max_background_jobs: 2
2025/06/30-09:49:58.146537 281473017981624             Options.max_background_compactions: -1
2025/06/30-09:49:58.146538 281473017981624             Options.max_subcompactions: 1
2025/06/30-09:49:58.146538 281473017981624             Options.avoid_flush_during_shutdown: 0
2025/06/30-09:49:58.146539 281473017981624           Options.writable_file_max_buffer_size: 1048576
2025/06/30-09:49:58.146539 281473017981624             Options.delayed_write_rate : 16777216
2025/06/30-09:49:58.146540 281473017981624             Options.max_total_wal_size: 1073741824
2025/06/30-09:49:58.146540 281473017981624             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/30-09:49:58.146541 281473017981624                   Options.stats_dump_period_sec: 600
2025/06/30-09:49:58.146541 281473017981624                 Options.stats_persist_period_sec: 600
2025/06/30-09:49:58.146542 281473017981624                 Options.stats_history_buffer_size: 1048576
2025/06/30-09:49:58.146543 281473017981624                          Options.max_open_files: -1
2025/06/30-09:49:58.146543 281473017981624                          Options.bytes_per_sync: 0
2025/06/30-09:49:58.146544 281473017981624                      Options.wal_bytes_per_sync: 0
2025/06/30-09:49:58.146544 281473017981624                   Options.strict_bytes_per_sync: 0
2025/06/30-09:49:58.146545 281473017981624       Options.compaction_readahead_size: 2097152
2025/06/30-09:49:58.146545 281473017981624                  Options.max_background_flushes: -1
2025/06/30-09:49:58.146546 281473017981624 Options.daily_offpeak_time_utc: 
2025/06/30-09:49:58.146546 281473017981624 Compression algorithms supported:
2025/06/30-09:49:58.146547 281473017981624 	kZSTDNotFinalCompression supported: 1
2025/06/30-09:49:58.146548 281473017981624 	kZSTD supported: 1
2025/06/30-09:49:58.146549 281473017981624 	kXpressCompression supported: 0
2025/06/30-09:49:58.146550 281473017981624 	kLZ4HCCompression supported: 1
2025/06/30-09:49:58.146550 281473017981624 	kLZ4Compression supported: 1
2025/06/30-09:49:58.146551 281473017981624 	kBZip2Compression supported: 1
2025/06/30-09:49:58.146551 281473017981624 	kZlibCompression supported: 1
2025/06/30-09:49:58.146552 281473017981624 	kSnappyCompression supported: 1
2025/06/30-09:49:58.146553 281473017981624 Fast CRC32 supported: Supported on Arm64
2025/06/30-09:49:58.146554 281473017981624 DMutex implementation: pthread_mutex_t
2025/06/30-09:49:58.147048 281473017981624 [/version_set.cc:5906] Recovering from manifest file: /home/<USER>/data/protocol/raft/naming_service_metadata/log/MANIFEST-000005
2025/06/30-09:49:58.147222 281473017981624 [/column_family.cc:618] --------------- Options for column family [default]:
2025/06/30-09:49:58.147224 281473017981624               Options.comparator: leveldb.BytewiseComparator
2025/06/30-09:49:58.147225 281473017981624           Options.merge_operator: StringAppendOperator
2025/06/30-09:49:58.147226 281473017981624        Options.compaction_filter: None
2025/06/30-09:49:58.147226 281473017981624        Options.compaction_filter_factory: None
2025/06/30-09:49:58.147227 281473017981624  Options.sst_partitioner_factory: None
2025/06/30-09:49:58.147228 281473017981624         Options.memtable_factory: SkipListFactory
2025/06/30-09:49:58.147228 281473017981624            Options.table_factory: BlockBasedTable
2025/06/30-09:49:58.147257 281473017981624            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff7623c4a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff76a190f0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-09:49:58.147262 281473017981624        Options.write_buffer_size: 134217728
2025/06/30-09:49:58.147262 281473017981624  Options.max_write_buffer_number: 6
2025/06/30-09:49:58.147263 281473017981624        Options.compression[0]: NoCompression
2025/06/30-09:49:58.147264 281473017981624        Options.compression[1]: NoCompression
2025/06/30-09:49:58.147265 281473017981624        Options.compression[2]: LZ4
2025/06/30-09:49:58.147266 281473017981624        Options.compression[3]: LZ4
2025/06/30-09:49:58.147266 281473017981624        Options.compression[4]: LZ4
2025/06/30-09:49:58.147267 281473017981624        Options.compression[5]: LZ4
2025/06/30-09:49:58.147267 281473017981624        Options.compression[6]: LZ4
2025/06/30-09:49:58.147268 281473017981624                  Options.bottommost_compression: Disabled
2025/06/30-09:49:58.147269 281473017981624       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/30-09:49:58.147269 281473017981624   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-09:49:58.147270 281473017981624             Options.num_levels: 7
2025/06/30-09:49:58.147270 281473017981624        Options.min_write_buffer_number_to_merge: 2
2025/06/30-09:49:58.147271 281473017981624     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-09:49:58.147272 281473017981624     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-09:49:58.147272 281473017981624            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-09:49:58.147273 281473017981624                  Options.bottommost_compression_opts.level: 32767
2025/06/30-09:49:58.147273 281473017981624               Options.bottommost_compression_opts.strategy: 0
2025/06/30-09:49:58.147274 281473017981624         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-09:49:58.147274 281473017981624         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-09:49:58.147275 281473017981624         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-09:49:58.147275 281473017981624                  Options.bottommost_compression_opts.enabled: false
2025/06/30-09:49:58.147276 281473017981624         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-09:49:58.147277 281473017981624         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-09:49:58.147277 281473017981624            Options.compression_opts.window_bits: -14
2025/06/30-09:49:58.147278 281473017981624                  Options.compression_opts.level: 32767
2025/06/30-09:49:58.147278 281473017981624               Options.compression_opts.strategy: 0
2025/06/30-09:49:58.147279 281473017981624         Options.compression_opts.max_dict_bytes: 0
2025/06/30-09:49:58.147280 281473017981624         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-09:49:58.147280 281473017981624         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-09:49:58.147281 281473017981624         Options.compression_opts.parallel_threads: 1
2025/06/30-09:49:58.147281 281473017981624                  Options.compression_opts.enabled: false
2025/06/30-09:49:58.147282 281473017981624         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-09:49:58.147282 281473017981624      Options.level0_file_num_compaction_trigger: 2
2025/06/30-09:49:58.147283 281473017981624          Options.level0_slowdown_writes_trigger: 20
2025/06/30-09:49:58.147283 281473017981624              Options.level0_stop_writes_trigger: 40
2025/06/30-09:49:58.147284 281473017981624                   Options.target_file_size_base: 67108864
2025/06/30-09:49:58.147284 281473017981624             Options.target_file_size_multiplier: 1
2025/06/30-09:49:58.147285 281473017981624                Options.max_bytes_for_level_base: 536870912
2025/06/30-09:49:58.147285 281473017981624 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-09:49:58.147286 281473017981624          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-09:49:58.147287 281473017981624 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-09:49:58.147287 281473017981624 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-09:49:58.147288 281473017981624 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-09:49:58.147289 281473017981624 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-09:49:58.147289 281473017981624 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-09:49:58.147290 281473017981624 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-09:49:58.147291 281473017981624 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-09:49:58.147291 281473017981624       Options.max_sequential_skip_in_iterations: 8
2025/06/30-09:49:58.147292 281473017981624                    Options.max_compaction_bytes: 1677721600
2025/06/30-09:49:58.147292 281473017981624   Options.ignore_max_compaction_bytes_for_input: true
2025/06/30-09:49:58.147293 281473017981624                        Options.arena_block_size: 1048576
2025/06/30-09:49:58.147293 281473017981624   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-09:49:58.147294 281473017981624   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-09:49:58.147294 281473017981624                Options.disable_auto_compactions: 0
2025/06/30-09:49:58.147295 281473017981624                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-09:49:58.147296 281473017981624                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-09:49:58.147297 281473017981624 Options.compaction_options_universal.size_ratio: 1
2025/06/30-09:49:58.147297 281473017981624 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-09:49:58.147298 281473017981624 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-09:49:58.147298 281473017981624 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-09:49:58.147299 281473017981624 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-09:49:58.147300 281473017981624 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-09:49:58.147300 281473017981624 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-09:49:58.147301 281473017981624 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-09:49:58.147303 281473017981624                   Options.table_properties_collectors: 
2025/06/30-09:49:58.147304 281473017981624                   Options.inplace_update_support: 0
2025/06/30-09:49:58.147304 281473017981624                 Options.inplace_update_num_locks: 10000
2025/06/30-09:49:58.147305 281473017981624               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/30-09:49:58.147306 281473017981624               Options.memtable_whole_key_filtering: 0
2025/06/30-09:49:58.147306 281473017981624   Options.memtable_huge_page_size: 0
2025/06/30-09:49:58.147307 281473017981624                           Options.bloom_locality: 0
2025/06/30-09:49:58.147307 281473017981624                    Options.max_successive_merges: 0
2025/06/30-09:49:58.147308 281473017981624                Options.optimize_filters_for_hits: 0
2025/06/30-09:49:58.147308 281473017981624                Options.paranoid_file_checks: 0
2025/06/30-09:49:58.147309 281473017981624                Options.force_consistency_checks: 1
2025/06/30-09:49:58.147309 281473017981624                Options.report_bg_io_stats: 0
2025/06/30-09:49:58.147310 281473017981624                               Options.ttl: 2592000
2025/06/30-09:49:58.147311 281473017981624          Options.periodic_compaction_seconds: 0
2025/06/30-09:49:58.147311 281473017981624                        Options.default_temperature: kUnknown
2025/06/30-09:49:58.147312 281473017981624  Options.preclude_last_level_data_seconds: 0
2025/06/30-09:49:58.147312 281473017981624    Options.preserve_internal_time_seconds: 0
2025/06/30-09:49:58.147313 281473017981624                       Options.enable_blob_files: false
2025/06/30-09:49:58.147313 281473017981624                           Options.min_blob_size: 0
2025/06/30-09:49:58.147314 281473017981624                          Options.blob_file_size: 268435456
2025/06/30-09:49:58.147314 281473017981624                   Options.blob_compression_type: NoCompression
2025/06/30-09:49:58.147315 281473017981624          Options.enable_blob_garbage_collection: false
2025/06/30-09:49:58.147316 281473017981624      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-09:49:58.147317 281473017981624 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-09:49:58.147317 281473017981624          Options.blob_compaction_readahead_size: 0
2025/06/30-09:49:58.147318 281473017981624                Options.blob_file_starting_level: 0
2025/06/30-09:49:58.147318 281473017981624         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-09:49:58.147319 281473017981624            Options.memtable_max_range_deletions: 0
2025/06/30-09:49:58.160674 281473017981624 [/column_family.cc:618] --------------- Options for column family [Configuration]:
2025/06/30-09:49:58.160679 281473017981624               Options.comparator: leveldb.BytewiseComparator
2025/06/30-09:49:58.160680 281473017981624           Options.merge_operator: StringAppendOperator
2025/06/30-09:49:58.160681 281473017981624        Options.compaction_filter: None
2025/06/30-09:49:58.160682 281473017981624        Options.compaction_filter_factory: None
2025/06/30-09:49:58.160683 281473017981624  Options.sst_partitioner_factory: None
2025/06/30-09:49:58.160683 281473017981624         Options.memtable_factory: SkipListFactory
2025/06/30-09:49:58.160684 281473017981624            Options.table_factory: BlockBasedTable
2025/06/30-09:49:58.160741 281473017981624            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff7623c4a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff76a190f0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-09:49:58.160747 281473017981624        Options.write_buffer_size: 134217728
2025/06/30-09:49:58.160747 281473017981624  Options.max_write_buffer_number: 6
2025/06/30-09:49:58.160748 281473017981624        Options.compression[0]: NoCompression
2025/06/30-09:49:58.160749 281473017981624        Options.compression[1]: NoCompression
2025/06/30-09:49:58.160749 281473017981624        Options.compression[2]: LZ4
2025/06/30-09:49:58.160750 281473017981624        Options.compression[3]: LZ4
2025/06/30-09:49:58.160750 281473017981624        Options.compression[4]: LZ4
2025/06/30-09:49:58.160751 281473017981624        Options.compression[5]: LZ4
2025/06/30-09:49:58.160751 281473017981624        Options.compression[6]: LZ4
2025/06/30-09:49:58.160752 281473017981624                  Options.bottommost_compression: Disabled
2025/06/30-09:49:58.160753 281473017981624       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/30-09:49:58.160754 281473017981624   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-09:49:58.160754 281473017981624             Options.num_levels: 7
2025/06/30-09:49:58.160755 281473017981624        Options.min_write_buffer_number_to_merge: 2
2025/06/30-09:49:58.160755 281473017981624     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-09:49:58.160756 281473017981624     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-09:49:58.160757 281473017981624            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-09:49:58.160758 281473017981624                  Options.bottommost_compression_opts.level: 32767
2025/06/30-09:49:58.160758 281473017981624               Options.bottommost_compression_opts.strategy: 0
2025/06/30-09:49:58.160759 281473017981624         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-09:49:58.160759 281473017981624         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-09:49:58.160760 281473017981624         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-09:49:58.160760 281473017981624                  Options.bottommost_compression_opts.enabled: false
2025/06/30-09:49:58.160761 281473017981624         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-09:49:58.160762 281473017981624         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-09:49:58.160762 281473017981624            Options.compression_opts.window_bits: -14
2025/06/30-09:49:58.160763 281473017981624                  Options.compression_opts.level: 32767
2025/06/30-09:49:58.160764 281473017981624               Options.compression_opts.strategy: 0
2025/06/30-09:49:58.160764 281473017981624         Options.compression_opts.max_dict_bytes: 0
2025/06/30-09:49:58.160765 281473017981624         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-09:49:58.160765 281473017981624         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-09:49:58.160766 281473017981624         Options.compression_opts.parallel_threads: 1
2025/06/30-09:49:58.160766 281473017981624                  Options.compression_opts.enabled: false
2025/06/30-09:49:58.160767 281473017981624         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-09:49:58.160767 281473017981624      Options.level0_file_num_compaction_trigger: 2
2025/06/30-09:49:58.160768 281473017981624          Options.level0_slowdown_writes_trigger: 20
2025/06/30-09:49:58.160768 281473017981624              Options.level0_stop_writes_trigger: 40
2025/06/30-09:49:58.160769 281473017981624                   Options.target_file_size_base: 67108864
2025/06/30-09:49:58.160769 281473017981624             Options.target_file_size_multiplier: 1
2025/06/30-09:49:58.160770 281473017981624                Options.max_bytes_for_level_base: 536870912
2025/06/30-09:49:58.160771 281473017981624 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-09:49:58.160771 281473017981624          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-09:49:58.160772 281473017981624 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-09:49:58.160772 281473017981624 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-09:49:58.160773 281473017981624 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-09:49:58.160774 281473017981624 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-09:49:58.160774 281473017981624 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-09:49:58.160775 281473017981624 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-09:49:58.160775 281473017981624 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-09:49:58.160776 281473017981624       Options.max_sequential_skip_in_iterations: 8
2025/06/30-09:49:58.160776 281473017981624                    Options.max_compaction_bytes: 1677721600
2025/06/30-09:49:58.160777 281473017981624   Options.ignore_max_compaction_bytes_for_input: true
2025/06/30-09:49:58.160777 281473017981624                        Options.arena_block_size: 1048576
2025/06/30-09:49:58.160778 281473017981624   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-09:49:58.160778 281473017981624   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-09:49:58.160779 281473017981624                Options.disable_auto_compactions: 0
2025/06/30-09:49:58.160780 281473017981624                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-09:49:58.160781 281473017981624                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-09:49:58.160782 281473017981624 Options.compaction_options_universal.size_ratio: 1
2025/06/30-09:49:58.160783 281473017981624 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-09:49:58.160783 281473017981624 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-09:49:58.160784 281473017981624 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-09:49:58.160784 281473017981624 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-09:49:58.160785 281473017981624 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-09:49:58.160786 281473017981624 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-09:49:58.160786 281473017981624 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-09:49:58.160790 281473017981624                   Options.table_properties_collectors: 
2025/06/30-09:49:58.160790 281473017981624                   Options.inplace_update_support: 0
2025/06/30-09:49:58.160791 281473017981624                 Options.inplace_update_num_locks: 10000
2025/06/30-09:49:58.160791 281473017981624               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/30-09:49:58.160792 281473017981624               Options.memtable_whole_key_filtering: 0
2025/06/30-09:49:58.160805 281473017981624   Options.memtable_huge_page_size: 0
2025/06/30-09:49:58.160806 281473017981624                           Options.bloom_locality: 0
2025/06/30-09:49:58.160806 281473017981624                    Options.max_successive_merges: 0
2025/06/30-09:49:58.160807 281473017981624                Options.optimize_filters_for_hits: 0
2025/06/30-09:49:58.160807 281473017981624                Options.paranoid_file_checks: 0
2025/06/30-09:49:58.160808 281473017981624                Options.force_consistency_checks: 1
2025/06/30-09:49:58.160808 281473017981624                Options.report_bg_io_stats: 0
2025/06/30-09:49:58.160809 281473017981624                               Options.ttl: 2592000
2025/06/30-09:49:58.160809 281473017981624          Options.periodic_compaction_seconds: 0
2025/06/30-09:49:58.160811 281473017981624                        Options.default_temperature: kUnknown
2025/06/30-09:49:58.160811 281473017981624  Options.preclude_last_level_data_seconds: 0
2025/06/30-09:49:58.160812 281473017981624    Options.preserve_internal_time_seconds: 0
2025/06/30-09:49:58.160812 281473017981624                       Options.enable_blob_files: false
2025/06/30-09:49:58.160813 281473017981624                           Options.min_blob_size: 0
2025/06/30-09:49:58.160813 281473017981624                          Options.blob_file_size: 268435456
2025/06/30-09:49:58.160814 281473017981624                   Options.blob_compression_type: NoCompression
2025/06/30-09:49:58.160814 281473017981624          Options.enable_blob_garbage_collection: false
2025/06/30-09:49:58.160815 281473017981624      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-09:49:58.160816 281473017981624 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-09:49:58.160816 281473017981624          Options.blob_compaction_readahead_size: 0
2025/06/30-09:49:58.160817 281473017981624                Options.blob_file_starting_level: 0
2025/06/30-09:49:58.160817 281473017981624         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-09:49:58.160818 281473017981624            Options.memtable_max_range_deletions: 0
2025/06/30-09:49:58.229384 281473017981624 [/version_set.cc:5957] Recovered from manifest file:/home/<USER>/data/protocol/raft/naming_service_metadata/log/MANIFEST-000005 succeeded,manifest_file_number is 5, next_file_number is 7, last_sequence is 0, log_number is 4,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 0
2025/06/30-09:49:58.229391 281473017981624 [/version_set.cc:5966] Column family [default] (ID 0), log number is 0
2025/06/30-09:49:58.229392 281473017981624 [/version_set.cc:5966] Column family [Configuration] (ID 1), log number is 4
2025/06/30-09:49:58.230095 281473017981624 [/db_impl/db_impl_open.cc:646] DB ID: e9336bcf-bc16-41a2-95a2-12811a2eff46
2025/06/30-09:49:58.230645 281473017981624 EVENT_LOG_v1 {"time_micros": 1751248198230638, "job": 1, "event": "recovery_started", "wal_files": [4]}
2025/06/30-09:49:58.230654 281473017981624 [/db_impl/db_impl_open.cc:1145] Recovering log #4 mode 2
2025/06/30-09:49:58.232644 281473017981624 EVENT_LOG_v1 {"time_micros": 1751248198232616, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 8, "file_size": 1067, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 5, "largest_seqno": 5, "table_properties": {"data_size": 0, "index_size": 13, "index_partitions": 0, "top_level_index_size": 8, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 0, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751248198, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "e9336bcf-bc16-41a2-95a2-12811a2eff46", "db_session_id": "U9M4M1L8JZU0AV343NRV", "orig_file_number": 8, "seqno_to_time_mapping": "N/A"}}
2025/06/30-09:49:58.233878 281473017981624 EVENT_LOG_v1 {"time_micros": 1751248198233868, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 9, "file_size": 1218, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 6, "largest_seqno": 34, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 42, "raw_average_key_size": 21, "raw_value_size": 16, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751248198, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "e9336bcf-bc16-41a2-95a2-12811a2eff46", "db_session_id": "U9M4M1L8JZU0AV343NRV", "orig_file_number": 9, "seqno_to_time_mapping": "N/A"}}
2025/06/30-09:49:58.234399 281473017981624 EVENT_LOG_v1 {"time_micros": 1751248198234399, "job": 1, "event": "recovery_finished"}
2025/06/30-09:49:58.234567 281473017981624 [/version_set.cc:5417] Creating manifest 11
2025/06/30-09:49:58.238194 281473017981624 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_service_metadata/log/000004.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/30-09:49:58.238290 281473017981624 [/db_impl/db_impl_open.cc:2150] SstFileManager instance 0xffff78484060
2025/06/30-09:49:58.238474 281473017981624 DB pointer 0xffff7ccb9f00
2025/06/30-09:50:07.239510 281472673428968 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/06/30-09:50:07.239634 281472673428968 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 9.1 total, 9.1 interval
Cumulative writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.04 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    1.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 9.1 total, 9.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff76a190f0#1 capacity: 8.00 MB seed: 1165982427 usage: 0.93 KB table_size: 256 occupancy: 5 collections: 1 last_copies: 1 last_secs: 3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(3,0.38 KB,0.00469685%) IndexBlock(1,0.12 KB,0.00146627%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 5 Average: 9.0000  StdDev: 15.50
Min: 1  Median: 1.0000  Max: 40
Percentiles: P50: 1.00 P75: 1.75 P99: 40.00 P99.9: 40.00 P99.99: 40.00
------------------------------------------------------
[       0,       1 ]        3  60.000%  60.000% ############
(       1,       2 ]        1  20.000%  80.000% ####
(      34,      51 ]        1  20.000% 100.000% ####


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.19 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      1/0    1.19 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.7      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 9.1 total, 9.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff76a190f0#1 capacity: 8.00 MB seed: 1165982427 usage: 0.93 KB table_size: 256 occupancy: 5 collections: 1 last_copies: 1 last_secs: 3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(3,0.38 KB,0.00469685%) IndexBlock(1,0.12 KB,0.00146627%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 7 Average: 7.1429  StdDev: 10.30
Min: 0  Median: 0.8750  Max: 27
Percentiles: P50: 0.88 P75: 16.75 P99: 27.00 P99.9: 27.00 P99.99: 27.00
------------------------------------------------------
[       0,       1 ]        4  57.143%  57.143% ###########
(       2,       3 ]        1  14.286%  71.429% ###
(      15,      22 ]        1  14.286%  85.714% ###
(      22,      34 ]        1  14.286% 100.000% ###

2025/06/30-09:50:07.240261 281472673428968 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 4
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 4
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 1
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 1
rocksdb.block.cache.index.bytes.insert COUNT : 123
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 3
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 3
rocksdb.block.cache.data.bytes.insert COUNT : 394
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 517
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 5
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 168
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 2
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 168
rocksdb.write.self COUNT : 5
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 5
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2285
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 3468
rocksdb.non.last.level.read.count COUNT : 12
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 0
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 1
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 8
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 113
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 4
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 35
rocksdb.prefetch.hits COUNT : 1
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 140.000000 P95 : 474.000000 P99 : 474.000000 P100 : 474.000000 COUNT : 5 SUM : 929
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 170.000000 P95 : 517.000000 P99 : 517.000000 P100 : 517.000000 COUNT : 2 SUM : 631
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 135.000000 P95 : 135.000000 P99 : 135.000000 P100 : 135.000000 COUNT : 1 SUM : 135
rocksdb.manifest.file.sync.micros P50 : 48.000000 P95 : 48.000000 P99 : 48.000000 P100 : 48.000000 COUNT : 1 SUM : 48
rocksdb.table.open.io.micros P50 : 110.000000 P95 : 197.000000 P99 : 197.000000 P100 : 197.000000 COUNT : 2 SUM : 282
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 2.000000 P95 : 8.000000 P99 : 8.000000 P100 : 8.000000 COUNT : 6 SUM : 26
rocksdb.write.raw.block.micros P50 : 0.555556 P95 : 1.500000 P99 : 1.900000 P100 : 2.000000 COUNT : 10 SUM : 4
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 0.857143 P95 : 40.000000 P99 : 40.000000 P100 : 40.000000 COUNT : 12 SUM : 95
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.open.micros P50 : 0.785714 P95 : 40.000000 P99 : 40.000000 P100 : 40.000000 COUNT : 11 SUM : 92
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 5 SUM : 168
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1133.000000 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 1 SUM : 1133
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/30-09:50:08.239224 281472673428968 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/06/30-09:50:08.239232 281472673428968 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/06/30-09:50:08.239233 281472673428968 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/06/30-10:00:07.241647 281472673428968 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/06/30-10:00:07.242389 281472673428968 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 609.1 total, 600.0 interval
Cumulative writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.04 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    1.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 609.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff76a190f0#1 capacity: 8.00 MB seed: 1165982427 usage: 0.93 KB table_size: 256 occupancy: 5 collections: 2 last_copies: 1 last_secs: 5.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(3,0.38 KB,0.00469685%) IndexBlock(1,0.12 KB,0.00146627%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 5 Average: 9.0000  StdDev: 15.50
Min: 1  Median: 1.0000  Max: 40
Percentiles: P50: 1.00 P75: 1.75 P99: 40.00 P99.9: 40.00 P99.99: 40.00
------------------------------------------------------
[       0,       1 ]        3  60.000%  60.000% ############
(       1,       2 ]        1  20.000%  80.000% ####
(      34,      51 ]        1  20.000% 100.000% ####


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.19 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      1/0    1.19 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.7      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.7      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 609.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff76a190f0#1 capacity: 8.00 MB seed: 1165982427 usage: 0.93 KB table_size: 256 occupancy: 5 collections: 2 last_copies: 1 last_secs: 5.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(3,0.38 KB,0.00469685%) IndexBlock(1,0.12 KB,0.00146627%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 7 Average: 7.1429  StdDev: 10.30
Min: 0  Median: 0.8750  Max: 27
Percentiles: P50: 0.88 P75: 16.75 P99: 27.00 P99.9: 27.00 P99.99: 27.00
------------------------------------------------------
[       0,       1 ]        4  57.143%  57.143% ###########
(       2,       3 ]        1  14.286%  71.429% ###
(      15,      22 ]        1  14.286%  85.714% ###
(      22,      34 ]        1  14.286% 100.000% ###

2025/06/30-10:00:07.245226 281472673428968 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 4
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 4
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 1
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 1
rocksdb.block.cache.index.bytes.insert COUNT : 123
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 3
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 3
rocksdb.block.cache.data.bytes.insert COUNT : 394
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 517
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 5
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 168
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 2
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 168
rocksdb.write.self COUNT : 5
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 5
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2285
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 3468
rocksdb.non.last.level.read.count COUNT : 12
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 0
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 1
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 8
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 113
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 4
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 35
rocksdb.prefetch.hits COUNT : 1
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 140.000000 P95 : 474.000000 P99 : 474.000000 P100 : 474.000000 COUNT : 5 SUM : 929
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 170.000000 P95 : 517.000000 P99 : 517.000000 P100 : 517.000000 COUNT : 2 SUM : 631
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 135.000000 P95 : 135.000000 P99 : 135.000000 P100 : 135.000000 COUNT : 1 SUM : 135
rocksdb.manifest.file.sync.micros P50 : 48.000000 P95 : 48.000000 P99 : 48.000000 P100 : 48.000000 COUNT : 1 SUM : 48
rocksdb.table.open.io.micros P50 : 110.000000 P95 : 197.000000 P99 : 197.000000 P100 : 197.000000 COUNT : 2 SUM : 282
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 2.000000 P95 : 8.000000 P99 : 8.000000 P100 : 8.000000 COUNT : 6 SUM : 26
rocksdb.write.raw.block.micros P50 : 0.555556 P95 : 1.500000 P99 : 1.900000 P100 : 2.000000 COUNT : 10 SUM : 4
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 0.857143 P95 : 40.000000 P99 : 40.000000 P100 : 40.000000 COUNT : 12 SUM : 95
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.open.micros P50 : 0.785714 P95 : 40.000000 P99 : 40.000000 P100 : 40.000000 COUNT : 11 SUM : 92
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 5 SUM : 168
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1133.000000 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 1 SUM : 1133
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/30-10:00:08.240255 281472673428968 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/06/30-10:00:08.240452 281472673428968 [/db_impl/db_impl.cc:1022] Storing 211 stats with timestamp 1751248808 to in-memory stats history
2025/06/30-10:00:08.240458 281472673428968 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 15127 bytes, slice count: 1
2025/06/30-10:00:08.240460 281472673428968 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 15127 bytes, slice count: 1
2025/06/30-10:04:30.966358 281472426575336 [/db_impl/db_impl.cc:487] Shutdown: canceling all background work
2025/06/30-10:04:31.042615 281472426575336 [/db_impl/db_impl.cc:668] Shutdown complete
