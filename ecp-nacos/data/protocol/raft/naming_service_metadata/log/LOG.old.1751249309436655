2025/06/30-10:05:55.062466 281473575824056 RocksDB version: 8.8.1
2025/06/30-10:05:55.062622 281473575824056 Compile date 2023-11-23 11:07:28
2025/06/30-10:05:55.062625 281473575824056 DB SUMMARY
2025/06/30-10:05:55.062629 281473575824056 Host name (Env):  ab4311f86f33
2025/06/30-10:05:55.062630 281473575824056 DB Session ID:  DJ073KN838NTE1LSV2OK
2025/06/30-10:05:55.062883 281473575824056 CURRENT file:  CURRENT
2025/06/30-10:05:55.062888 281473575824056 IDENTITY file:  IDENTITY
2025/06/30-10:05:55.062932 281473575824056 MANIFEST file:  MANIFEST-000017 size: 738 Bytes
2025/06/30-10:05:55.062934 281473575824056 SST files in /home/<USER>/data/protocol/raft/naming_service_metadata/log dir, Total Num: 1, files: 000021.sst 
2025/06/30-10:05:55.062935 281473575824056 Write Ahead Log file in /home/<USER>/data/protocol/raft/naming_service_metadata/log: 000016.log size: 203 ; 
2025/06/30-10:05:55.062936 281473575824056                         Options.error_if_exists: 0
2025/06/30-10:05:55.062937 281473575824056                       Options.create_if_missing: 1
2025/06/30-10:05:55.062938 281473575824056                         Options.paranoid_checks: 1
2025/06/30-10:05:55.062938 281473575824056             Options.flush_verify_memtable_count: 1
2025/06/30-10:05:55.062939 281473575824056          Options.compaction_verify_record_count: 1
2025/06/30-10:05:55.062939 281473575824056                               Options.track_and_verify_wals_in_manifest: 0
2025/06/30-10:05:55.062940 281473575824056        Options.verify_sst_unique_id_in_manifest: 1
2025/06/30-10:05:55.062940 281473575824056                                     Options.env: 0xffff97a1f980
2025/06/30-10:05:55.062941 281473575824056                                      Options.fs: PosixFileSystem
2025/06/30-10:05:55.062942 281473575824056                                Options.info_log: 0xffff9d429990
2025/06/30-10:05:55.062943 281473575824056                Options.max_file_opening_threads: 16
2025/06/30-10:05:55.062943 281473575824056                              Options.statistics: 0xffff99d2d1a0
2025/06/30-10:05:55.062944 281473575824056                              Options.statistics stats level: 3
2025/06/30-10:05:55.062944 281473575824056                               Options.use_fsync: 0
2025/06/30-10:05:55.062945 281473575824056                       Options.max_log_file_size: 0
2025/06/30-10:05:55.062946 281473575824056                  Options.max_manifest_file_size: 1073741824
2025/06/30-10:05:55.062946 281473575824056                   Options.log_file_time_to_roll: 0
2025/06/30-10:05:55.062947 281473575824056                       Options.keep_log_file_num: 100
2025/06/30-10:05:55.062947 281473575824056                    Options.recycle_log_file_num: 0
2025/06/30-10:05:55.062948 281473575824056                         Options.allow_fallocate: 1
2025/06/30-10:05:55.062948 281473575824056                        Options.allow_mmap_reads: 0
2025/06/30-10:05:55.062949 281473575824056                       Options.allow_mmap_writes: 0
2025/06/30-10:05:55.062949 281473575824056                        Options.use_direct_reads: 0
2025/06/30-10:05:55.062950 281473575824056                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/30-10:05:55.062951 281473575824056          Options.create_missing_column_families: 1
2025/06/30-10:05:55.062951 281473575824056                              Options.db_log_dir: 
2025/06/30-10:05:55.062952 281473575824056                                 Options.wal_dir: 
2025/06/30-10:05:55.062952 281473575824056                Options.table_cache_numshardbits: 6
2025/06/30-10:05:55.062953 281473575824056                         Options.WAL_ttl_seconds: 0
2025/06/30-10:05:55.062953 281473575824056                       Options.WAL_size_limit_MB: 0
2025/06/30-10:05:55.062954 281473575824056                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/30-10:05:55.062954 281473575824056             Options.manifest_preallocation_size: 4194304
2025/06/30-10:05:55.062955 281473575824056                     Options.is_fd_close_on_exec: 1
2025/06/30-10:05:55.062956 281473575824056                   Options.advise_random_on_open: 1
2025/06/30-10:05:55.062958 281473575824056                    Options.db_write_buffer_size: 0
2025/06/30-10:05:55.062959 281473575824056                    Options.write_buffer_manager: 0xffff99d2d9d0
2025/06/30-10:05:55.062959 281473575824056         Options.access_hint_on_compaction_start: 1
2025/06/30-10:05:55.062960 281473575824056           Options.random_access_max_buffer_size: 1048576
2025/06/30-10:05:55.062960 281473575824056                      Options.use_adaptive_mutex: 0
2025/06/30-10:05:55.062961 281473575824056                            Options.rate_limiter: 0
2025/06/30-10:05:55.062962 281473575824056     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/30-10:05:55.062962 281473575824056                       Options.wal_recovery_mode: 2
2025/06/30-10:05:55.062963 281473575824056                  Options.enable_thread_tracking: 0
2025/06/30-10:05:55.062963 281473575824056                  Options.enable_pipelined_write: 0
2025/06/30-10:05:55.062964 281473575824056                  Options.unordered_write: 0
2025/06/30-10:05:55.062964 281473575824056         Options.allow_concurrent_memtable_write: 1
2025/06/30-10:05:55.062965 281473575824056      Options.enable_write_thread_adaptive_yield: 1
2025/06/30-10:05:55.062965 281473575824056             Options.write_thread_max_yield_usec: 100
2025/06/30-10:05:55.062966 281473575824056            Options.write_thread_slow_yield_usec: 3
2025/06/30-10:05:55.062967 281473575824056                               Options.row_cache: None
2025/06/30-10:05:55.062967 281473575824056                              Options.wal_filter: None
2025/06/30-10:05:55.062968 281473575824056             Options.avoid_flush_during_recovery: 0
2025/06/30-10:05:55.062968 281473575824056             Options.allow_ingest_behind: 0
2025/06/30-10:05:55.062969 281473575824056             Options.two_write_queues: 0
2025/06/30-10:05:55.062970 281473575824056             Options.manual_wal_flush: 0
2025/06/30-10:05:55.062970 281473575824056             Options.wal_compression: 0
2025/06/30-10:05:55.062971 281473575824056             Options.atomic_flush: 0
2025/06/30-10:05:55.062971 281473575824056             Options.avoid_unnecessary_blocking_io: 0
2025/06/30-10:05:55.062972 281473575824056                 Options.persist_stats_to_disk: 0
2025/06/30-10:05:55.062972 281473575824056                 Options.write_dbid_to_manifest: 0
2025/06/30-10:05:55.062973 281473575824056                 Options.log_readahead_size: 0
2025/06/30-10:05:55.062973 281473575824056                 Options.file_checksum_gen_factory: Unknown
2025/06/30-10:05:55.062974 281473575824056                 Options.best_efforts_recovery: 0
2025/06/30-10:05:55.062975 281473575824056                Options.max_bgerror_resume_count: 2147483647
2025/06/30-10:05:55.062975 281473575824056            Options.bgerror_resume_retry_interval: 1000000
2025/06/30-10:05:55.062976 281473575824056             Options.allow_data_in_errors: 0
2025/06/30-10:05:55.062976 281473575824056             Options.db_host_id: __hostname__
2025/06/30-10:05:55.062977 281473575824056             Options.enforce_single_del_contracts: true
2025/06/30-10:05:55.062978 281473575824056             Options.max_background_jobs: 2
2025/06/30-10:05:55.062978 281473575824056             Options.max_background_compactions: -1
2025/06/30-10:05:55.062979 281473575824056             Options.max_subcompactions: 1
2025/06/30-10:05:55.062979 281473575824056             Options.avoid_flush_during_shutdown: 0
2025/06/30-10:05:55.062980 281473575824056           Options.writable_file_max_buffer_size: 1048576
2025/06/30-10:05:55.062980 281473575824056             Options.delayed_write_rate : 16777216
2025/06/30-10:05:55.062981 281473575824056             Options.max_total_wal_size: 1073741824
2025/06/30-10:05:55.062981 281473575824056             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/30-10:05:55.062982 281473575824056                   Options.stats_dump_period_sec: 600
2025/06/30-10:05:55.062983 281473575824056                 Options.stats_persist_period_sec: 600
2025/06/30-10:05:55.062984 281473575824056                 Options.stats_history_buffer_size: 1048576
2025/06/30-10:05:55.062984 281473575824056                          Options.max_open_files: -1
2025/06/30-10:05:55.062985 281473575824056                          Options.bytes_per_sync: 0
2025/06/30-10:05:55.062985 281473575824056                      Options.wal_bytes_per_sync: 0
2025/06/30-10:05:55.062986 281473575824056                   Options.strict_bytes_per_sync: 0
2025/06/30-10:05:55.062986 281473575824056       Options.compaction_readahead_size: 2097152
2025/06/30-10:05:55.062987 281473575824056                  Options.max_background_flushes: -1
2025/06/30-10:05:55.062987 281473575824056 Options.daily_offpeak_time_utc: 
2025/06/30-10:05:55.062988 281473575824056 Compression algorithms supported:
2025/06/30-10:05:55.062989 281473575824056 	kZSTDNotFinalCompression supported: 1
2025/06/30-10:05:55.062990 281473575824056 	kZSTD supported: 1
2025/06/30-10:05:55.062991 281473575824056 	kXpressCompression supported: 0
2025/06/30-10:05:55.062991 281473575824056 	kLZ4HCCompression supported: 1
2025/06/30-10:05:55.062992 281473575824056 	kLZ4Compression supported: 1
2025/06/30-10:05:55.062992 281473575824056 	kBZip2Compression supported: 1
2025/06/30-10:05:55.062993 281473575824056 	kZlibCompression supported: 1
2025/06/30-10:05:55.062993 281473575824056 	kSnappyCompression supported: 1
2025/06/30-10:05:55.062995 281473575824056 Fast CRC32 supported: Supported on Arm64
2025/06/30-10:05:55.062996 281473575824056 DMutex implementation: pthread_mutex_t
2025/06/30-10:05:55.063351 281473575824056 [/version_set.cc:5906] Recovering from manifest file: /home/<USER>/data/protocol/raft/naming_service_metadata/log/MANIFEST-000017
2025/06/30-10:05:55.063522 281473575824056 [/column_family.cc:618] --------------- Options for column family [default]:
2025/06/30-10:05:55.063526 281473575824056               Options.comparator: leveldb.BytewiseComparator
2025/06/30-10:05:55.063527 281473575824056           Options.merge_operator: StringAppendOperator
2025/06/30-10:05:55.063528 281473575824056        Options.compaction_filter: None
2025/06/30-10:05:55.063529 281473575824056        Options.compaction_filter_factory: None
2025/06/30-10:05:55.063529 281473575824056  Options.sst_partitioner_factory: None
2025/06/30-10:05:55.063530 281473575824056         Options.memtable_factory: SkipListFactory
2025/06/30-10:05:55.063531 281473575824056            Options.table_factory: BlockBasedTable
2025/06/30-10:05:55.063561 281473575824056            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff99c1cb00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff99d2d320
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-10:05:55.063564 281473575824056        Options.write_buffer_size: 134217728
2025/06/30-10:05:55.063565 281473575824056  Options.max_write_buffer_number: 6
2025/06/30-10:05:55.063566 281473575824056        Options.compression[0]: NoCompression
2025/06/30-10:05:55.063566 281473575824056        Options.compression[1]: NoCompression
2025/06/30-10:05:55.063567 281473575824056        Options.compression[2]: LZ4
2025/06/30-10:05:55.063568 281473575824056        Options.compression[3]: LZ4
2025/06/30-10:05:55.063568 281473575824056        Options.compression[4]: LZ4
2025/06/30-10:05:55.063569 281473575824056        Options.compression[5]: LZ4
2025/06/30-10:05:55.063570 281473575824056        Options.compression[6]: LZ4
2025/06/30-10:05:55.063570 281473575824056                  Options.bottommost_compression: Disabled
2025/06/30-10:05:55.063571 281473575824056       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/30-10:05:55.063572 281473575824056   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-10:05:55.063572 281473575824056             Options.num_levels: 7
2025/06/30-10:05:55.063573 281473575824056        Options.min_write_buffer_number_to_merge: 2
2025/06/30-10:05:55.063573 281473575824056     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-10:05:55.063574 281473575824056     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-10:05:55.063575 281473575824056            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-10:05:55.063575 281473575824056                  Options.bottommost_compression_opts.level: 32767
2025/06/30-10:05:55.063576 281473575824056               Options.bottommost_compression_opts.strategy: 0
2025/06/30-10:05:55.063576 281473575824056         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-10:05:55.063577 281473575824056         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-10:05:55.063577 281473575824056         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-10:05:55.063578 281473575824056                  Options.bottommost_compression_opts.enabled: false
2025/06/30-10:05:55.063579 281473575824056         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-10:05:55.063579 281473575824056         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-10:05:55.063580 281473575824056            Options.compression_opts.window_bits: -14
2025/06/30-10:05:55.063580 281473575824056                  Options.compression_opts.level: 32767
2025/06/30-10:05:55.063581 281473575824056               Options.compression_opts.strategy: 0
2025/06/30-10:05:55.063581 281473575824056         Options.compression_opts.max_dict_bytes: 0
2025/06/30-10:05:55.063582 281473575824056         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-10:05:55.063583 281473575824056         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-10:05:55.063583 281473575824056         Options.compression_opts.parallel_threads: 1
2025/06/30-10:05:55.063584 281473575824056                  Options.compression_opts.enabled: false
2025/06/30-10:05:55.063584 281473575824056         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-10:05:55.063585 281473575824056      Options.level0_file_num_compaction_trigger: 2
2025/06/30-10:05:55.063585 281473575824056          Options.level0_slowdown_writes_trigger: 20
2025/06/30-10:05:55.063586 281473575824056              Options.level0_stop_writes_trigger: 40
2025/06/30-10:05:55.063586 281473575824056                   Options.target_file_size_base: 67108864
2025/06/30-10:05:55.063587 281473575824056             Options.target_file_size_multiplier: 1
2025/06/30-10:05:55.063588 281473575824056                Options.max_bytes_for_level_base: 536870912
2025/06/30-10:05:55.063588 281473575824056 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-10:05:55.063589 281473575824056          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-10:05:55.063589 281473575824056 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-10:05:55.063590 281473575824056 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-10:05:55.063591 281473575824056 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-10:05:55.063591 281473575824056 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-10:05:55.063592 281473575824056 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-10:05:55.063593 281473575824056 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-10:05:55.063593 281473575824056 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-10:05:55.063594 281473575824056       Options.max_sequential_skip_in_iterations: 8
2025/06/30-10:05:55.063594 281473575824056                    Options.max_compaction_bytes: 1677721600
2025/06/30-10:05:55.063595 281473575824056   Options.ignore_max_compaction_bytes_for_input: true
2025/06/30-10:05:55.063596 281473575824056                        Options.arena_block_size: 1048576
2025/06/30-10:05:55.063596 281473575824056   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-10:05:55.063597 281473575824056   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-10:05:55.063597 281473575824056                Options.disable_auto_compactions: 0
2025/06/30-10:05:55.063598 281473575824056                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-10:05:55.063599 281473575824056                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-10:05:55.063600 281473575824056 Options.compaction_options_universal.size_ratio: 1
2025/06/30-10:05:55.063600 281473575824056 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-10:05:55.063601 281473575824056 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-10:05:55.063601 281473575824056 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-10:05:55.063602 281473575824056 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-10:05:55.063603 281473575824056 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-10:05:55.063603 281473575824056 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-10:05:55.063604 281473575824056 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-10:05:55.063607 281473575824056                   Options.table_properties_collectors: 
2025/06/30-10:05:55.063607 281473575824056                   Options.inplace_update_support: 0
2025/06/30-10:05:55.063608 281473575824056                 Options.inplace_update_num_locks: 10000
2025/06/30-10:05:55.063608 281473575824056               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/30-10:05:55.063609 281473575824056               Options.memtable_whole_key_filtering: 0
2025/06/30-10:05:55.063610 281473575824056   Options.memtable_huge_page_size: 0
2025/06/30-10:05:55.063610 281473575824056                           Options.bloom_locality: 0
2025/06/30-10:05:55.063611 281473575824056                    Options.max_successive_merges: 0
2025/06/30-10:05:55.063611 281473575824056                Options.optimize_filters_for_hits: 0
2025/06/30-10:05:55.063612 281473575824056                Options.paranoid_file_checks: 0
2025/06/30-10:05:55.063612 281473575824056                Options.force_consistency_checks: 1
2025/06/30-10:05:55.063613 281473575824056                Options.report_bg_io_stats: 0
2025/06/30-10:05:55.063613 281473575824056                               Options.ttl: 2592000
2025/06/30-10:05:55.063614 281473575824056          Options.periodic_compaction_seconds: 0
2025/06/30-10:05:55.063615 281473575824056                        Options.default_temperature: kUnknown
2025/06/30-10:05:55.063615 281473575824056  Options.preclude_last_level_data_seconds: 0
2025/06/30-10:05:55.063616 281473575824056    Options.preserve_internal_time_seconds: 0
2025/06/30-10:05:55.063616 281473575824056                       Options.enable_blob_files: false
2025/06/30-10:05:55.063617 281473575824056                           Options.min_blob_size: 0
2025/06/30-10:05:55.063617 281473575824056                          Options.blob_file_size: 268435456
2025/06/30-10:05:55.063618 281473575824056                   Options.blob_compression_type: NoCompression
2025/06/30-10:05:55.063619 281473575824056          Options.enable_blob_garbage_collection: false
2025/06/30-10:05:55.063630 281473575824056      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-10:05:55.063632 281473575824056 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-10:05:55.063632 281473575824056          Options.blob_compaction_readahead_size: 0
2025/06/30-10:05:55.063633 281473575824056                Options.blob_file_starting_level: 0
2025/06/30-10:05:55.063633 281473575824056         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-10:05:55.063634 281473575824056            Options.memtable_max_range_deletions: 0
2025/06/30-10:05:55.067621 281473575824056 [/column_family.cc:618] --------------- Options for column family [Configuration]:
2025/06/30-10:05:55.067626 281473575824056               Options.comparator: leveldb.BytewiseComparator
2025/06/30-10:05:55.067627 281473575824056           Options.merge_operator: StringAppendOperator
2025/06/30-10:05:55.067628 281473575824056        Options.compaction_filter: None
2025/06/30-10:05:55.067628 281473575824056        Options.compaction_filter_factory: None
2025/06/30-10:05:55.067629 281473575824056  Options.sst_partitioner_factory: None
2025/06/30-10:05:55.067630 281473575824056         Options.memtable_factory: SkipListFactory
2025/06/30-10:05:55.067630 281473575824056            Options.table_factory: BlockBasedTable
2025/06/30-10:05:55.067656 281473575824056            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff99c1cb00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff99d2d320
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-10:05:55.067658 281473575824056        Options.write_buffer_size: 134217728
2025/06/30-10:05:55.067659 281473575824056  Options.max_write_buffer_number: 6
2025/06/30-10:05:55.067660 281473575824056        Options.compression[0]: NoCompression
2025/06/30-10:05:55.067660 281473575824056        Options.compression[1]: NoCompression
2025/06/30-10:05:55.067661 281473575824056        Options.compression[2]: LZ4
2025/06/30-10:05:55.067662 281473575824056        Options.compression[3]: LZ4
2025/06/30-10:05:55.067662 281473575824056        Options.compression[4]: LZ4
2025/06/30-10:05:55.067663 281473575824056        Options.compression[5]: LZ4
2025/06/30-10:05:55.067663 281473575824056        Options.compression[6]: LZ4
2025/06/30-10:05:55.067664 281473575824056                  Options.bottommost_compression: Disabled
2025/06/30-10:05:55.067665 281473575824056       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/30-10:05:55.067666 281473575824056   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-10:05:55.067667 281473575824056             Options.num_levels: 7
2025/06/30-10:05:55.067667 281473575824056        Options.min_write_buffer_number_to_merge: 2
2025/06/30-10:05:55.067668 281473575824056     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-10:05:55.067668 281473575824056     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-10:05:55.067669 281473575824056            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-10:05:55.067670 281473575824056                  Options.bottommost_compression_opts.level: 32767
2025/06/30-10:05:55.067670 281473575824056               Options.bottommost_compression_opts.strategy: 0
2025/06/30-10:05:55.067671 281473575824056         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-10:05:55.067672 281473575824056         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-10:05:55.067672 281473575824056         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-10:05:55.067673 281473575824056                  Options.bottommost_compression_opts.enabled: false
2025/06/30-10:05:55.067673 281473575824056         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-10:05:55.067674 281473575824056         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-10:05:55.067675 281473575824056            Options.compression_opts.window_bits: -14
2025/06/30-10:05:55.067675 281473575824056                  Options.compression_opts.level: 32767
2025/06/30-10:05:55.067676 281473575824056               Options.compression_opts.strategy: 0
2025/06/30-10:05:55.067676 281473575824056         Options.compression_opts.max_dict_bytes: 0
2025/06/30-10:05:55.067677 281473575824056         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-10:05:55.067677 281473575824056         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-10:05:55.067678 281473575824056         Options.compression_opts.parallel_threads: 1
2025/06/30-10:05:55.067678 281473575824056                  Options.compression_opts.enabled: false
2025/06/30-10:05:55.067679 281473575824056         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-10:05:55.067680 281473575824056      Options.level0_file_num_compaction_trigger: 2
2025/06/30-10:05:55.067680 281473575824056          Options.level0_slowdown_writes_trigger: 20
2025/06/30-10:05:55.067681 281473575824056              Options.level0_stop_writes_trigger: 40
2025/06/30-10:05:55.067681 281473575824056                   Options.target_file_size_base: 67108864
2025/06/30-10:05:55.067682 281473575824056             Options.target_file_size_multiplier: 1
2025/06/30-10:05:55.067682 281473575824056                Options.max_bytes_for_level_base: 536870912
2025/06/30-10:05:55.067683 281473575824056 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-10:05:55.067683 281473575824056          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-10:05:55.067684 281473575824056 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-10:05:55.067685 281473575824056 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-10:05:55.067685 281473575824056 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-10:05:55.067686 281473575824056 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-10:05:55.067687 281473575824056 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-10:05:55.067687 281473575824056 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-10:05:55.067688 281473575824056 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-10:05:55.067688 281473575824056       Options.max_sequential_skip_in_iterations: 8
2025/06/30-10:05:55.067689 281473575824056                    Options.max_compaction_bytes: 1677721600
2025/06/30-10:05:55.067689 281473575824056   Options.ignore_max_compaction_bytes_for_input: true
2025/06/30-10:05:55.067690 281473575824056                        Options.arena_block_size: 1048576
2025/06/30-10:05:55.067691 281473575824056   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-10:05:55.067691 281473575824056   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-10:05:55.067692 281473575824056                Options.disable_auto_compactions: 0
2025/06/30-10:05:55.067693 281473575824056                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-10:05:55.067694 281473575824056                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-10:05:55.067695 281473575824056 Options.compaction_options_universal.size_ratio: 1
2025/06/30-10:05:55.067696 281473575824056 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-10:05:55.067696 281473575824056 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-10:05:55.067697 281473575824056 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-10:05:55.067697 281473575824056 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-10:05:55.067698 281473575824056 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-10:05:55.067699 281473575824056 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-10:05:55.067699 281473575824056 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-10:05:55.067701 281473575824056                   Options.table_properties_collectors: 
2025/06/30-10:05:55.067702 281473575824056                   Options.inplace_update_support: 0
2025/06/30-10:05:55.067702 281473575824056                 Options.inplace_update_num_locks: 10000
2025/06/30-10:05:55.067703 281473575824056               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/30-10:05:55.067704 281473575824056               Options.memtable_whole_key_filtering: 0
2025/06/30-10:05:55.067704 281473575824056   Options.memtable_huge_page_size: 0
2025/06/30-10:05:55.067705 281473575824056                           Options.bloom_locality: 0
2025/06/30-10:05:55.067705 281473575824056                    Options.max_successive_merges: 0
2025/06/30-10:05:55.067706 281473575824056                Options.optimize_filters_for_hits: 0
2025/06/30-10:05:55.067706 281473575824056                Options.paranoid_file_checks: 0
2025/06/30-10:05:55.067707 281473575824056                Options.force_consistency_checks: 1
2025/06/30-10:05:55.067707 281473575824056                Options.report_bg_io_stats: 0
2025/06/30-10:05:55.067708 281473575824056                               Options.ttl: 2592000
2025/06/30-10:05:55.067709 281473575824056          Options.periodic_compaction_seconds: 0
2025/06/30-10:05:55.067709 281473575824056                        Options.default_temperature: kUnknown
2025/06/30-10:05:55.067710 281473575824056  Options.preclude_last_level_data_seconds: 0
2025/06/30-10:05:55.067710 281473575824056    Options.preserve_internal_time_seconds: 0
2025/06/30-10:05:55.067711 281473575824056                       Options.enable_blob_files: false
2025/06/30-10:05:55.067712 281473575824056                           Options.min_blob_size: 0
2025/06/30-10:05:55.067712 281473575824056                          Options.blob_file_size: 268435456
2025/06/30-10:05:55.067713 281473575824056                   Options.blob_compression_type: NoCompression
2025/06/30-10:05:55.067713 281473575824056          Options.enable_blob_garbage_collection: false
2025/06/30-10:05:55.067714 281473575824056      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-10:05:55.067714 281473575824056 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-10:05:55.067715 281473575824056          Options.blob_compaction_readahead_size: 0
2025/06/30-10:05:55.067716 281473575824056                Options.blob_file_starting_level: 0
2025/06/30-10:05:55.067716 281473575824056         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-10:05:55.067717 281473575824056            Options.memtable_max_range_deletions: 0
2025/06/30-10:05:55.159652 281473575824056 [/version_set.cc:5957] Recovered from manifest file:/home/<USER>/data/protocol/raft/naming_service_metadata/log/MANIFEST-000017 succeeded,manifest_file_number is 17, next_file_number is 23, last_sequence is 38, log_number is 11,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 11
2025/06/30-10:05:55.159662 281473575824056 [/version_set.cc:5966] Column family [default] (ID 0), log number is 11
2025/06/30-10:05:55.159664 281473575824056 [/version_set.cc:5966] Column family [Configuration] (ID 1), log number is 11
2025/06/30-10:05:55.160307 281473575824056 [/db_impl/db_impl_open.cc:646] DB ID: e9336bcf-bc16-41a2-95a2-12811a2eff46
2025/06/30-10:05:55.160917 281473575824056 EVENT_LOG_v1 {"time_micros": 1751249155160912, "job": 1, "event": "recovery_started", "wal_files": [16]}
2025/06/30-10:05:55.160921 281473575824056 [/db_impl/db_impl_open.cc:1145] Recovering log #16 mode 2
2025/06/30-10:05:55.161795 281473575824056 EVENT_LOG_v1 {"time_micros": 1751249155161785, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 24, "file_size": 1067, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 40, "largest_seqno": 40, "table_properties": {"data_size": 0, "index_size": 13, "index_partitions": 0, "top_level_index_size": 8, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 0, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751249155, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "e9336bcf-bc16-41a2-95a2-12811a2eff46", "db_session_id": "DJ073KN838NTE1LSV2OK", "orig_file_number": 24, "seqno_to_time_mapping": "N/A"}}
2025/06/30-10:05:55.162954 281473575824056 EVENT_LOG_v1 {"time_micros": 1751249155162944, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 25, "file_size": 1218, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 41, "largest_seqno": 42, "table_properties": {"data_size": 50, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 42, "raw_average_key_size": 21, "raw_value_size": 16, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751249155, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "e9336bcf-bc16-41a2-95a2-12811a2eff46", "db_session_id": "DJ073KN838NTE1LSV2OK", "orig_file_number": 25, "seqno_to_time_mapping": "N/A"}}
2025/06/30-10:05:55.163583 281473575824056 EVENT_LOG_v1 {"time_micros": 1751249155163582, "job": 1, "event": "recovery_finished"}
2025/06/30-10:05:55.163972 281473575824056 [/version_set.cc:5417] Creating manifest 27
2025/06/30-10:05:55.168896 281473575824056 [le/delete_scheduler.cc:77] Deleted file /home/<USER>/data/protocol/raft/naming_service_metadata/log/000016.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/30-10:05:55.169171 281473575824056 [/db_impl/db_impl_open.cc:2150] SstFileManager instance 0xffff99c1e7f0
2025/06/30-10:05:55.169409 281473575824056 DB pointer 0xffff96657180
2025/06/30-10:06:04.170242 281473218246120 [/db_impl/db_impl.cc:1141] ------- DUMPING STATS -------
2025/06/30-10:06:04.170631 281473218246120 [/db_impl/db_impl.cc:1142] 
** DB Stats **
Uptime(secs): 9.1 total, 9.1 interval
Cumulative writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.04 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      1/0    1.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 9.1 total, 9.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff99d2d320#1 capacity: 8.00 MB seed: 775045006 usage: 1.35 KB table_size: 256 occupancy: 7 collections: 1 last_copies: 1 last_secs: 0.000119 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(4,0.52 KB,0.00634193%) IndexBlock(2,0.24 KB,0.00293255%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 5 Average: 2.4000  StdDev: 4.80
Min: 0  Median: 0.6250  Max: 12
Percentiles: P50: 0.62 P75: 0.94 P99: 12.00 P99.9: 12.00 P99.99: 12.00
------------------------------------------------------
[       0,       1 ]        4  80.000%  80.000% ################
(      10,      15 ]        1  20.000% 100.000% ####


** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.19 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0
  L6      1/0    1.15 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      2/0    2.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.5      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 9.1 total, 9.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0xffff99d2d320#1 capacity: 8.00 MB seed: 775045006 usage: 1.35 KB table_size: 256 occupancy: 7 collections: 1 last_copies: 1 last_secs: 0.000119 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(4,0.52 KB,0.00634193%) IndexBlock(2,0.24 KB,0.00293255%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 7 Average: 25.8571  StdDev: 44.50
Min: 0  Median: 0.8750  Max: 127
Percentiles: P50: 0.88 P75: 38.25 P99: 127.00 P99.9: 127.00 P99.99: 127.00
------------------------------------------------------
[       0,       1 ]        4  57.143%  57.143% ###########
(       2,       3 ]        1  14.286%  71.429% ###
(      34,      51 ]        1  14.286%  85.714% ###
(     110,     170 ]        1  14.286% 100.000% ###

** Level 6 read latency histogram (micros):
Count: 6 Average: 52.8333  StdDev: 112.33
Min: 1  Median: 3.0000  Max: 304
Percentiles: P50: 3.00 P75: 3.75 P99: 304.00 P99.9: 304.00 P99.99: 304.00
------------------------------------------------------
[       0,       1 ]        2  33.333%  33.333% #######
(       2,       3 ]        1  16.667%  50.000% ###
(       3,       4 ]        2  33.333%  83.333% #######
(     250,     380 ]        1  16.667% 100.000% ###

2025/06/30-10:06:04.171216 281473218246120 [/db_impl/db_impl.cc:751] STATISTICS:
 rocksdb.block.cache.miss COUNT : 6
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 6
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 2
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 2
rocksdb.block.cache.index.bytes.insert COUNT : 246
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.data.miss COUNT : 4
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 4
rocksdb.block.cache.data.bytes.insert COUNT : 532
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 778
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 5
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 168
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.opens COUNT : 3
rocksdb.no.file.errors COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.bloom.filter.prefix.true.positive COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 168
rocksdb.write.self COUNT : 5
rocksdb.write.other COUNT : 0
rocksdb.write.wal COUNT : 5
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2285
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.compaction.total.time.cpu_micros COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 1
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.marked.trash.deleted COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.error.count COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.error.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.error.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 1174
rocksdb.last.level.read.count COUNT : 6
rocksdb.non.last.level.read.bytes COUNT : 3468
rocksdb.non.last.level.read.count COUNT : 12
rocksdb.last.level.seek.filtered COUNT : 0
rocksdb.last.level.seek.filter.match COUNT : 0
rocksdb.last.level.seek.data COUNT : 1
rocksdb.last.level.seek.data.useful.no.filter COUNT : 0
rocksdb.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.non.last.level.seek.filtered COUNT : 0
rocksdb.non.last.level.seek.filter.match COUNT : 0
rocksdb.non.last.level.seek.data COUNT : 1
rocksdb.non.last.level.seek.data.useful.no.filter COUNT : 1
rocksdb.non.last.level.seek.data.useful.filter.match COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 12
rocksdb.block.checksum.mismatch.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.read.async.micros COUNT : 0
rocksdb.async.read.error.count COUNT : 0
rocksdb.secondary.cache.filter.hits COUNT : 0
rocksdb.secondary.cache.index.hits COUNT : 0
rocksdb.secondary.cache.data.hits COUNT : 0
rocksdb.table.open.prefetch.tail.miss COUNT : 0
rocksdb.table.open.prefetch.tail.hit COUNT : 0
rocksdb.timestamp.filter.table.checked COUNT : 0
rocksdb.timestamp.filter.table.filtered COUNT : 0
rocksdb.bytes.compressed.from COUNT : 0
rocksdb.bytes.compressed.to COUNT : 0
rocksdb.bytes.compression_bypassed COUNT : 113
rocksdb.bytes.compression.rejected COUNT : 0
rocksdb.number.block_compression_bypassed COUNT : 4
rocksdb.number.block_compression_rejected COUNT : 0
rocksdb.bytes.decompressed.from COUNT : 0
rocksdb.bytes.decompressed.to COUNT : 0
rocksdb.readahead.trimmed COUNT : 0
rocksdb.fifo.max.size.compactions COUNT : 0
rocksdb.fifo.ttl.compactions COUNT : 0
rocksdb.prefetch.bytes COUNT : 0
rocksdb.prefetch.bytes.useful COUNT : 35
rocksdb.prefetch.hits COUNT : 1
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 347.500000 P95 : 82908.000000 P99 : 82908.000000 P100 : 82908.000000 COUNT : 5 SUM : 84412
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 110.000000 P95 : 119.000000 P99 : 119.000000 P100 : 119.000000 COUNT : 2 SUM : 225
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 194.000000 P95 : 194.000000 P99 : 194.000000 P100 : 194.000000 COUNT : 1 SUM : 194
rocksdb.manifest.file.sync.micros P50 : 45.000000 P95 : 45.000000 P99 : 45.000000 P100 : 45.000000 COUNT : 1 SUM : 45
rocksdb.table.open.io.micros P50 : 210.000000 P95 : 526.000000 P99 : 526.000000 P100 : 526.000000 COUNT : 3 SUM : 822
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 2.500000 P95 : 9.100000 P99 : 9.820000 P100 : 10.000000 COUNT : 9 SUM : 38
rocksdb.write.raw.block.micros P50 : 0.500000 P95 : 0.950000 P99 : 0.990000 P100 : 1.000000 COUNT : 10 SUM : 5
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 0.900000 P95 : 263.000000 P99 : 304.000000 P100 : 304.000000 COUNT : 18 SUM : 510
rocksdb.file.read.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.open.micros P50 : 0.833333 P95 : 282.500000 P99 : 304.000000 P100 : 304.000000 COUNT : 15 SUM : 503
rocksdb.file.read.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.db.iterator.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.db.checksum.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.file.read.verify.file.checksums.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 5 SUM : 168
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1133.000000 P95 : 1133.000000 P99 : 1133.000000 P100 : 1133.000000 COUNT : 1 SUM : 1133
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.open.prefetch.tail.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/30-10:06:05.169854 281473218246120 [/db_impl/db_impl.cc:965] ------- PERSISTING STATS -------
2025/06/30-10:06:05.169874 281473218246120 [/db_impl/db_impl.cc:1035] [Pre-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/06/30-10:06:05.169875 281473218246120 [/db_impl/db_impl.cc:1044] [Post-GC] In-memory stats history size: 48 bytes, slice count: 0
2025/06/30-10:07:42.874831 281473166869992 [/db_impl/db_impl.cc:487] Shutdown: canceling all background work
2025/06/30-10:07:42.877298 281473166869992 [/db_impl/db_impl.cc:668] Shutdown complete
