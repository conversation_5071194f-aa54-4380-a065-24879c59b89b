version: '3.0'

services:

  zookeeper:
    container_name: ecp-zookeeper
    image: 'bitnami/zookeeper:3.8'
    ports:
      - '2181:2181'
    environment:
      - ALLOW_ANONYMOUS_LOGIN=yes
      - TZ=Asia/Shanghai
      # Zookeeper JVM优化 - 800MB
      - JVMFLAGS=-Xms256m -Xmx800m -XX:+UseG1GC

    # 内存限制 - 800MB
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "echo ruok | nc localhost 2181 | grep imok || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3

    restart: always

  kafka:
    image: 'bitnami/kafka:3.4'
    container_name: ecp-kafka
    volumes:
      - './ecp-kafka/certs:/bitnami/kafka/config/certs:rw'
    ports:
      - '9092:9092'  # PLAINTEXT
      - '9093:9093'  # SSL
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_CFG_LISTENERS=PLAINTEXT://0.0.0.0:9092,SSL://0.0.0.0:9093
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092,SSL://localhost:9093
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,SSL:SSL
      - KAFKA_CFG_ZOOKEEPER_CONNECT=zookeeper:2181
      - ALLOW_PLAINTEXT_LISTENER=yes
      - TZ=Asia/Shanghai

      # SSL Configuration
      - KAFKA_CFG_SSL_KEYSTORE_LOCATION=/bitnami/kafka/config/certs/kafka.server.keystore.jks
      - KAFKA_CFG_SSL_KEYSTORE_PASSWORD=changeit
      - KAFKA_CFG_SSL_KEY_PASSWORD=changeit
      - KAFKA_CFG_SSL_TRUSTSTORE_LOCATION=/bitnami/kafka/config/certs/kafka.server.truststore.jks
      - KAFKA_CFG_SSL_TRUSTSTORE_PASSWORD=changeit
      - KAFKA_CFG_SSL_CLIENT_AUTH=required
      - KAFKA_CFG_SECURITY_INTER_BROKER_PROTOCOL=PLAINTEXT
      - KAFKA_CFG_SSL_ENDPOINT_IDENTIFICATION_ALGORITHM=

      # Kafka JVM优化 - 1.5GB
      - KAFKA_HEAP_OPTS=-Xms512m -Xmx1536m
      - KAFKA_JVM_PERFORMANCE_OPTS=-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions

      # Kafka性能优化
      - KAFKA_CFG_NUM_NETWORK_THREADS=8
      - KAFKA_CFG_NUM_IO_THREADS=8
      - KAFKA_CFG_SOCKET_SEND_BUFFER_BYTES=102400
      - KAFKA_CFG_SOCKET_RECEIVE_BUFFER_BYTES=102400
      - KAFKA_CFG_SOCKET_REQUEST_MAX_BYTES=104857600

    # 内存限制 - 1.5GB
    deploy:
      resources:
        limits:
          memory: 1024M
          cpus: '1.5'
        reservations:
          memory: 512M
          cpus: '0.5'

    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics.sh --bootstrap-server localhost:9092 --list || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    restart: always
    depends_on:
      - zookeeper
