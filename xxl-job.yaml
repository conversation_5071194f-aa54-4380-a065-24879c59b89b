version: '3.0'

services:
  xxl2.2.0:
    restart: always
    image:  xuxueli/xxl-job-admin:2.2.0
    container_name: ecp-xxl-job-admin
    volumes:
      - ${ROOT_ENV_DIR}/ecp-xxl-job:/data/applogs
    ports:
      - "8800:8800"
    environment:
      PARAMS: '
        --server.port=8800
        --server.servlet.context-path=/xxl-job-admin
        --spring.datasource.url=********************************************************************************************************************************************************************************************
        --spring.datasource.username=root
        --spring.datasource.password=${MYSQL_ROOT_PASSWORD}
        '
      # JVM内存优化 - 500MB
      JAVA_OPTS: '-Xms128m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200'
      TZ: 'Asia/Shanghai'

    # 内存限制 - 500MB
    deploy:
      resources:
        limits:
          memory: 820M
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8800/xxl-job-admin || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

    depends_on:
      - mysql