osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.io.naming)
310; version=1.4.20
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.model)
308; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.io.xml)
310; version=1.4.20
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=javax.inject)
9; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.io)
310; version=1.4.20
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream)
310; version=1.4.20
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.converters.basic)
310; version=1.4.20
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.converters.reflection)
310; version=1.4.20
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.security)
310; version=1.4.20
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.converters.collections)
310; version=1.4.20
osgi.wiring.package; (osgi.wiring.package=org.slf4j)
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.converters)
310; version=1.4.20
