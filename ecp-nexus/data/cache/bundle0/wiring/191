osgi.wiring.package; (osgi.wiring.package=org.apache.http.conn.socket)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.conn.routing)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.protocol)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics)(version>=4.1.0)(!(version>=5.0.0)))
15; version=4.1.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.conn.ssl)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.conn)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.pool)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.conn)
78; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.utils)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.config)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.methods)
78; version=0.0.0
