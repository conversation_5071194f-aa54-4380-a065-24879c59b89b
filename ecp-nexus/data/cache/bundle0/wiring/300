osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.extdirect)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.quorum)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang)(version>=2.6.0))
138; version=2.6.0
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.annotation)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.node)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.callsite)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.entity)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.facet)(version>=3.70.0))
292; version=3.70.4
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.manager)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.core)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.fluent)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.storage)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.softwarementors.extjs.djn.config.annotations)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform.builder)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.util)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=javax.validation.constraints)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.typehandling)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.extdirect.model)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rapture)(version>=3.70.0))
235; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=groovy.lang)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.annotation)(version>=4.1.0))
169; version=4.1.0
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu)
103; version=0.0.0
