osgi.wiring.package; (&(osgi.wiring.package=javax.validation)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (osgi.wiring.package=org.apache.http.conn.routing)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.annotation)(version>=2.17.0))
167; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation.constraint)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.conn)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.protocol)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.net.ssl)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.auth)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.conn)
78; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=org.apache.http.config)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.methods)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.auth)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.config)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.protocol)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.validation.constraints)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=org.apache.http)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.text)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu)
103; version=0.0.0
