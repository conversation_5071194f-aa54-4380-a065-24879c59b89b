osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.restore.datastore)(version>=3.70.0))
263; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.event)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.db.document)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.authc.apikey)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.annotation)(version>=2.17.0))
167; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.node)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.reflect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.audit)(version>=3.70.0))
148; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rest)(version>=3.70.0))
278; version=3.70.4
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.purge)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.manager)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.search.sql)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.datatype.joda)(version>=2.17.0))
137; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.i18n)(version>=2.3.0))
165; version=2.3.8
osgi.wiring.package; (osgi.wiring.package=org.apache.http.util)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.record.impl)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.mgt)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.metadata.schema)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.handlers)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.search.sql)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.cache)(version>=1.1.0))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rest.sql)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.browse)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.typehandling)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.hash)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.cleanup.config)(version>=3.70.0))
294; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.browse.store)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.node)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.search.normalize)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rapture)(version>=3.70.0))
235; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.io)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.aether.util)(version>=1.1.0))
105; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=groovy.lang)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu)
103; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.security)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.filter.export)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.stateguard)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core)(version>=2.17.0))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.entity)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.maintenance)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.compress.archivers)(version>=1.26.0))
298; version=1.26.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.matchers.logic)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.api)(version>=3.70.0))
170; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.entity)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.facet)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.firewall.event)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.restore.orient)(version>=3.70.0))
263; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.search.index)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.storage)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.client)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.expiry)(version>=1.1.0))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.importtask)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.datastore.api)(version>=3.70.0))
141; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.event.asset)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.collect)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.exception)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.upgrade.datastore)(version>=3.70.0))
204; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.protocol)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.gson.annotations)(version>=2.9.0))
209; version=2.9.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.transaction)(version>=3.70.0))
159; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.config)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.upload)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authc)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.eventbus)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.replication)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rest.api)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.httpclient)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.cache)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.mime)(version>=3.70.0))
144; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.validation)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.command)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.utils)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.subject)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang)(version>=2.6.0))
138; version=2.6.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.type)(version>=2.17.0))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.attributes)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.search.elasticsearch)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.token)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.callsite)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.cache)(version>=3.70.0))
150; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.formfields)(version>=3.70.0))
179; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.repair)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.browse.node)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.restore)(version>=3.70.0))
263; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.io)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.core)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.cooperation2)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang3)(version>=3.13.0))
172; version=3.13.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.sql)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.matchers.token)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.user)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.entity)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.group)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.gson)(version>=2.9.0))
209; version=2.9.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rest.api.model)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.payloads)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.net)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.routing)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.vulnerability.exceptions)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.store)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.text)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.mime)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.io)(version>=1.4.0))
28; version=2.15.1
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.assistedinject)(version>=1.4.0))
112; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.logging.task)(version>=3.70.0))
29; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.transaction)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.common.concur)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.hash)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.director)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.vulnerability)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.http)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.json)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.annotation)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.proxy)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.capability)(version>=3.70.0))
174; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.scheduling)(version>=3.70.0))
181; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.query)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.search)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.recipe)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.browse)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.gson.stream)(version>=2.9.0))
209; version=2.9.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.escape)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.fluent)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.log)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.methods)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.deser)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.compress.compressors)(version>=1.26.0))
298; version=1.26.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.deser.std)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.firewall)(version>=3.70.0))
350; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.matchers)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.search)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.id)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.replication)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.upgrade)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.configuration)(version>=1.1.0))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.sql.query)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.thread)(version>=3.70.0))
187; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.thread.io)(version>=3.70.0))
187; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.export)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=io.swagger.annotations)(version>=1.6.0))
188; version=1.6.11
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time)(version>=2.10.0))
121; version=2.10.6
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.search.query)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time.format)(version>=2.10.0))
121; version=2.10.6
