osgi.wiring.package; (osgi.wiring.package=javax.xml.namespace)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.print)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.dom)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.validation)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.lang.model.type)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax.ext)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.commons.cli)
135; version=1.5.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.event)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.filechooser)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.rmi.ssl)
0; version=0.0.0
osgi.extender; (osgi.extender=osgi.serviceloader.processor)
96; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.tree)
0; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.fusesource.jansi)(version>=2.4.0)(!(version>=3.0.0)))
26; version=2.4.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.text)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.xpath)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.lang.model.element)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.tools)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.sql)
0; version=0.0.0
osgi.serviceloader; (osgi.serviceloader=org.apache.groovy.json.FastStringServiceFactory)
126
osgi.wiring.package; (osgi.wiring.package=javax.swing)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.print.attribute)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.management.remote)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.management.remote.rmi)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.lang.model)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation.processing)
0; version=1.0.0
osgi.extender; (osgi.extender=osgi.serviceloader.registrar)
96; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=javax.lang.model.util)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.management)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.parsers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.imageio)
0; version=0.0.0
osgi.serviceloader; (osgi.serviceloader=javax.script.ScriptEngineFactory)
127
osgi.wiring.package; (osgi.wiring.package=javax.script)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax.helpers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.stream)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.management.timer)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.management.modelmbean)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.border)
0; version=0.0.0
