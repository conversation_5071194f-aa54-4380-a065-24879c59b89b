osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.util.version)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.networknt.schema)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tukaani.xz.common)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.protocol)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.auth)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.file)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.dataformat.xml.ser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.ser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.builder)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tukaani.xz.lzma)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.deser.impl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.codec)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.artifact.versioning)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.reader)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.fs.archive.zip)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation.meta)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.util)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.repository.legacy.metadata)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.crypto)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.utility.compare)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.nls)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.grammar)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.repository)
306; version=*********
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.licenseTemplate)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.jsoup.select)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.parser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.client.utils)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.io.file.attribute)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.model)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.brain.client)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.diff)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.datatype.jsr310.deser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.plaf)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.objectweb.asm.signature)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.jsoup.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.brain.common.io)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model)
306; version=*********
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.collection)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.deflate64)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tukaani.xz.rangecoder)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.introspect)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.key.sl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.networknt.schema.utils)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.node)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.sr)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.fs.archive.tar)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.metadata)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.verifier.regexp)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.converters.basic)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.googlecode.javaewah.symmetric)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.msv)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.semver4j)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.protocol)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.storage.pack)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.bzip2)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.jsoup)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.error)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tukaani.xz.index)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.format)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.hooks)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.evt)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.deployment)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.sax)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.border)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.stax)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2.validation)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.antlr.v4.runtime.tree.xpath)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.annotations)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.fs.spi)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.entry)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.repository)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.io.file)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv.org_jp_gr_xml.xml)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.parallel)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.antlr.v4.runtime.tree.pattern)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.google.gson.reflect)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv.xsd_util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.io.function)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.type)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.grammar.trex)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.inspector)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.ssl)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.zeroturnaround.exec.stop)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.plexus.util.reflection)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.dircache)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tomlj)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2.evt)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.stream)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.google.gson.internal.bind)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.artifact.resolver.filter)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.client)
307; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.io.build)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.java.util.jar)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.archivers)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.base)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.trex.ng.comp)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.version)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.snappy)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.networknt.schema.regex)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.googlecode.javaewah)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.io)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.zeroturnaround.exec.stream.slf4j)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.resolver)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.artifact.resolver)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.archivers.dump)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.socket.sl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.methods)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.zeroturnaround.exec.stream)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.zstandard)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.key.pbe.swing)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv.org_isorelax.dispatcher)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.io.output)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.jsoup.safety)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.socket)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.networknt.schema.walk)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.cookie)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.io)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.relaxns.grammar)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv.org_jp_gr_xml.dom)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.merge)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.gzip)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.datatype.jsr310.ser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.ignore)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.remediation)
306; version=*********
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.storage.file)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.comments)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.lzma)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.io.naming)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.repository)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tomlj.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.graph)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.notes)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.lib.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.plexus.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.codec.binary)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.dataformat.xml.annotation)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.tree)
0; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.formulation.trigger)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.antlr.v4.runtime.tree)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.dataformat.xml.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.artifact)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.converters.reflection)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.anon)
307; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.networknt.schema.urn)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.config)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.fs)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.organization)
306; version=*********
osgi.wiring.package; (osgi.wiring.package=javax.management)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.transport.resolver)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.stream)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.api)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.neuvector.model)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.events)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.attributes)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.artifact.handler)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.generators.xml)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.sw)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.pack200)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.antlr.v4.runtime.atn)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.io.doubleparser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.namespace)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.library.referencetype)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.grammar.relaxng)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.rof)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.library.model.pointer)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tukaani.xz.delta)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.storage)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.annotations)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.lz77support)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.revwalk)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.hash.internal.asm)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tukaani.xz.check)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.metadata)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.nexus.git.utils.common)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.library.model.license)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.dataformat.yaml.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.relax.core.checker)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.library.model.enumerations)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.gitrepo.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.util)
307; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.storage.pack)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.converters.extended)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.transport.connectivity)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.utils)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.jacksonstore)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.module)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.trex)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.objectweb.asm)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.component.modelCard.data)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.io.schubfach)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.verifier.jaxp)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.networknt.schema.uri)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.inject)
9; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tukaani.xz.lz)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.exc)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.generators.json)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.objectweb.asm.v71.v71)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.stream.util)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv.org_isorelax.dispatcher.impl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.cfg)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.storage.dfs)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.annotation)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ethlo.time.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.application)
306; version=*********
osgi.wiring.package; (osgi.wiring.package=zz.com.google.gson.internal.sql)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.submodule)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.io.path)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.jdk14)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.sym)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.composer)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.antlr.v4.runtime.misc)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.config)
307; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.networknt.org.apache.commons.validator.routines)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.writer)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.manifest)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.events)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.math)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.googlecode.javaewah32)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.grammar.trex.typed)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.datatype.xsd)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.model)
308; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.datatype.jsr310.deser.key)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.text)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.file)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.z)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.semver4j.internal.range)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.artifact.repository)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.dtd)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv.relaxng_datatype)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.formulation.task)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.semver4j.internal.range.processor)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.vulnerability)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.plexus.util.cli.shell)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.grammar.relax)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.harmony.archive.internal.nls)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.google.gson.internal.reflect)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.mutable)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.dom)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.component)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.component.evidence)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.grammar.relaxng.datatype)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.introspector)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.artifact.metadata)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.google.gson.annotations)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.json.store)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.conn.routing)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.objectweb.asm.v60)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.relaxns.reader)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.signature)
306; version=*********
osgi.wiring.package; (osgi.wiring.package=javax.crypto)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.verifier.regexp.xmlschema)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.transport.http)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.storage.file)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.converters)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.io.xml.xppdom)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.cfg)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2.ri.dom)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.trex.classic)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.formulation.workspace)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.arch)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.codec.digest)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.lib)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.auth)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.objectweb.asm.v71)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.grammar.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.ide)
306; version=*********
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.annotation)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.submodule)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.io.xml)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.objectweb.asm.v60.tree)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.file.ruby)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.logging.impl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.antlr.v4.runtime.dfa)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ethlo.time)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.emitter)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2.ri)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.util.io)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.io.charset)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv.relaxng_datatype.helpers)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.compat)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.repository.migration)
306; version=*********
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.model.io.xpp3)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.transport.http)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax.ext)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.datatype.xsd.regex)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.transport)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.datatype)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.key.pbe)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.exception)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.constructor)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.io.input)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.plexus.util.io)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.json.async)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.verifier.jarv)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.xmlschema)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.util.graph.transformer)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.external.com.google.gdata.util.common.base)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.storage.simple)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.mapper)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.json)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.api.errors)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.storage.reftable)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.driver.textui)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.ent)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.zeroturnaround.exec)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.scanner)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.archive)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.datatype.xsd)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.util.sha1)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.jsoup.parser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.dataformat.yaml)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.validation)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.googlecode.javaewah32.symmetric)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.hash)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.objectweb.asm.tree)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.net.ssl)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.stream.events)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=zz.org.objectweb.asm.v71.tree)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.artifact.repository.layout)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.slf4j.spi)
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2.ri.evt)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.plexus.util.xml)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.jsoup.helper)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2.ri.typed)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.text.translate)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.crypto.spec)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.archivers.zip)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.jsoup.nodes)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=sun.nio.ch)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.artifact)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.archivers.tar)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.parsers)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.harmony.unpack200)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.datatype)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.crypto.param)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.component.modelCard)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.zeroturnaround.exec.listener)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.util.graph.traverser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.neuvector)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.time)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.nodes)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.google.gson.stream)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.archivers.sevenz)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.ser.std)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.jsonFormatVisitors)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.security)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.plexus.util.xml.pull)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.grammar.xmlschema)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.treewalk.filter)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.maven.artifact.repository.metadata)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.harmony.unpack200.bytecode)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.dataformat.yaml.snakeyaml.error)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.validation)
75; version=2.0.2
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.sourcecontrol)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.utils)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.googlecode.javaewah.datastructure)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tukaani.xz)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.scanner.dtd)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.datatype)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.datatype.jsr310.ser.key)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.relaxns.reader.trex)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.relaxns.verifier)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.ignore.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.archivers.jar)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.zeroturnaround.exec.close)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.exc)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.core)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.google.gson.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.networknt.schema.format)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.parsers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.component.modelCard.consideration)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.reflect)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.deser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.relaxns.reader.relax)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2.typed)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.formulation.common)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.blame)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv.org_jp_gr_xml.sax)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.json)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.util.graph.selector)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.conn)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.revwalk.filter)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.fsck)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.conn)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.nexus.git.utils.repository)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.core.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.lz4)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.datatype.xsd.ngimpl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom.ls)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.datatype.jsr310.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.text)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.api)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.representer)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.util.time)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.tuple)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.bind)
0; version=2.3.3
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.logging)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.resolution)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2.io)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv.org_isorelax.verifier.impl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.util.graph)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.zip)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.activation)
4; version=1.1.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.relax)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.harmony.unpack200.bytecode.forms)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax.helpers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.component)
306; version=*********
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.ser.impl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.lzw)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.io.comparator)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.nexus.git.utils)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.storage.io)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.util.mixin)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.jsontype.impl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.library.model)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.archivers.cpio)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.serializer)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.revwalk)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.archivers.ar)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.google.gson)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.jsontype)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.exception)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.xpath)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.nexus.git.utils.api)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.converters.collections)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.util.serializer)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.fnmatch)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.filter)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.transfer)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.archivers.arj)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.relaxns.grammar.trex)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.key.pbe.console)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.util.graph.manager)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.fs.sl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.ietf.jgss)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.antlr.v4.runtime)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.converters.enums)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.type)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.github.packageurl)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.trex.ng)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.datatype.xsd.datetime)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.security.auth)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.exc)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.external.biz.base64Coder)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.deser.std)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.tools.manifests.gradle.model)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.event)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.ext)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.internal.transport.parser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.semver4j.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.harmony.pack200)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.storage.listedlicense)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.socket.spi)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.lang3.function)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.converters.javabean)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.cookie)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.dataformat.xml)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.key.spi)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.jsonschema)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.model.io)
309; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.entity)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.dtd)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.hash.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv.org_isorelax.verifier)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.datatype.jsr310)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.key)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.sonatype.aether.installation)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.io)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.util.internal)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.treewalk)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.relaxns.grammar.relax)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.yaml.snakeyaml.tokens)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.slf4j)
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.policy)
306; version=*********
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.file.nuget.model)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.brotli)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.dataformat.xml.deser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.dom)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.xz)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.core.async)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.grammar.dtd)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.verifier)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.patch)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.filechooser)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.codehaus.stax2.osgi)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=sun.misc)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.eclipse.jgit.errors)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.tools.manifests.gradle.parser)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.compress.compressors.deflate)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.spdx.library)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.verifier.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.config)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.util.deserializer)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.google.gson.internal.bind.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.util)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.apache.commons.io.filefilter)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.thoughtworks.xstream.io)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.de.schlichtherle.truezip.io)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.tukaani.xz.simple)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.reader.relax.core)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.fasterxml.jackson.databind.json)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.com.ctc.wstx.shaded.msv_core.verifier.identity)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.stream)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.cyclonedx.model.formulation)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=zz.org.objectweb.asm.v60.v60)
303; version=0.0.0
