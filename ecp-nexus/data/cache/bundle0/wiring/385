osgi.wiring.package; (&(osgi.wiring.package=javax.validation)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.type)(version>=2.17.0))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.attributes)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.annotation)(version>=2.17.0))
167; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.callsite)(version>=3.0.0))
122; version=3.0.19
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.purge)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.core)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.httpbridge)(version>=3.70.0))
257; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.matchers.token)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.validation.constraints)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.group)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.handlers)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.typehandling)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet.http)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rest.api.model)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.hash)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.cleanup.config)(version>=3.70.0))
294; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.payloads)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.routing)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.types)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.io)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=groovy.lang)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.transaction)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.security)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.http)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.annotation)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.proxy)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.matchers.logic)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.api)(version>=3.70.0))
170; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.search)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.browse)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.storage)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.collect)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.util)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.transaction)(version>=3.70.0))
159; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.config)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.matchers)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime)(version>=3.0.0))
129; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.upgrade)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=io.swagger.annotations)(version>=1.6.0))
188; version=1.6.11
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rest.api)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.httpclient)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.cache)(version>=3.70.0))
276; version=3.70.4
