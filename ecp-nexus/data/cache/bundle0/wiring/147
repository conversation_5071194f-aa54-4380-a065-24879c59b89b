osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.ser.std)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=javax.crypto.spec)
0; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.core.type)
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.annotation)
167; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.core)
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=javax.crypto)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.module)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.deser.std)
166; version=2.17.0
