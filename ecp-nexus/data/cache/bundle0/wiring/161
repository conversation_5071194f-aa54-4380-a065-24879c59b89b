osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.template)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang)(version>=2.6.0))
138; version=2.6.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.classmate)(version>=1.3.0)(!(version>=2.0.0)))
225; version=1.5.1
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.matcher)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=javax.script)(version>=0.0.0))
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time)(version>=2.0.0)(!(version>=3.0.0)))
121; version=2.10.6
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.transform.stream)(version>=0.0.0))
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu.inject)
103; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=javax.inject)
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.aopalliance.intercept)(version>=1.0.0))
111; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.xml.sax)(version>=0.0.0))
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.classmate.members)(version>=1.3.0)(!(version>=2.0.0)))
225; version=1.5.1
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.transform)(version>=0.0.0))
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.stream)(version>=0.0.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.namespace)(version>=0.0.0))
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.binder)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.validation)(version>=0.0.0))
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.jboss.logging)(version>=3.1.0)(!(version>=4.0.0)))
21; version=3.4.3.Final
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.stream.events)(version>=0.0.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=org.codehaus.groovy.jsr223)
127; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=javax.script)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.validator.routines)(version>=1.7.0))
162; version=1.7.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.text)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu)
103; version=0.0.0
