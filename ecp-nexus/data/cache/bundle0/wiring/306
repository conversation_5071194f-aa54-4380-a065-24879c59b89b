osgi.wiring.package; (osgi.wiring.package=javax.xml.validation)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.security.auth)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.conn.routing)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.crypto)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.event)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.conn)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.protocol)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.net.ssl)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.auth)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.tree)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.conn)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.stream.events)
0; version=1.2.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=javax.inject)
9; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.cookie)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.slf4j.spi)
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom.ls)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.stream.util)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=javax.crypto.spec)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation.meta)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.util)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.anon)
307; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.model.io)
309; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=sun.nio.ch)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.auth)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.entity)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.bind)
0; version=2.3.3
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.config)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.ssl)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.activation)
4; version=1.1.0
osgi.wiring.package; (osgi.wiring.package=javax.management)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.stream)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax.helpers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.client)
307; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.slf4j)
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=javax.swing.plaf)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.namespace)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.config)
307; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.dom)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.annotation)(version>=2.14.0)(!(version>=3.0.0)))
167; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax.ext)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.filechooser)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=sun.misc)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.model)
308; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.validation)
75; version=2.0.2
osgi.wiring.package; (osgi.wiring.package=javax.swing.text)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.utils)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.xpath)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.config)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.methods)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.datatype)
0; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8.0))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=javax.swing)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.protocol)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.cookie)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.parsers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.ietf.jgss)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.stream)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.sax)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.border)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.util)
307; version=0.0.0
