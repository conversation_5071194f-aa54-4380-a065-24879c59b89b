osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0)(!(version>=2.2.0)))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.module.jaxb)(version>=2.17.0)(!(version>=3.0.0)))
218; version=2.17.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.dataformat.smile)(version>=2.17.0)(!(version>=3.0.0)))
285; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.cfg)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.util)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.introspect)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.core)(version>=2.0.0)(!(version>=2.2.0)))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.jaxrs.cfg)(version>=2.17.0)(!(version>=3.0.0)))
217; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.jaxrs.base)(version>=2.17.0)(!(version>=3.0.0)))
217; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.ext)(version>=2.0.0)(!(version>=2.2.0)))
221; version=2.0.1
