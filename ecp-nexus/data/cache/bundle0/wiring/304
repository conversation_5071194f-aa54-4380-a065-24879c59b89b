osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.ldap.persist)(version>=3.70.0))
299; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.httpclient.config)(version>=3.70.0))
180; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.jaxrs.smile)(version>=2.17.0))
313; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.event)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.clm.migration)(version>=3.70.0))
305; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.util.concurrent)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.authc.apikey)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.repository.nuget.odata)(version>=3.70.0))
318; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.reflect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.repository.nuget.datastore)(version>=3.70.0))
318; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.ssl)(version>=3.70.0))
203; version=3.70.4
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.manager)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=groovy.time)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.i18n)(version>=2.3.0))
165; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.apache.karaf.shell.support.completers)(version>=4.3.0))
62; version=4.3.9
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.sequence)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.time)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.subject)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.httpbridge.legacy)(version>=3.70.0))
257; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.typehandling)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.hash)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rapture)(version>=3.70.0))
235; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.io)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.karaf.shell.api.console)(version>=4.3.0))
62; version=4.3.9
osgi.wiring.package; (&(osgi.wiring.package=groovy.lang)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.extdirect)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.security)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.sonatype.licensing.feature)
384; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.stateguard)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.role)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rubygems.datastore)(version>=3.70.0))
362; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core)(version>=2.17.0))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.repository.npm)(version>=3.70.0))
348; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.httpclient)(version>=3.70.0))
180; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.clm.migration.orient)(version>=3.70.0))
305; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.maven)(version>=3.70.0))
269; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.api)(version>=3.70.0))
170; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.entity)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.facet)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.property)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.client)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.selector)(version>=3.70.0))
289; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.search.index)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.storage)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.client)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.authz)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.datastore.api)(version>=3.70.0))
141; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.content.maven.store)(version>=3.70.0))
269; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.collect)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.ldap.persist.entity)(version>=3.70.0))
299; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.protocol)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.transaction)(version>=3.70.0))
159; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.config)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.codehaus.plexus.util)
205; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.eventbus)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rubygems.orient)(version>=3.70.0))
362; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.repository.npm.orient)(version>=3.70.0))
348; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.httpclient)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.cache)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.privilege)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.subject)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rubygems)(version>=3.70.0))
362; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.maven)(version>=3.70.0))
269; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.attributes)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.config)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.anonymous)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rest.client)(version>=3.70.0))
312; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.callsite)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.plugins.healthcheck.service)(version>=3.70.0))
305; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.formfields)(version>=3.70.0))
179; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.browse.node)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.content.raw)(version>=3.70.0))
275; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.core)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rubygems.marshal)(version>=3.70.0))
362; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang3)(version>=3.13.0))
172; version=3.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.wrappers)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.softwarementors.extjs.djn.config.annotations)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.user)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.lifecycle)(version>=2.3.0))
120; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.payloads)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.repository.nuget)(version>=3.70.0))
318; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.upgrade.plan)(version>=3.70.0))
204; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.graph)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.store)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.text)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.annotation)(version>=4.1.0))
169; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.raw)(version>=3.70.0))
275; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.transaction)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.hash)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.annotation)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.capability)(version>=3.70.0))
174; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.scheduling)(version>=3.70.0))
181; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.search)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.repository.nuget.security)(version>=3.70.0))
318; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.email)(version>=3.70.0))
176; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.fluent)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.log)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.methods)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.repository.content.npm)(version>=3.70.0))
348; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.repository.nuget.orient)(version>=3.70.0))
318; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.util)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.search)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.upgrade)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.content.maven)(version>=3.70.0))
269; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.thread)(version>=3.70.0))
187; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.clm.migration.datastore)(version>=3.70.0))
305; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.realm)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time)(version>=2.10.0))
121; version=2.10.6
