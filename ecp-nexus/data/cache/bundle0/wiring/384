osgi.wiring.package; (osgi.wiring.package=org.aopalliance.intercept)
111; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=org.codehaus.plexus.util.xml.pull)
205; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.google.common.collect)
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=org.apache.commons.codec.binary)
210; version=1.16.0
osgi.wiring.package; (osgi.wiring.package=org.codehaus.plexus.util.io)
205; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.crypto.spec)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.security.auth.x500)
0; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.6))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=javax.crypto)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.google.inject)
89; version=1.4.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.filechooser)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.google.inject.matcher)
89; version=1.4.0
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu.inject)
103; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.codehaus.plexus.util)
205; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=javax.inject)
9; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream)
310; version=1.4.20
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.annotations)
310; version=1.4.20
osgi.wiring.package; (osgi.wiring.package=javax.enterprise.inject)
113; version=1.1.0
osgi.wiring.package; (osgi.wiring.package=org.slf4j)
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu)
103; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.google.common.base)
11; version=32.1.1
