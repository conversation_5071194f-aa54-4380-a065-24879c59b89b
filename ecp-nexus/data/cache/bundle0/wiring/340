osgi.wiring.package; (osgi.wiring.package=org.spdx.storage)
339; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.spdx.library.referencetype)
339; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.spdx.library.model.enumerations)
339; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.dataformat.yaml)
231; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.node)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=org.spdx.library)
339; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.spdx.storage.simple)
339; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=org.spdx.library.model.license)
339; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.dataformat.xml.ser)
336; version=2.14.2
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.core)
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.dataformat.xml)
336; version=2.14.2
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=org.slf4j)
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=org.json)
341; version=20231013.0.0
osgi.wiring.package; (osgi.wiring.package=org.spdx.library.model)
339; version=0.0.0
