osgi.wiring.package; (&(osgi.wiring.package=org.osgi.framework.launch)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.jetty.server.handler)(version>=9.4.0))
6; version=9.4.53
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.jetty.server)(version>=9.4.0))
6; version=9.4.53
osgi.wiring.package; (osgi.wiring.package=java.nio.file)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.osgi.util.tracker)(version>=1.5.0))
0; version=1.5.2
osgi.wiring.package; (osgi.wiring.package=java.security)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.jetty.io)(version>=9.4.0))
31; version=9.4.53
osgi.wiring.package; (osgi.wiring.package=java.nio.charset)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.plexus.interpolation)(version>=1.24.0))
70; version=1.24.0
osgi.wiring.package; (osgi.wiring.package=java.lang)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.io)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics)(version>=4.1.0))
15; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.jetty.util.component)(version>=9.4.0))
60; version=9.4.53
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=ch.qos.logback.classic)(version>=1.2.0))
39; version=1.2.13
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.jetty.util.ssl)(version>=9.4.0))
60; version=9.4.53
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.jetty.http)(version>=9.4.0))
3; version=9.4.53
osgi.wiring.package; (osgi.wiring.package=java.lang.invoke)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.karaf.features)(version>=4.3.0))
48; version=4.3.9
osgi.wiring.package; (osgi.wiring.package=uk.org.lidalia.sysoutslf4j.context)
72; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.jetty.xml)(version>=9.4.0))
32; version=9.4.53
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.jetty9)(version>=4.1.0))
19; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.jetty.util.resource)(version>=9.4.0))
60; version=9.4.53
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j.impl)(version>=1.7.0))
39; version=1.7.32
osgi.wiring.package; (osgi.wiring.package=java.util.concurrent)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.util.prefs)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.nio.file.attribute)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=java.util.function)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.net)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.osgi.framework)(version>=1.9.0))
0; version=1.9.0
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet.http)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=java.util.stream)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=java.util.logging)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.util.concurrent.atomic)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.util)
0; version=0.0.0
