osgi.wiring.package; (osgi.wiring.package=javax.xml.namespace)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.management)(version>=1.1.0)(!(version>=2.0.0)))
151; version=1.1.1
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.dom)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.validation)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.event)(version>=1.1.0)(!(version>=2.0.0)))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.bind.annotation)(version>=2.2.0)(!(version>=3.0.0)))
0; version=2.3.3
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.expiry)(version>=1.1.0)(!(version>=2.0.0)))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.configuration)(version>=1.1.0)(!(version>=2.0.0)))
151; version=1.1.1
osgi.extender; (&(osgi.extender=osgi.component)(version>=1.3.0)(!(version>=2.0.0)))
56; version=1.4.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=javax.cache)(version>=1.1.0)(!(version>=2.0.0)))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.bind.annotation.adapters)(version>=2.2.0)(!(version>=3.0.0)))
0; version=2.3.3
osgi.wiring.package; (osgi.wiring.package=javax.management.openmbean)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.datatype)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.osgi.framework)
0; version=1.9.0
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j.event)(version>=1.7.0)(!(version>=2.0.0)))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.bind)(version>=2.2.0)(!(version>=3.0.0)))
0; version=2.3.3
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.integration)(version>=1.1.0)(!(version>=2.0.0)))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0)(!(version>=2.0.0)))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.spi)(version>=1.1.0)(!(version>=2.0.0)))
151; version=1.1.1
osgi.wiring.package; (osgi.wiring.package=javax.management)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.parsers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.stream)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.processor)(version>=1.1.0)(!(version>=2.0.0)))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.bind.helpers)(version>=2.2.0)(!(version>=3.0.0)))
0; version=2.3.3
