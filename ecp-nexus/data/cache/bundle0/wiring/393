osgi.wiring.package; (osgi.wiring.package=com.google.common.cache)
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=org.joda.time)
121; version=2.10.6
osgi.wiring.package; (osgi.wiring.package=com.google.common.collect)
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=org.apache.http.util)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.google.gson)
209; version=2.9.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.annotation)
167; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.entity)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.google.common.reflect)
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=org.sonatype.goodies.packageurl.jackson)
279; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.auth)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.google.common.io)
11; version=32.1.1
osgi.ee; (&(osgi.ee=JavaSE)(version=1.7))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=com.google.common.hash)
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=org.sonatype.goodies.packageurl)
279; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.core)
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http)
77; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=org.slf4j)
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=org.sonatype.ossindex.service.api.componentreport)
394; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.methods)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.google.common.base)
11; version=32.1.1
