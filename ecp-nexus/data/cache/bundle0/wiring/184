osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang)(version>=2.6.0))
138; version=2.6.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.module)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.type)(version>=2.17.0))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.stateguard)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core)(version>=2.17.0))
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform)
0; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.io)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.datatype.jdk8)(version>=2.17.0))
186; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.datatype.joda)(version>=2.17.0))
137; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.log)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.type)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.deser.std)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=javax.xml.parsers)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.stream)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.io)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.sax)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.primitives)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.io)(version>=1.4.0))
28; version=2.15.1
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.datatype.jsr310)(version>=2.17.0))
185; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.ser.std)(version>=2.17.0))
166; version=2.17.0
