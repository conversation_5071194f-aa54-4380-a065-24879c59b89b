osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.subject)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang)(version>=2.6.0))
138; version=2.6.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.event)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.config)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.anonymous)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.clm)(version>=3.70.0))
305; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.db.document)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.annotation)(version>=2.17.0))
167; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.node)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.plugins.healthcheck.service)(version>=3.70.0))
305; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.scheduling.schedule)(version>=3.70.0))
181; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.datastore)(version>=3.70.0))
201; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.formfields)(version>=3.70.0))
179; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.application.scan)(version>=3.70.0))
278; version=3.70.4
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang3.tuple)(version>=3.13.0))
172; version=3.13.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.io)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.manager)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.core)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang3)(version>=3.13.0))
172; version=3.13.0
osgi.wiring.package; (osgi.wiring.package=org.codehaus.plexus.util.xml.pull)
205; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.util)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.softwarementors.extjs.djn.config.annotations)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.user)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.model.io)
309; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.config.memory)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.entity)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.validation.constraints)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=org.apache.maven.artifact.repository.metadata)
271; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rest)(version>=3.70.0))
214; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.collections)(version>=3.2.0))
158; version=3.2.2
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.client.utils)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.brain.client)
303; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.component)
306; version=1.57.0.01
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.io)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.annotation)(version>=4.1.0))
169; version=4.1.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model.policy)
306; version=1.57.0.01
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.io)(version>=1.4.0))
28; version=2.15.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.extdirect)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.transaction)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.security)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=com.sonatype.clm.dto.model)
306; version=1.57.0.01
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.stateguard)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.rm.rest)
303; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.annotation)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.db)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.scheduling)(version>=3.70.0))
181; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.api)(version>=3.70.0))
170; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.entity)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.facet)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.scan.model)
308; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.selector)(version>=3.70.0))
289; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.fluent)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.coreui)(version>=3.70.0))
256; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.storage)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.log)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.methods)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.maven.artifact.repository.metadata.io.xpp3)
271; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.security)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.datastore.api)(version>=3.70.0))
141; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.event.asset)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.collect)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.transaction)(version>=3.70.0))
159; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.config)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.search)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.eventbus)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.upgrade)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.clm.vulnerability.service)(version>=3.70.0))
305; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.thread)(version>=3.70.0))
187; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.ibatis.annotations)(version>=3.5.0))
239; version=3.5.15
osgi.wiring.package; (&(osgi.wiring.package=io.swagger.annotations)(version>=1.6.0))
188; version=1.6.11
osgi.wiring.package; (osgi.wiring.package=org.apache.http)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content)(version>=3.70.0))
292; version=3.70.4
