osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0)(!(version>=2.0.0)))
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=javax.naming.ldap)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.naming)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.sql)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.crypto.spec)
0; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=javax.naming.directory)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.beanutils)(version>=1.9.0)(!(version>=2.0.0)))
157; version=1.9.4
osgi.wiring.package; (osgi.wiring.package=javax.crypto)
0; version=0.0.0
