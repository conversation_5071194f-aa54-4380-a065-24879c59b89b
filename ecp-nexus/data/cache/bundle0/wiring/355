osgi.wiring.package; (osgi.wiring.package=com.sonatype.insight.json.store)
303; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.event)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.graph)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.db.document)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.annotation)(version>=2.17.0))
167; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.node)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.rest)(version>=3.70.0))
278; version=3.70.4
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.manager)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.i18n)(version>=2.3.0))
165; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.siesta)(version>=3.70.0))
213; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.capability.condition)(version>=3.70.0))
174; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.util)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.record.impl)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.metadata.schema)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rest)(version>=3.70.0))
214; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view.handlers)(version>=3.70.0))
280; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet.http)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.hash)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.group)(version>=3.70.0))
142; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rapture)(version>=3.70.0))
235; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.types)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.scheduling.spi)(version>=3.70.0))
181; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.servlet)(version>=1.4.0))
101; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu)
103; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.security)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.sonatype.licensing.feature)
384; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.jmx.reflect)(version>=3.70.0))
149; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.stateguard)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.role)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core)(version>=2.17.0))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.entity)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.cleanup.storage)(version>=3.70.0))
294; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.httpclient)(version>=3.70.0))
180; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.api)(version>=3.70.0))
170; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics)(version>=4.1.0))
15; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.entity)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.facet)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.property)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.selector)(version>=3.70.0))
289; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.storage)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.client)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.authz)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.datastore.api)(version>=3.70.0))
141; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.script)(version>=3.70.0))
297; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.annotation)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.authc)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.collect)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.protocol)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.transaction)(version>=3.70.0))
159; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.config)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.message)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.eventbus)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.licensing.ext)(version>=3.70.0))
383; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.search.event)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.command)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.privilege)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.event.component)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.subject)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang)(version>=2.6.0))
138; version=2.6.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.anonymous)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.clm)(version>=3.70.0))
305; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.analytics)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.scheduling.schedule)(version>=3.70.0))
181; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.formfields)(version>=3.70.0))
179; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.index)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (osgi.wiring.package=javax.inject)
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.core)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang3)(version>=3.13.0))
172; version=3.13.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.sql)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.transaction)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=javax.sql)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.user)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.entity)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.validation.constraints)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.db.record)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (osgi.wiring.package=org.sonatype.licensing.product)
384; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.lifecycle)(version>=2.3.0))
120; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.graph)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.store)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.text)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.annotation)(version>=4.1.0))
169; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.io)(version>=1.4.0))
28; version=2.15.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.logging.task)(version>=3.70.0))
29; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.servlet)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.hash)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.licensing.ext.capability)(version>=3.70.0))
383; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.annotation)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.proxy)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.db)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.capability)(version>=3.70.0))
174; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.scheduling)(version>=3.70.0))
181; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu.inject)
103; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository)(version>=3.70.0))
277; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.utils)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.email)(version>=3.70.0))
176; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.fluent)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.scheduling.events)(version>=3.70.0))
181; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.methods)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.view)(version>=3.70.0))
276; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.upgrade)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.sql.query)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.apache.ibatis.annotations)(version>=3.5.0))
239; version=3.5.15
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.realm)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time)(version>=2.10.0))
121; version=2.10.6
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.search.query)(version>=3.70.0))
278; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time.format)(version>=2.10.0))
121; version=2.10.6
