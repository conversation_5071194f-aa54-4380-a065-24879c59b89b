osgi.wiring.package; (&(osgi.wiring.package=javax.validation)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.crypto.maven)(version>=3.70.0))
153; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.subject.support)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.subject)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang)(version>=2.6.0))
138; version=2.6.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.cache)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.event)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.config)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authc.pam)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (osgi.wiring.package=com.auth0.jwt.interfaces)
147; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.crypto)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.name)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.annotation)(version>=2.17.0))
167; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.callsite)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.cache)(version>=3.70.0))
150; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.formfields)(version>=3.70.0))
179; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.audit)(version>=3.70.0))
148; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.realm)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.session)(version>=1.13.0))
154; version=1.13.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=com.auth0.jwt.algorithms)
147; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.io)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.core)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.cache)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.i18n)(version>=2.3.0))
165; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.session.mgt)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.distributed.event.service.api.common)(version>=3.70.0))
207; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.mgt)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.session.mgt.eis)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.permission)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.filter.authz)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.filter.mgt)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=javax.validation.constraints)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rest)(version>=3.70.0))
214; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.binder)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.lifecycle)(version>=2.3.0))
120; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=javax.cache)(version>=1.1.0))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.typehandling)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet.http)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (osgi.wiring.package=com.auth0.jwt)
147; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=groovy.lang)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.text)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.util)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu)
103; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.template)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.subject)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.servlet)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.script)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.stateguard)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.session.mgt)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.subject.support)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.annotation)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (osgi.wiring.package=com.auth0.jwt.exceptions)
147; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.distributed.event.service.api)(version>=3.70.0))
207; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu.inject)
103; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.util)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.guice.web)(version>=1.13.0))
88; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.filter.authc)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.expiry)(version>=1.1.0))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authc.credential)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.config)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authc)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.eventbus)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime)(version>=3.0.0))
129; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=javax.cache.configuration)(version>=1.1.0))
151; version=1.1.1
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.crypto.hash)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=io.swagger.annotations)(version>=1.6.0))
188; version=1.6.11
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.web.mgt)(version>=1.13.0))
155; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.crypto.hash.format)(version>=1.13.0))
154; version=1.13.0
