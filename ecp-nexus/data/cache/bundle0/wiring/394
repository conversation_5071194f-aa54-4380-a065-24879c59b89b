osgi.wiring.package; (osgi.wiring.package=javax.annotation)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=javax.ws.rs.core)
221; version=2.0.1
osgi.wiring.package; (osgi.wiring.package=com.google.common.collect)
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=javax.ws.rs)
221; version=2.0.1
osgi.ee; (&(osgi.ee=JavaSE)(version=1.7))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.annotation)
167; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=org.sonatype.goodies.packageurl.jaxb)
279; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=io.swagger.annotations)
188; version=1.6.11
osgi.wiring.package; (osgi.wiring.package=org.sonatype.goodies.packageurl)
279; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.slf4j)
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=javax.xml.bind.annotation.adapters)
0; version=2.3.3
osgi.wiring.package; (osgi.wiring.package=javax.xml.bind.annotation)
0; version=2.3.3
osgi.wiring.package; (osgi.wiring.package=com.google.common.base)
11; version=32.1.1
