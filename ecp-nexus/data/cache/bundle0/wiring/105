osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.aether.resolution)(version>=1.1.0)(!(version>=2.0.0)))
106; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.aether.version)(version>=1.1.0)(!(version>=2.0.0)))
106; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.aether.transfer)(version>=1.1.0)(!(version>=2.0.0)))
106; version=1.1.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.5.0))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.aether.graph)(version>=1.1.0)(!(version>=2.0.0)))
106; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.aether)(version>=1.1.0)(!(version>=2.0.0)))
106; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.aether.artifact)(version>=1.1.0)(!(version>=2.0.0)))
106; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.aether.repository)(version>=1.1.0)(!(version>=2.0.0)))
106; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.aether.metadata)(version>=1.1.0)(!(version>=2.0.0)))
106; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.eclipse.aether.collection)(version>=1.1.0)(!(version>=2.0.0)))
106; version=1.1.0
osgi.wiring.package; (osgi.wiring.package=javax.net.ssl)
0; version=0.0.0
