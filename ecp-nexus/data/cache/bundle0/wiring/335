osgi.wiring.package; (osgi.wiring.package=javax.xml.namespace)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.commons.codec.binary)
210; version=1.16.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.introspect)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.validation)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.type)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.jsontype)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.dataformat.xml.deser)
336; version=2.14.2
osgi.wiring.package; (osgi.wiring.package=com.networknt.schema)
337; version=1.0.77
osgi.wiring.package; (osgi.wiring.package=org.apache.commons.codec.digest)
210; version=1.16.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.ser.std)
166; version=2.17.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=org.apache.commons.io)
28; version=2.15.1
osgi.wiring.package; (osgi.wiring.package=com.github.packageurl)
342; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.dataformat.xml.ser)
336; version=2.14.2
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.core.type)
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.dataformat.xml)
336; version=2.14.2
osgi.wiring.package; (osgi.wiring.package=javax.xml.xpath)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.deser.std)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=org.apache.commons.lang3.math)
172; version=3.13.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.core.util)
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.annotation)
167; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.node)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.annotation)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.stream)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.parsers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.dataformat.xml.annotation)
336; version=2.14.2
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.core)
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.stream)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.dataformat.xml.util)
336; version=2.14.2
osgi.wiring.package; (osgi.wiring.package=org.xml.sax)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind)
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.fasterxml.jackson.databind.module)
166; version=2.17.0
