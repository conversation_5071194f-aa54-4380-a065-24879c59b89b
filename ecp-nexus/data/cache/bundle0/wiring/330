osgi.wiring.package; (&(osgi.wiring.package=groovyjarjarantlr.collections)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.io.xml)
310; version=1.4.20
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=groovy.io)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.apache.groovy.internal.util)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=javax.crypto)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.node)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.classgen.asm.indy)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.apache.groovy.parser.antlr4)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.vmplugin.v8)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.ast.expr)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=org.fusesource.jansi)
26; version=2.4.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.plexus.interpolation.fixed)(version>=1.24.0))
70; version=1.24.0
osgi.wiring.package; (osgi.wiring.package=java.security.spec)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.macro.matcher)(version>=3.0.0))
128; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform.stc)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.jce.provider)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (osgi.wiring.package=net.jcip.annotations)
224; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.rest)(version>=3.70.0))
142; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=javax.annotation.meta)
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.transform.trait)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.cert)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (&(osgi.wiring.package=groovyjarjarasm.asm.util)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.quota)(version>=3.70.0))
142; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.dgmimpl)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.awt.event)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.powerassert)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.yaml.snakeyaml.events)(version>=2.2.0))
233; version=2.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.typehandling)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet.http)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (osgi.wiring.package=java.security.interfaces)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.thread)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=groovy.lang)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.apache.groovy.ast.tools)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.vmplugin.v6)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=org.apache.log4j)
21; version=1.2.19
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.operator)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (osgi.wiring.package=com.sun.net.httpserver)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax.ext)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.plexus.interpolation)(version>=1.24.0))
70; version=1.24.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.entity)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sun.jna.win32)(version>=5.11.0))
25; version=5.11.0
osgi.wiring.package; (&(osgi.wiring.package=com.sun.jna)(version>=5.11.0))
25; version=5.11.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.ast.stmt)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.operator.jcajce)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.macro.runtime)(version>=3.0.0))
128; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (osgi.wiring.package=java.time.temporal)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.metaclass)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection.stdclasses)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.nio.channels.spi)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.yaml.snakeyaml.parser)(version>=2.2.0))
233; version=2.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.control.messages)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.osgi.framework)(version>=1.9.0))
0; version=1.9.0
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.cert.jcajce)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=groovy.lang.groovydoc)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.util.stream)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.time.chrono)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.ast.tools)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.util.concurrent.atomic)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.sax)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.transform.sc.transformers)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=javax.swing.border)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform.options)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=javax.validation)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=org.osgi.util.tracker)(version>=1.5.0))
0; version=1.5.2
osgi.wiring.package; (&(osgi.wiring.package=org.apache.groovy.util.concurrent.concurrentlinkedhashmap)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.yaml.snakeyaml.reader)(version>=2.2.0))
233; version=2.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=javax.net.ssl)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.yaml.snakeyaml.resolver)(version>=2.2.0))
233; version=2.2.0
osgi.wiring.package; (osgi.wiring.package=java.text)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.core)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.cache)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=javax.tools)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.security.cert)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.lang.management)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.transaction)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.ast)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=javax.crypto.spec)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.asn1.x509)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (&(osgi.wiring.package=org.apache.groovy.io)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=com.github.luben.zstd)
268; version=1.4.8.7
osgi.wiring.package; (osgi.wiring.package=java.awt)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.tools.javac)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.openssl)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.classgen.asm.sc)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j.spi)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.annotation)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.asn1)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (osgi.wiring.package=java.io)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.scheduling)(version>=3.70.0))
181; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=groovy.ui)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.yaml.snakeyaml.nodes)(version>=2.2.0))
233; version=2.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.yaml.snakeyaml)(version>=2.2.0))
233; version=2.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.memoize)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.control.io)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.groovy.util.concurrent)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.log)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=javax.xml.datatype)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.time.zone)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.nio.file.attribute)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.util)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=groovyjarjarasm.asm.signature)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.lang.reflect)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.upgrade)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=javax.xml.parsers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.nio)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.time)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.macro.methods)(version>=3.0.0))
128; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.apache.ibatis.annotations)(version>=3.5.0))
239; version=3.5.15
osgi.wiring.package; (&(osgi.wiring.package=groovyjarjarpicocli)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=io.swagger.annotations)(version>=1.6.0))
188; version=1.6.11
osgi.wiring.package; (&(osgi.wiring.package=groovy.grape)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=org.xml.sax)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.ning.compress)(version>=1.1.0))
288; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time.format)(version>=2.10.0))
121; version=2.10.6
osgi.wiring.package; (osgi.wiring.package=java.security)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection.v7)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=javax.security.auth.x500)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.db.document)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.control.customizers)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.ast.builder)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.apache.groovy.util)(version>=3.0.0))
122; version=3.0.19
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom.ls)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.i18n)(version>=2.3.0))
165; version=2.3.8
osgi.wiring.package; (osgi.wiring.package=javax.naming)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.record.impl)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (osgi.wiring.package=java.util.concurrent)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.util.jar)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.classgen.asm.util)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.antlr.treewalker)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.util.prefs)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.entity.action)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=java.util.function)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.metadata.schema)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rest)(version>=3.70.0))
214; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.metrics)(version>=3.70.0))
142; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.antlr)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.transform.sc)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rapture)(version>=3.70.0))
235; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.xml.sax.helpers)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.ast.decompiled)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.syntax)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.tools)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu)
103; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.namespace)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.time.format)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.sonatype.licensing.feature)
384; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.crypto.params)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.stateguard)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.entity)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=groovy.util)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.macro.matcher.internal)(version>=3.0.0))
128; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=groovyjarjarantlr.collections.impl)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.lang)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.api)(version>=3.70.0))
170; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.property)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=java.nio.channels)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=groovy.xml)(version>=3.0.0))
132; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.yaml.snakeyaml.emitter)(version>=2.2.0))
233; version=2.2.0
osgi.wiring.package; (osgi.wiring.package=java.lang.ref)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (osgi.wiring.package=java.lang.invoke)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.xpath)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.tools.shell)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.datastore.api)(version>=3.70.0))
141; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.collect)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream.io)
310; version=1.4.20
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.plexus.interpolation.util)(version>=1.24.0))
70; version=1.24.0
osgi.wiring.package; (&(osgi.wiring.package=com.ning.compress.lzf)(version>=1.1.0))
288; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=groovyjarjarasm.asm)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.net)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.classgen)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.util.zip)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.transaction)(version>=3.70.0))
159; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore)(version>=3.70.0))
142; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.asn1.x500)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.transform.stc)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=javax.naming.directory)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=javax.script)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.sql)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.logging.log4j.message)
21; version=2.18.0
osgi.wiring.package; (osgi.wiring.package=java.util)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.vmplugin)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.reactivestreams)(version>=1.0.0))
223; version=1.0.3
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang)(version>=2.6.0))
138; version=2.6.0
osgi.wiring.package; (osgi.wiring.package=java.util.concurrent.locks)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.control)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.logging)(version>=1.2.0))
21; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=java.nio.charset)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.callsite)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.formfields)(version>=3.70.0))
179; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=java.beans)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.index)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.yaml.snakeyaml.error)(version>=2.2.0))
233; version=2.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.wrappers)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.antlr.java)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.ning.compress.lzf.util)(version>=1.1.0))
288; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.bouncycastle.openssl.jcajce)(version>=1.0.0))
0; version=1.78.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=java.math)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=groovyjarjarantlr.debug.misc)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.security.cert)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.dgmimpl.arrays)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform.builder)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=javax.validation.constraints)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=java.util.regex)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=groovyjarjarantlr)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.classgen.asm)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.antlr.parser)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.plexus.interpolation.os)(version>=1.24.0))
70; version=1.24.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.groovy.plugin)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=java.lang.annotation)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j.helpers)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.repository.content.store)(version>=3.70.0))
292; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=java.nio.file)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.hash)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.dom)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.m12n)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=groovy.cli)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=javax.net)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.transform)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.vmplugin.v5)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=sun.misc)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.classgen.asm.indy.sc)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection.android)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=javax.swing.text)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.logging.log4j.spi)
21; version=2.18.0
osgi.wiring.package; (osgi.wiring.package=org.apache.logging.log4j)
21; version=2.18.0
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.sun.jna.platform.win32)(version>=5.11.0))
248; version=5.11.0
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.plexus.interpolation.reflection)(version>=1.24.0))
70; version=1.24.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.blobstore.api.metrics)(version>=3.70.0))
170; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=com.thoughtworks.xstream)
310; version=1.4.20
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.tools.shell.util)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.thread)(version>=3.70.0))
187; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.groovy.lang.annotation)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.stream)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.util.logging)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time)(version>=2.10.0))
121; version=2.10.6
