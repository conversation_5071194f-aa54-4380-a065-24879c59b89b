osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.typehandling)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime)(version>=3.0.0))
122; version=3.0.19
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.softwarementors.extjs.djn.config.annotations)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.plugins.healthcheck.ui)(version>=3.70.0))
305; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=groovy.lang)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.callsite)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.plugins.healthcheck.service)(version>=3.70.0))
305; version=3.70.4
