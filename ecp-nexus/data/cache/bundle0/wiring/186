osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.util)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.ser.std)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.ser.impl)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.jsontype)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.ser)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.type)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.deser)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.cfg)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.util)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.io)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.deser.std)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.jsonFormatVisitors)(version>=2.17.0)(!(version>=3.0.0)))
166; version=2.17.0
