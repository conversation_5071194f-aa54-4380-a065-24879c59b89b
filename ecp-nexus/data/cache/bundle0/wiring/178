osgi.wiring.package; (osgi.wiring.package=javax.activation)
4; version=1.1.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.7))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=javax.security.auth.callback)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.crypto.spec)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.security.auth.x500)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.net)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.stream)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.sun.mail.util)(version>=1.6.0)(!(version>=2.0.0)))
226; version=1.6.5
osgi.wiring.package; (osgi.wiring.package=javax.crypto)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.security.sasl)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.net.ssl)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.sun.mail.iap)(version>=1.6.0)(!(version>=2.0.0)))
226; version=1.6.5
