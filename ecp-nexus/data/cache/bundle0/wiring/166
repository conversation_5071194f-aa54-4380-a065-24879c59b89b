osgi.wiring.package; (osgi.wiring.package=javax.xml.namespace)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.type)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.datatype)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.base)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.filter)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.dom)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom.bootstrap)
0; version=0.0.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (osgi.wiring.package=javax.xml.parsers)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.exc)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.util)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.json)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.stream)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.io)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core.format)(version>=2.17.0)(!(version>=3.0.0)))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.annotation)(version>=2.17.0)(!(version>=3.0.0)))
167; version=2.17.0
