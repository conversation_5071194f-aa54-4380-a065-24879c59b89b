osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind)(version>=2.9.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (osgi.wiring.package=com.papertrail.profiler)
195; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet.http)(version>=2.5.0)(!(version>=4.0.0)))
34; version=3.1.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.util)(version>=2.9.0)(!(version>=3.0.0)))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet)(version>=2.5.0)(!(version>=4.0.0)))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.jvm)(version>=4.1.0)(!(version>=5.0.0)))
194; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time)(version>=2.9.0)(!(version>=3.0.0)))
121; version=2.10.6
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.json)(version>=4.1.0)(!(version>=5.0.0)))
193; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.health)(version>=4.1.0)(!(version>=5.0.0)))
98; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics)(version>=4.1.0)(!(version>=5.0.0)))
15; version=4.1.0
