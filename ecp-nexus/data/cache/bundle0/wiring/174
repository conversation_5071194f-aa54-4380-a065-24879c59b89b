osgi.wiring.package; (&(osgi.wiring.package=javax.validation)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.template)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.event)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.annotation)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.db)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=javax.crypto)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.annotation)(version>=2.17.0))
167; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.formfields)(version>=3.70.0))
179; version=3.70.4
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.i18n)(version>=2.3.0))
165; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.crypto)(version>=3.70.0))
153; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.validation.groups)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.validation.constraints)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation.group)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rest)(version>=3.70.0))
214; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.lifecycle)(version>=2.3.0))
120; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.eventbus)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.text)(version>=3.70.0))
104; version=3.70.4
