osgi.wiring.package; (osgi.wiring.package=javax.xml.namespace)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.datatype)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.jdom2.input)
329; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xmlpull.v1)
317; version=1.1.4.c
osgi.wiring.package; (osgi.wiring.package=javax.security.auth)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=javax.xml.bind)(version>=2.3.0)(!(version>=3.0.0)))
0; version=2.3.3
osgi.wiring.package; (osgi.wiring.package=org.w3c.dom)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.activation)
4; version=1.1.0
osgi.wiring.package; (osgi.wiring.package=sun.misc)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.stream)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.parsers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xmlpull.mxp1)
317; version=1.1.4.c
osgi.wiring.package; (osgi.wiring.package=org.xml.sax.helpers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.stream)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.transform.sax)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.jdom2)
329; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.swing.plaf)
0; version=0.0.0
