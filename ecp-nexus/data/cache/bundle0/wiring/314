osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.mime)(version>=3.70.0))
144; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.subject)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.event)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.callsite)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.validation.constraint)(version>=3.70.0))
160; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.webresources)(version>=3.70.0))
143; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.formfields)(version>=3.70.0))
179; version=3.70.4
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.servlet)(version>=3.70.0))
182; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.io)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang3)(version>=3.13.0))
172; version=3.13.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.util)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.softwarementors.extjs.djn.config.annotations)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.user)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.validation.constraints)(version>=2.0.0))
75; version=2.0.2
osgi.wiring.package; (osgi.wiring.package=org.sonatype.licensing.product.util)
384; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime.typehandling)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet.http)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.net)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.io)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=groovy.lang)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.servlet)(version>=1.4.0))
101; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.text)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.annotation)(version>=4.1.0))
169; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.io)(version>=1.4.0))
28; version=2.15.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.extdirect)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.hash)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.stateguard)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.role)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.capability)(version>=3.70.0))
174; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.vulnerability)(version>=3.70.0))
369; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.utils)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.reflection)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.softwarementors.extjs.djn.servlet.ssm)(version>=3.70.0))
208; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.apache.http.impl.client)
78; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.apache.http.client.methods)
78; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.authz)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.eventbus)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.codehaus.groovy.runtime)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.upgrade)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.sonatype.nexus.licensing.ext)(version>=3.70.0))
383; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=groovy.transform)(version>=3.0.0))
122; version=3.0.19
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=org.apache.http)
77; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time)(version>=2.10.0))
121; version=2.10.6
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time.format)(version>=2.10.0))
121; version=2.10.6
