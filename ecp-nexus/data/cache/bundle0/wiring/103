osgi.wiring.package; (osgi.wiring.package=javax.servlet)
34; version=3.1.0
osgi.ee; (&(osgi.ee=JavaSE)(version=1.6.0))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.matcher)(version>=1.3.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=org.osgi.util.tracker)(version>=1.4.0))
0; version=1.5.2
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject)(version>=1.3.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.spi)(version>=1.3.0))
89; version=1.4.0
osgi.wiring.package; (osgi.wiring.package=javax.lang.model)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation.processing)
0; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=javax.annotation)
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=javax.lang.model.util)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.inject)
9; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=javax.enterprise.inject)
113; version=1.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.name)(version>=1.3.0))
89; version=1.4.0
osgi.wiring.package; (osgi.wiring.package=javax.lang.model.element)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.tools)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.slf4j)
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.osgi.framework)(version>=1.5.0))
0; version=1.9.0
osgi.wiring.package; (osgi.wiring.package=com.google.inject.servlet)
101; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.binder)(version>=1.3.0))
89; version=1.4.0
