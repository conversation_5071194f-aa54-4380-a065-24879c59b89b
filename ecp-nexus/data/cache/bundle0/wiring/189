osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.ssl.spi)(version>=3.70.0))
203; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=java.security)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.event)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.karaf.shell.support.table)(version>=4.3.0))
62; version=4.3.9
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.db.document)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.util.concurrent)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.authc.apikey)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.name)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.annotation)(version>=2.17.0))
167; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.node)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.storage.impl.local.paginated)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.node.orient)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.ssl)(version>=3.70.0))
203; version=3.70.4
osgi.ee; (&(osgi.ee=JavaSE)(version=1.8))
0; version=[1.0.0, 1.1.0, 1.2.0, 1.3.0, 1.4.0, 1.5.0, 1.6.0, 1.7.0, 1.8.0]
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time.base)(version>=2.10.0))
121; version=2.10.6
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.common.factory)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.crypto)(version>=3.70.0))
153; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.karaf.shell.support.completers)(version>=4.3.0))
62; version=4.3.9
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.record.impl)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j.impl)(version>=1.7.0))
39; version=1.7.32
osgi.wiring.package; (osgi.wiring.package=java.util.concurrent)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.conflict)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.entity.action)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=java.util.function)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.metadata.schema)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.console)(version>=2.2.0))
252; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.rest)(version>=3.70.0))
214; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.binder)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet.http)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.common.io)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (osgi.wiring.package=javax.management)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=org.xml.sax.helpers)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.io)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.karaf.shell.api.console)(version>=4.3.0))
62; version=4.3.9
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject.servlet)(version>=1.4.0))
101; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu)
103; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.server)(version>=2.2.0))
250; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.health)(version>=4.1.0))
98; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.server.handler)(version>=2.2.0))
250; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.jmx.reflect)(version>=3.70.0))
149; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.stateguard)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.role)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.core)(version>=2.17.0))
136; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.entity)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=ch.qos.logback.core)(version>=1.2.0))
39; version=1.2.13
osgi.wiring.package; (osgi.wiring.package=java.lang)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics)(version>=4.1.0))
15; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.entity)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=io.prometheus.client)(version>=0.6.0))
13; version=0.6.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.property)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=ch.qos.logback.classic)(version>=1.2.0))
39; version=1.2.13
osgi.wiring.package; (&(osgi.wiring.package=org.slf4j)(version>=1.7.0))
21; version=1.7.36
osgi.wiring.package; (&(osgi.wiring.package=org.apache.velocity.app)(version>=2.3.0))
200; version=2.3.0
osgi.wiring.package; (osgi.wiring.package=java.lang.invoke)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.io.input)(version>=1.4.0))
28; version=2.15.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.authz)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.datastore.api)(version>=3.70.0))
141; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.common)(version>=2.3.0))
117; version=2.3.8
osgi.wiring.package; (&(osgi.wiring.package=com.fasterxml.jackson.databind.annotation)(version>=2.17.0))
166; version=2.17.0
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.jvm)(version>=4.1.0))
194; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.authc)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.exception)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.upgrade.datastore)(version>=3.70.0))
204; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=javax.security.auth.login)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.velocity)(version>=2.3.0))
200; version=2.3.0
osgi.wiring.package; (osgi.wiring.package=java.net)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.app)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=ch.qos.logback.classic.spi)(version>=1.2.0))
39; version=1.2.13
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.transaction)(version>=3.70.0))
159; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.server.network.protocol.binary)(version>=2.2.0))
250; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.osgi.framework)(version>=1.9.0))
0; version=1.9.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authc)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.eventbus)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.supportzip)(version>=3.70.0))
184; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=java.util.stream)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.base)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.common.console)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.commands)(version>=3.70.0))
175; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=java.sql)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.util.concurrent.atomic)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.util)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.mime)(version>=3.70.0))
144; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.command)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.privilege)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.subject)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.lang)(version>=2.6.0))
138; version=2.6.0
osgi.wiring.package; (osgi.wiring.package=java.util.concurrent.locks)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.nio.charset)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.security.auth)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.config)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.anonymous)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=javax.servlet)(version>=3.1.0))
34; version=3.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.servlet)(version>=4.1.0))
196; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=io.prometheus.client.exporter)(version>=0.6.0))
30; version=0.6.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.datastore)(version>=3.70.0))
201; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.webresources)(version>=3.70.0))
143; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.index)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (osgi.wiring.package=java.text)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.servlet)(version>=3.70.0))
182; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.server.network.protocol.http.command.get)(version>=2.2.0))
250; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.io)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=javax.ws.rs.core)(version>=2.0.0))
221; version=2.0.1
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.sql.functions)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.cache)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (osgi.wiring.package=java.lang.management)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.sql)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient.transaction)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=javax.sql)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.security.cert)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.user)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.config.memory)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.karaf.jaas.config)(version>=4.3.0))
57; version=4.3.9
osgi.wiring.package; (&(osgi.wiring.package=javax.annotation)(version>=1.2.0))
0; version=1.2.0
osgi.wiring.package; (osgi.wiring.package=java.util.regex)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.goodies.lifecycle)(version>=2.3.0))
120; version=2.3.8
osgi.wiring.package; (osgi.wiring.package=javax.security.auth.callback)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.lang.annotation)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.common.console.annotation)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.wonderland)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.inject)(version>=1.4.0))
89; version=1.4.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.storage)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.compression)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.text)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.util)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.commons.io)(version>=1.4.0))
28; version=2.15.1
osgi.wiring.package; (osgi.wiring.package=java.nio.file)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.logging.task)(version>=3.70.0))
29; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.template)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.hash)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.health.jvm)(version>=4.1.0))
98; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.karaf.jaas.boot.principal)(version>=4.3.0))
0; version=4.3.9
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro.authz.annotation)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.supportzip.datastore)(version>=3.70.0))
184; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.karaf.jaas.boot)(version>=4.3.0))
0; version=4.3.9
osgi.wiring.package; (osgi.wiring.package=java.io)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.apache.shiro)(version>=1.13.0))
154; version=1.13.0
osgi.wiring.package; (&(osgi.wiring.package=io.prometheus.client.dropwizard)(version>=0.6.0))
91; version=0.6.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.server.network.protocol.http)(version>=2.2.0))
250; version=2.2.37
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu.inject)
103; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.jwt)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.db)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.common.log)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.orient)(version>=3.70.0))
245; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.log)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.karaf.shell.api.action)(version>=4.3.0))
62; version=4.3.9
osgi.wiring.package; (&(osgi.wiring.package=javax.inject)(version>=1.0.0))
9; version=1.0.0
osgi.wiring.package; (osgi.wiring.package=java.lang.reflect)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.core.config)(version>=2.2.0))
246; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.codahale.metrics.servlets)(version>=4.1.0))
192; version=4.1.0
osgi.wiring.package; (&(osgi.wiring.package=com.orientechnologies.orient.server.config)(version>=2.2.0))
252; version=2.2.37
osgi.wiring.package; (&(osgi.wiring.package=com.google.common.collect)(version>=32.1.0))
11; version=32.1.1
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.common.upgrade)(version>=3.70.0))
104; version=3.70.4
osgi.wiring.package; (osgi.wiring.package=org.eclipse.sisu.space)
103; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.xml.parsers)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=java.time)
0; version=0.0.0
osgi.wiring.package; (osgi.wiring.package=javax.security.auth.spi)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.thread)(version>=3.70.0))
187; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.apache.ibatis.annotations)(version>=3.5.0))
239; version=3.5.15
osgi.wiring.package; (osgi.wiring.package=java.util.logging)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=io.swagger.annotations)(version>=1.6.0))
188; version=1.6.11
osgi.wiring.package; (osgi.wiring.package=org.xml.sax)
0; version=0.0.0
osgi.wiring.package; (&(osgi.wiring.package=org.sonatype.nexus.security.realm)(version>=3.70.0))
146; version=3.70.4
osgi.wiring.package; (&(osgi.wiring.package=org.joda.time)(version>=2.10.0))
121; version=2.10.6
osgi.wiring.package; (&(osgi.wiring.package=org.apache.velocity.context)(version>=2.3.0))
200; version=2.3.0
