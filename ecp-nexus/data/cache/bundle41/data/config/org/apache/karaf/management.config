:org.apache.felix.configadmin.locked:=B"true"
:org.apache.felix.configadmin.revision:=L"2"
daemon="true"
felix.fileinstall.filename="file:/opt/sonatype/nexus/etc/karaf/org.apache.karaf.management.cfg"
jmxRealm="shiro"
jmxmpEnabled="false"
jmxmpHost="127.0.0.1"
jmxmpObjectName="connector:name\=jmxmp"
jmxmpPort="9999"
jmxmpServiceUrl="service:jmx:jmxmp://127.0.0.1:9999"
objectName="connector:name\=rmi"
rmiRegistryHost="0.0.0.0"
rmiRegistryPort="8099"
rmiServerHost="0.0.0.0"
rmiServerPort="8044"
service.bundleLocation="?"
service.pid="org.apache.karaf.management"
serviceUrl="service:jmx:rmi://0.0.0.0:8044/jndi/rmi://0.0.0.0:8099/karaf-root"
threaded="true"
