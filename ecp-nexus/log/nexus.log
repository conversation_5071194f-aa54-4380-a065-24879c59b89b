2025-07-02 09:54:04,319+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.pax.logging.NexusLogActivator - start
2025-07-02 09:54:06,914+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.features.internal.FeaturesWrapper - Fast FeaturesService starting
2025-07-02 09:54:13,109+0800 INFO  [FelixStartLevel]  *SYSTEM ROOT - bundle org.apache.felix.scr:2.1.30 (56) Starting with globalExtender setting: false
2025-07-02 09:54:13,207+0800 INFO  [FelixStartLevel]  *SYSTEM ROOT - bundle org.apache.felix.scr:2.1.30 (56)  Version = 2.1.30
2025-07-02 09:54:14,726+0800 WARN  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4JInitialiser - Your logging framework class org.ops4j.pax.logging.slf4j.Slf4jLogger is not known - if it needs access to the standard println methods on the console you will need to register it by calling registerLoggingSystemPackage
2025-07-02 09:54:14,728+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Package org.ops4j.pax.logging.slf4j registered; all classes within it or subpackages of it will be allowed to print to System.out and System.err
2025-07-02 09:54:14,802+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Replaced standard System.out and System.err PrintStreams with SLF4JPrintStreams
2025-07-02 09:54:14,804+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Redirected System.out and System.err to SLF4J for this context
2025-07-02 09:54:14,823+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder - Properties:
2025-07-02 09:54:14,901+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   application-host='0.0.0.0'
2025-07-02 09:54:14,901+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   application-port='8081'
2025-07-02 09:54:14,902+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   fabric.etc='/opt/sonatype/nexus/etc/fabric'
2025-07-02 09:54:14,902+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   jetty.etc='/opt/sonatype/nexus/etc/jetty'
2025-07-02 09:54:14,902+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.base='/opt/sonatype/nexus'
2025-07-02 09:54:14,902+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.data='/nexus-data'
2025-07-02 09:54:14,903+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.etc='/opt/sonatype/nexus/etc/karaf'
2025-07-02 09:54:14,904+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.home='/opt/sonatype/nexus'
2025-07-02 09:54:14,905+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.instances='/nexus-data/instances'
2025-07-02 09:54:14,905+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   logback.etc='/opt/sonatype/nexus/etc/logback'
2025-07-02 09:54:14,907+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-args='/opt/sonatype/nexus/etc/jetty/jetty.xml,/opt/sonatype/nexus/etc/jetty/jetty-http.xml,/opt/sonatype/nexus/etc/jetty/jetty-requestlog.xml'
2025-07-02 09:54:14,908+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-context-path='/'
2025-07-02 09:54:14,908+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-edition='nexus-pro-edition'
2025-07-02 09:54:14,909+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-features='nexus-pro-feature'
2025-07-02 09:54:14,909+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus.hazelcast.discovery.isEnabled='true'
2025-07-02 09:54:14,910+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   ssl.etc='/opt/sonatype/nexus/etc/ssl'
2025-07-02 09:54:14,911+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - Java: 1.8.0_442, OpenJDK 64-Bit Server VM, Red Hat, Inc., 25.442-b06
2025-07-02 09:54:14,911+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - OS: Linux, 6.10.14-linuxkit, amd64
2025-07-02 09:54:14,911+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - User: nexus, en, /opt/sonatype/nexus
2025-07-02 09:54:14,912+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - CWD: /opt/sonatype/nexus
2025-07-02 09:54:14,921+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - TMP: /nexus-data/tmp
2025-07-02 09:54:14,934+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Starting
2025-07-02 09:54:15,008+0800 INFO  [FelixStartLevel]  *SYSTEM org.eclipse.jetty.util.log - Logging initialized @21878ms to org.eclipse.jetty.util.log.Slf4jLog
2025-07-02 09:54:15,019+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty.xml
2025-07-02 09:54:16,612+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty-http.xml
2025-07-02 09:54:16,810+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty-requestlog.xml
2025-07-02 09:54:17,012+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Starting: Server@4c263cdd{STOPPED}[9.4.53.v20231009]
2025-07-02 09:54:17,102+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.Server - jetty-9.4.53.v20231009; built: 2023-10-09T12:29:09.265Z; git: 27bde00a0b95a1d5bbee0eae7984f891d2d0f8c9; jvm 1.8.0_442-b06
2025-07-02 09:54:17,706+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - DefaultSessionIdManager workerName=node0
2025-07-02 09:54:17,711+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - No SessionScavenger set, using defaults
2025-07-02 09:54:17,807+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - node0 Scavenging every 660000ms
2025-07-02 09:54:17,903+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Initializing
2025-07-02 09:54:18,099+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.OssNexusEdition - Loading OSS Edition
2025-07-02 09:54:18,116+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Installing: nexus-oss-edition/********* (nexus-orient/*********)
2025-07-02 09:54:40,899+0800 INFO  [jetty-main-1]  *SYSTEM org.ehcache.core.osgi.EhcacheActivator - Detected OSGi Environment (core is in bundle: org.ehcache [152]): Using OSGi Based Service Loading
2025-07-02 09:54:43,899+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Installed: nexus-oss-edition/********* (nexus-orient/*********)
2025-07-02 09:54:46,517+0800 INFO  [jetty-main-1]  *SYSTEM org.apache.shiro.nexus.NexusWebSessionManager - Global session timeout: 1800000 ms
2025-07-02 09:54:46,519+0800 INFO  [jetty-main-1]  *SYSTEM org.apache.shiro.nexus.NexusWebSessionManager - Session-cookie prototype: name=NXSESSIONID, secure=true
2025-07-02 09:54:46,728+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.common [*********]
2025-07-02 09:54:47,501+0800 INFO  [jetty-main-1]  *SYSTEM org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-07-02 09:54:48,305+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.common [*********]
2025-07-02 09:54:48,309+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.hibernate.validator [6.2.0.Final]
2025-07-02 09:54:48,512+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.hibernate.validator [6.2.0.Final]
2025-07-02 09:54:48,514+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore-api [*********]
2025-07-02 09:54:49,110+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore-api [*********]
2025-07-02 09:54:49,120+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cache [*********]
2025-07-02 09:54:49,606+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cache [*********]
2025-07-02 09:54:49,610+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.jmx [*********]
2025-07-02 09:54:49,802+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.jmx [*********]
2025-07-02 09:54:49,805+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.crypto [*********]
2025-07-02 09:54:51,601+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.crypto [*********]
2025-07-02 09:54:51,606+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.security [*********]
2025-07-02 09:54:52,911+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.security [*********]
2025-07-02 09:54:52,912+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.datastore [*********]
2025-07-02 09:54:53,209+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.datastore [*********]
2025-07-02 09:54:53,210+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.supportzip-api [*********]
2025-07-02 09:54:53,311+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.supportzip-api [*********]
2025-07-02 09:54:53,313+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.thread [*********]
2025-07-02 09:54:53,503+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.thread [*********]
2025-07-02 09:54:53,504+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.scheduling [*********]
2025-07-02 09:54:53,717+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.scheduling [*********]
2025-07-02 09:54:53,718+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore [*********]
2025-07-02 09:54:54,005+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore [*********]
2025-07-02 09:54:54,006+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.apache.tika.core [1.28.4]
2025-07-02 09:54:54,104+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.apache.tika.core [1.28.4]
2025-07-02 09:54:54,107+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.orient [*********]
2025-07-02 09:54:54,514+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.orient [*********]
2025-07-02 09:54:54,514+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.upgrade [*********]
2025-07-02 09:54:54,708+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.upgrade [*********]
2025-07-02 09:54:54,708+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore-file [*********]
2025-07-02 09:54:55,005+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore-file [*********]
2025-07-02 09:54:55,006+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.capability [*********]
2025-07-02 09:54:55,109+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.capability [*********]
2025-07-02 09:54:55,109+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.commands [*********]
2025-07-02 09:54:55,212+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.commands [*********]
2025-07-02 09:54:55,213+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.email [*********]
2025-07-02 09:54:55,319+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.email [*********]
2025-07-02 09:54:55,320+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.httpclient [*********]
2025-07-02 09:54:55,615+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.httpclient [*********]
2025-07-02 09:54:55,698+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.servlet [*********]
2025-07-02 09:54:55,839+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.servlet [*********]
2025-07-02 09:54:55,898+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.ssl [*********]
2025-07-02 09:54:55,922+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.ssl [*********]
2025-07-02 09:54:55,923+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.base [*********]
2025-07-02 09:54:56,316+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.metrics.MetricsModule - Metrics support configured
2025-07-02 09:54:56,723+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.metrics.MetricsModule - Metrics support configured
2025-07-02 09:54:59,704+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.base [*********]
2025-07-02 09:54:59,710+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.extdirect [*********]
2025-07-02 09:55:00,017+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.extdirect [*********]
2025-07-02 09:55:00,099+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.siesta [*********]
2025-07-02 09:55:00,313+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.siesta [*********]
2025-07-02 09:55:00,318+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rest-jackson2 [*********]
2025-07-02 09:55:00,417+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rest-jackson2 [*********]
2025-07-02 09:55:00,419+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.swagger [*********]
2025-07-02 09:55:00,519+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.swagger [*********]
2025-07-02 09:55:00,520+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rapture [*********]
2025-07-02 09:55:01,302+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rapture [*********]
2025-07-02 09:55:01,313+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.datastore-mybatis [*********]
2025-07-02 09:55:01,799+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.datastore-mybatis [*********]
2025-07-02 09:55:01,799+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.quartz [*********]
2025-07-02 09:55:02,106+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.quartz [*********]
2025-07-02 09:55:02,107+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.oss-edition [*********]
2025-07-02 09:55:02,300+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.oss-edition [*********]
2025-07-02 09:55:02,307+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Running lifecycle phases [KERNEL, STORAGE, RESTORE, UPGRADE, SCHEMAS, EVENTS, SECURITY, SERVICES, REPOSITORIES, CAPABILITIES, TASKS]
2025-07-02 09:55:02,308+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start KERNEL
2025-07-02 09:55:02,403+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.log.overrides.file.LogbackLoggerOverrides - File: /nexus-data/etc/logback/logback-overrides.xml
2025-07-02 09:55:02,407+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.log.LogbackLogManager - Configuring
2025-07-02 09:55:02,707+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Installing: [nexus-oss-feature/*********, nexus-cma-extra/*********, nexus-ossindex-plugin/*********]
2025-07-02 09:55:41,102+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Installed: [nexus-oss-feature/*********, nexus-cma-extra/*********, nexus-ossindex-plugin/*********]
2025-07-02 09:55:41,306+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-audit-plugin [*********]
2025-07-02 09:55:42,010+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-audit-plugin [*********]
2025-07-02 09:55:42,499+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-ssl-plugin [*********]
2025-07-02 09:55:43,505+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-ssl-plugin [*********]
2025-07-02 09:55:47,306+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.selector [*********]
2025-07-02 09:55:48,507+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.selector [*********]
2025-07-02 09:55:48,522+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.elasticsearch [*********]
2025-07-02 09:55:48,796+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.elasticsearch [*********]
2025-07-02 09:55:48,799+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.repository-content [*********]
2025-07-02 09:55:52,521+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.repository-content [*********]
2025-07-02 09:55:52,608+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.repository-config [*********]
2025-07-02 09:55:53,108+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.repository-config [*********]
2025-07-02 09:55:53,111+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-coreui-plugin [*********]
2025-07-02 09:55:53,921+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-coreui-plugin [*********]
2025-07-02 09:55:54,302+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-httpbridge [*********]
2025-07-02 09:55:54,599+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-httpbridge [*********]
2025-07-02 09:55:55,914+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-blobstore-tasks [*********]
2025-07-02 09:55:56,102+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-blobstore-tasks [*********]
2025-07-02 09:55:56,104+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cleanup-config [*********]
2025-07-02 09:55:56,503+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cleanup-config [*********]
2025-07-02 09:55:56,506+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.core [*********]
2025-07-02 09:55:58,511+0800 ERROR [Thread-77]  *SYSTEM org.sonatype.nexus.internal.node.orient.OrientLocalNodeAccess - Failed to determine hostname, using nodeId instead.
2025-07-02 09:55:58,811+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.core [*********]
2025-07-02 09:55:58,899+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-maven [*********]
2025-07-02 09:56:00,018+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-maven [*********]
2025-07-02 09:56:00,102+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-script-plugin [*********]
2025-07-02 09:56:00,312+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-script-plugin [*********]
2025-07-02 09:56:00,418+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-task-log-cleanup [*********]
2025-07-02 09:56:00,511+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-task-log-cleanup [*********]
2025-07-02 09:56:00,845+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-blobstore-s3 [*********]
2025-07-02 09:56:01,503+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-blobstore-s3 [*********]
2025-07-02 09:56:01,517+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-onboarding-plugin [*********]
2025-07-02 09:56:01,708+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-onboarding-plugin [*********]
2025-07-02 09:56:01,717+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-default-role-plugin [*********]
2025-07-02 09:56:01,816+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-default-role-plugin [*********]
2025-07-02 09:56:01,914+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-apt [*********]
2025-07-02 09:56:02,317+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-apt [*********]
2025-07-02 09:56:03,400+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-raw [*********]
2025-07-02 09:56:03,704+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-raw [*********]
2025-07-02 09:56:03,810+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cleanup [*********]
2025-07-02 09:56:03,998+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cleanup [*********]
2025-07-02 09:56:04,503+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ldap-plugin [*********]
2025-07-02 09:56:04,801+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ldap-plugin [*********]
2025-07-02 09:56:06,001+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-proui-plugin [*********]
2025-07-02 09:56:06,402+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-proui-plugin [*********]
2025-07-02 09:56:06,805+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-proximanova-plugin [*********]
2025-07-02 09:56:07,013+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-proximanova-plugin [*********]
2025-07-02 09:56:08,200+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_insight_scan_insight-scanner-core_2.36.66-01_insight-scanner-core-2.36.66-01.jar [0.0.0]
2025-07-02 09:56:08,403+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_insight_scan_insight-scanner-core_2.36.66-01_insight-scanner-core-2.36.66-01.jar [0.0.0]
2025-07-02 09:56:08,405+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_insight_scan_insight-scanner-model-io_2.36.66-01_insight-scanner-model-io-2.36.66-01.jar [0.0.0]
2025-07-02 09:56:08,502+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_insight_scan_insight-scanner-model-io_2.36.66-01_insight-scanner-model-io-2.36.66-01.jar [0.0.0]
2025-07-02 09:56:08,504+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-healthcheck-base [*********]
2025-07-02 09:56:13,410+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-healthcheck-base [*********]
2025-07-02 09:56:13,426+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_licensing_license-bundle_1.6.0_license-bundle-1.6.0.jar [0.0.0]
2025-07-02 09:56:13,698+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_licensing_license-bundle_1.6.0_license-bundle-1.6.0.jar [0.0.0]
2025-07-02 09:56:13,704+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.licensing-extension [*********]
2025-07-02 09:56:13,819+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.licensing-extension [*********]
2025-07-02 09:56:13,822+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-analytics-plugin [*********]
2025-07-02 09:56:14,222+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-analytics-plugin [*********]
2025-07-02 09:56:14,300+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-licensing-plugin [*********]
2025-07-02 09:56:14,410+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-licensing-plugin [*********]
2025-07-02 09:56:15,310+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-npm [*********]
2025-07-02 09:56:15,719+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-npm [*********]
2025-07-02 09:56:15,798+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-nuget [*********]
2025-07-02 09:56:17,406+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-nuget [*********]
2025-07-02 09:56:17,501+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-rubygems [*********]
2025-07-02 09:56:18,302+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-rubygems [*********]
2025-07-02 09:56:18,312+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rest-client [*********]
2025-07-02 09:56:18,404+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rest-client [*********]
2025-07-02 09:56:18,407+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-migration-plugin [*********]
2025-07-02 09:56:18,907+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-migration-plugin [*********]
2025-07-02 09:56:19,406+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-vulnerability-plugin [*********]
2025-07-02 09:56:19,701+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-vulnerability-plugin [*********]
2025-07-02 09:56:19,702+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-outreach-plugin [*********]
2025-07-02 09:56:19,803+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-outreach-plugin [*********]
2025-07-02 09:56:19,814+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-rutauth-plugin [*********]
2025-07-02 09:56:19,902+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-rutauth-plugin [*********]
2025-07-02 09:56:20,201+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-clm-oss-plugin [*********]
2025-07-02 09:56:20,219+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-clm-oss-plugin [*********]
2025-07-02 09:56:20,403+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-docker [*********]
2025-07-02 09:56:21,900+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-docker [*********]
2025-07-02 09:56:22,201+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-yum [*********]
2025-07-02 09:56:22,513+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-yum [*********]
2025-07-02 09:56:22,899+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING nexus-blobstore-azure-cloud [*********]
2025-07-02 09:56:23,307+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED nexus-blobstore-azure-cloud [*********]
2025-07-02 09:56:23,999+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ahc-plugin [*********]
2025-07-02 09:56:24,610+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ahc-plugin [*********]
2025-07-02 09:56:25,118+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-helm [*********]
2025-07-02 09:56:25,402+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-helm [*********]
2025-07-02 09:56:25,514+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-gitlfs [*********]
2025-07-02 09:56:25,616+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-gitlfs [*********]
2025-07-02 09:56:25,700+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-pypi [*********]
2025-07-02 09:56:26,427+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-pypi [*********]
2025-07-02 09:56:26,530+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-conda [*********]
2025-07-02 09:56:26,699+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-conda [*********]
2025-07-02 09:56:26,708+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-conan [*********]
2025-07-02 09:56:26,908+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-conan [*********]
2025-07-02 09:56:26,919+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-r [*********]
2025-07-02 09:56:27,124+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-r [*********]
2025-07-02 09:56:27,205+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-cocoapods [*********]
2025-07-02 09:56:27,315+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-cocoapods [*********]
2025-07-02 09:56:27,422+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-golang [*********]
2025-07-02 09:56:27,606+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-golang [*********]
2025-07-02 09:56:27,616+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-p2 [*********]
2025-07-02 09:56:27,800+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-p2 [*********]
2025-07-02 09:56:27,917+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-bower [*********]
2025-07-02 09:56:28,211+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-bower [*********]
2025-07-02 09:56:28,309+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ossindex-plugin [*********]
2025-07-02 09:56:28,406+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ossindex-plugin [*********]
2025-07-02 09:56:28,517+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start STORAGE
2025-07-02 09:56:28,703+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.node.orient.OrientLocalNodeAccess - ID: E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A
2025-07-02 09:56:30,701+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - OrientDB version: 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 09:56:30,807+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server v2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x) is starting up...
2025-07-02 09:56:30,814+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Databases directory: /nexus-data/db
2025-07-02 09:56:31,802+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Configuration of usage of soft references inside of containers of results of SQL execution
2025-07-02 09:56:31,812+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Initial and maximum values of heap memory usage are NOT equal, containers of results of SQL executors will NOT use soft references by default
2025-07-02 09:56:31,814+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Auto configuration of disk cache size.
2025-07-02 09:56:32,500+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - 2684354560 B/2560 MB/2 GB of physical memory were detected on machine
2025-07-02 09:56:32,609+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Soft memory limit for this process is set to -1 B/-1 MB/-1 GB
2025-07-02 09:56:32,610+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Hard memory limit for this process is set to -1 B/-1 MB/-1 GB
2025-07-02 09:56:32,612+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Detected memory limit for current process is 2684354560 B/2560 MB/2 GB
2025-07-02 09:56:32,614+0800 WARN  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Not enough physical memory available for DISKCACHE: 2,560MB (heap=1,536MB direct=512MB). Set lower Maximum Heap (-Xmx setting on JVM) and restart OrientDB. Now running with DISKCACHE=256MB
2025-07-02 09:56:32,614+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - OrientDB config DISKCACHE=256MB (heap=1,536MB direct=512MB os=2,560MB)
2025-07-02 09:56:33,015+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Found ORIENTDB_ROOT_PASSWORD variable, using this value as root's password
2025-07-02 09:56:34,241+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.handler.OJMXPlugin - JMX plugin installed and active: profilerManaged=true
2025-07-02 09:56:34,305+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Studio available at $ANSI{blue http://localhost:2480/studio/index.html}
2025-07-02 09:56:34,306+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - $ANSI{green:italic OrientDB Server is active} v2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x).
2025-07-02 09:56:34,307+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - OrientDB Studio has been deprecated and it's no longer available
2025-07-02 09:56:34,307+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - Activated
2025-07-02 09:56:34,814+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start RESTORE
2025-07-02 09:56:36,309+0800 INFO  [ForkJoinPool.commonPool-worker-1]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/component' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 09:56:38,104+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/security' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 09:56:38,703+0800 INFO  [ForkJoinPool.commonPool-worker-1]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/config' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 09:56:39,204+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/analytics' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 09:56:39,703+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start UPGRADE
2025-07-02 09:56:42,906+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SCHEMAS
2025-07-02 09:56:43,006+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool config with per-core limit of 16
2025-07-02 09:56:43,615+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start EVENTS
2025-07-02 09:56:44,516+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SECURITY
2025-07-02 09:56:44,518+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.security.internal.DefaultSecuritySystem - Unlimited strength JCE policy detected
2025-07-02 09:56:44,521+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool security with per-core limit of 16
2025-07-02 09:56:44,601+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.security.internal.RealmManagerImpl - Using default configuration: OrientRealmConfiguration{realmNames=[NexusAuthenticatingRealm, NexusAuthorizingRealm]}
2025-07-02 09:56:44,614+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.RuntimeCacheManagerProvider - Cache-provider: ehcache
2025-07-02 09:56:44,620+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.ehcache.EhCacheManagerProvider - Creating cache-manager with configuration: file:/opt/sonatype/nexus/etc/fabric/ehcache.xml
2025-07-02 09:56:46,806+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache NexusAuthenticatingRealm.authenticationCache will be supplemented by template nexus-default
2025-07-02 09:56:47,011+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthenticatingRealm.authenticationCache' created in EhcacheManager.
2025-07-02 09:56:47,106+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthenticatingRealm.authenticationCache
2025-07-02 09:56:47,112+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthenticatingRealm.authenticationCache
2025-07-02 09:56:47,118+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache NexusAuthorizingRealm.authorizationCache will be supplemented by template nexus-default
2025-07-02 09:56:47,121+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthorizingRealm.authorizationCache' created in EhcacheManager.
2025-07-02 09:56:47,199+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthorizingRealm.authorizationCache
2025-07-02 09:56:47,200+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthorizingRealm.authorizationCache
2025-07-02 09:56:47,212+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SERVICES
2025-07-02 09:56:47,313+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.httpclient.HttpClientManagerImpl - Using default configuration: OrientHttpClientConfiguration{connection=null, proxy=null, authentication=null}
2025-07-02 09:56:47,609+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool analytics with per-core limit of 16
2025-07-02 09:56:48,326+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache SYSTEM_INFORMATION will be supplemented by template nexus-default
2025-07-02 09:56:48,416+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' created in EhcacheManager.
2025-07-02 09:56:48,422+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=SYSTEM_INFORMATION
2025-07-02 09:56:48,504+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=SYSTEM_INFORMATION
2025-07-02 09:56:48,524+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Detected 2 engine-factories
2025-07-02 09:56:48,533+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Engine-factory: Oracle Nashorn v1.8.0_442; language=ECMAScript, version=ECMA - 262 Edition 5.1, names=[nashorn, Nashorn, js, JS, JavaScript, javascript, ECMAScript, ecmascript], mime-types=[application/javascript, application/ecmascript, text/javascript, text/ecmascript], extensions=[js]
2025-07-02 09:56:48,601+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Engine-factory: Groovy Scripting Engine v2.0; language=Groovy, version=3.0.19, names=[groovy, Groovy], mime-types=[application/x-groovy], extensions=[groovy]
2025-07-02 09:56:48,602+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Default language: groovy
2025-07-02 09:56:48,618+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.elasticsearch.internal.NodeProvider - Creating node with config: /opt/sonatype/nexus/etc/fabric/elasticsearch.yml
2025-07-02 09:56:49,301+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] version[2.4.3], pid[1], build[d38a34e/2016-12-07T16:28:56Z]
2025-07-02 09:56:49,302+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] initializing ...
2025-07-02 09:56:49,308+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.plugins - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] modules [], plugins [content-auth-plugin], sites []
2025-07-02 09:56:49,332+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.env - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] using [1] data paths, mounts [[/nexus-data (/run/host_mark/Users)]], net usable_space [622.7gb], net total_space [926.3gb], spins? [possibly], types [fakeowner]
2025-07-02 09:56:49,332+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.env - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] heap size [1.5gb], compressed ordinary object pointers [true]
2025-07-02 09:56:52,620+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] initialized
2025-07-02 09:56:52,621+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] starting ...
2025-07-02 09:56:52,628+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.transport - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] publish_address {local[1]}, bound_addresses {local[1]}
2025-07-02 09:56:52,632+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.discovery - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] nexus/AcbD1u1fTt2TmRrFXL4-lQ
2025-07-02 09:56:52,702+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.cluster.service - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] new_master {E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A}{AcbD1u1fTt2TmRrFXL4-lQ}{local}{local[1]}{local=true, master=true}, reason: local-disco-initial_connect(master)
2025-07-02 09:56:52,707+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] started
2025-07-02 09:56:53,213+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.gateway - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] recovered [5] indices into cluster_state
2025-07-02 09:56:56,217+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.cluster.routing.allocation - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] Cluster health status changed from [RED] to [GREEN] (reason: [shards started [[2e9a1e67e8a325bcd6ee9f6790ff6c769e791d56][0]] ...]).
2025-07-02 09:56:56,430+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool component with per-core limit of 16
2025-07-02 09:56:56,528+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.QuartzSchedulerProvider - Thread-pool size: 20, Thread-pool priority: 5
2025-07-02 09:56:56,607+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.JobStoreImpl - Instance name: nexus; ID: E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A
2025-07-02 09:56:56,607+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.JobStoreImpl - Initialized
2025-07-02 09:56:56,618+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.QuartzSchedulerProvider - Quartz Scheduler v2.3.2
2025-07-02 09:56:57,304+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] : state=WAITING
2025-07-02 09:56:57,305+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Task log cleanup' [tasklog.cleanup] : state=WAITING
2025-07-02 09:56:57,306+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] : state=WAITING
2025-07-02 09:56:57,306+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Cleanup service' [repository.cleanup] : state=WAITING
2025-07-02 09:56:58,012+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start REPOSITORIES
2025-07-02 09:56:58,513+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.blobstore.file.internal.OrientFileBlobStoreMetricsStore - Loading blob store metrics file /nexus-data/blobs/default/E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A-metrics.properties null
2025-07-02 09:57:00,603+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetHostedGalleryFacet - Enabled NuGet feed cooperation for nuget-hosted
2025-07-02 09:57:01,320+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository nuget-group set to Ready to Connect.
2025-07-02 09:57:01,322+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetGroupGalleryFacet - Enabled NuGet feed cooperation for nuget-group
2025-07-02 09:57:01,399+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget-group#odata-query-cache will be supplemented by template nexus-default
2025-07-02 09:57:01,407+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget-group#odata-query-cache' created in EhcacheManager.
2025-07-02 09:57:01,408+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget-group#odata-query-cache
2025-07-02 09:57:01,410+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget-group#odata-query-cache
2025-07-02 09:57:02,513+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository nuget.org-proxy set to Ready to Connect.
2025-07-02 09:57:02,614+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget.org-proxy#negative-cache will be supplemented by template nexus-default
2025-07-02 09:57:02,623+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#negative-cache' created in EhcacheManager.
2025-07-02 09:57:02,624+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#negative-cache
2025-07-02 09:57:02,625+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#negative-cache
2025-07-02 09:57:02,700+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetProxyGalleryFacetImpl - Enabled NuGet feed cooperation for nuget.org-proxy
2025-07-02 09:57:02,701+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget.org-proxy#odata-query-cache will be supplemented by template nexus-default
2025-07-02 09:57:02,704+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#odata-query-cache' created in EhcacheManager.
2025-07-02 09:57:02,706+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#odata-query-cache
2025-07-02 09:57:02,708+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#odata-query-cache
2025-07-02 09:57:03,019+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository maven-central set to Ready to Connect.
2025-07-02 09:57:03,020+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache maven-central#negative-cache will be supplemented by template nexus-default
2025-07-02 09:57:03,099+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'maven-central#negative-cache' created in EhcacheManager.
2025-07-02 09:57:03,102+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=maven-central#negative-cache
2025-07-02 09:57:03,102+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=maven-central#negative-cache
2025-07-02 09:57:03,209+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start CAPABILITIES
2025-07-02 09:57:05,119+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Initialized
2025-07-02 09:57:05,121+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start TASKS
2025-07-02 09:57:06,128+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] removed
2025-07-02 09:57:06,427+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] : state=WAITING
2025-07-02 09:57:06,821+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.scheduling.TaskSchedulerImpl - Task 'Metric aggregation' [content.usage.aggregation] scheduled: cron
2025-07-02 09:57:06,833+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] runNow
2025-07-02 09:57:06,900+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change WAITING -> RUNNING
2025-07-02 09:57:07,317+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] removed
2025-07-02 09:57:07,402+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] : state=WAITING
2025-07-02 09:57:07,411+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.scheduling.TaskSchedulerImpl - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] scheduled: cron
2025-07-02 09:57:07,426+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.search.elasticsearch.IndexStartupRebuildManager - Skipping rebuild of repository indexes
2025-07-02 09:57:07,613+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.OrientQuartzSchedulerSPI - Scheduler put into ready mode
2025-07-02 09:57:08,713+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle - UI plugin descriptors:
2025-07-02 09:57:09,133+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rapture
2025-07-02 09:57:09,215+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-coreui-plugin
2025-07-02 09:57:09,313+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle - ExtJS UI plugin descriptors:
2025-07-02 09:57:09,409+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rapture
2025-07-02 09:57:09,416+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-blobstore-s3
2025-07-02 09:57:09,498+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-proximanova-plugin
2025-07-02 09:57:09,499+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rutauth-plugin
2025-07-02 09:57:09,501+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-blobstore-azure-cloud
2025-07-02 09:57:09,503+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-coreui-plugin
2025-07-02 09:57:09,506+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-proui-plugin
2025-07-02 09:57:09,599+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-maven
2025-07-02 09:57:09,606+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-onboarding-plugin
2025-07-02 09:57:09,607+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-npm
2025-07-02 09:57:09,608+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-nuget
2025-07-02 09:57:09,608+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-rubygems
2025-07-02 09:57:09,608+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-docker
2025-07-02 09:57:09,609+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-pypi
2025-07-02 09:57:09,609+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-analytics-plugin
2025-07-02 09:57:10,117+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.webresources.WebResourceServlet - Max-age: 30 days (2592000 seconds)
2025-07-02 09:57:10,705+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.servlet.DirectJNgineServlet - Servlet GLOBAL configuration: debug=false, providersUrl=service/extdirect, minify=false, batchRequestsMultithreadingEnabled=true, batchRequestsMinThreadsPoolSize=16, batchRequestsMaxThreadsPoolSize=80, batchRequestsMaxThreadsPerRequest=8, batchRequestsMaxThreadKeepAliveSeconds=60, gsonBuilderConfiguratorClass=org.sonatype.nexus.extdirect.internal.ExtDirectGsonBuilderConfigurator, dispatcherClass=com.softwarementors.extjs.djn.servlet.ssm.SsmDispatcher, jsonRequestProcessorThreadClass=org.sonatype.nexus.extdirect.internal.ExtDirectJsonRequestProcessorThread, contextPath=--not specified: calculated via Javascript--, createSourceFiles=true
2025-07-02 09:57:10,713+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.servlet.DirectJNgineServlet - Servlet GLOBAL configuration: registryConfiguratorClass=
2025-07-02 09:57:11,199+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.jscodegen.CodeFileGenerator - Creating source files for APIs...
2025-07-02 09:57:12,108+0800 INFO  [quartz-10-thread-2]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Task log cleanup' [tasklog.cleanup] state change WAITING -> RUNNING
2025-07-02 09:57:13,605+0800 INFO  [quartz-10-thread-2]  *SYSTEM org.sonatype.nexus.tasklog.TaskLogCleanup - Cleaning up log files in /nexus-data/log/tasks older than 30 days
2025-07-02 09:57:13,798+0800 INFO  [quartz-10-thread-3]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Cleanup service' [repository.cleanup] state change WAITING -> RUNNING
2025-07-02 09:57:14,809+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - JAX-RS RuntimeDelegate: org.sonatype.nexus.siesta.internal.resteasy.SisuResteasyProviderFactory@1ec39512
2025-07-02 09:57:14,916+0800 INFO  [quartz-10-thread-2]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Task log cleanup' [tasklog.cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 09:57:15,424+0800 INFO  [quartz-10-thread-3]  *SYSTEM org.sonatype.nexus.cleanup.internal.task.CleanupTask - Task log: /nexus-data/log/tasks/repository.cleanup-20250702095715108.log
2025-07-02 09:57:15,612+0800 INFO  [jetty-main-1]  *SYSTEM org.jboss.resteasy.plugins.validation.i18n - RESTEASY008550: Unable to find CDI supporting ValidatorFactory. Using default ValidatorFactory
2025-07-02 09:57:16,197+0800 INFO  [quartz-10-thread-3]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Cleanup service' [repository.cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 09:57:32,403+0800 INFO  [quartz-10-thread-1]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change RUNNING -> WAITING (OK)
2025-07-02 09:57:37,303+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - Initialized
2025-07-02 09:57:37,502+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.repository.httpbridge.internal.ViewServlet - Initialized
2025-07-02 09:57:37,910+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.handler.ContextHandler - Started o.e.j.w.WebAppContext@ac0747f{Sonatype Nexus,/,null,AVAILABLE}
2025-07-02 09:57:38,399+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.AbstractConnector - Started ServerConnector@4962c800{HTTP/1.1, (http/1.1)}{0.0.0.0:8081}
2025-07-02 09:57:38,400+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.Server - Started @225272ms
2025-07-02 09:57:38,400+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - 
-------------------------------------------------

Started Sonatype Nexus OSS 3.70.4-02

-------------------------------------------------
2025-07-02 09:57:54,706+0800 INFO  [qtp769007130-118]  *UNKNOWN org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Enabling session validation scheduler...
2025-07-02 09:57:54,904+0800 INFO  [qtp769007130-118]  *UNKNOWN org.sonatype.nexus.internal.security.anonymous.AnonymousManagerImpl - Using default configuration: OrientAnonymousConfiguration{enabled=true, userId='anonymous', realmName='NexusAuthorizingRealm'}
2025-07-02 09:58:05,802+0800 INFO  [periodic-4-thread-1]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Repository status for nuget.org-proxy changed from READY to AVAILABLE - reason n/a for n/a
2025-07-02 09:58:19,379+0800 WARN  [SIGTERM handler]  *SYSTEM com.orientechnologies.orient.core.OSignalHandler - Received signal: SIGTERM
2025-07-02 09:58:19,606+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpbridge.internal.ViewServlet - Destroyed
2025-07-02 09:58:19,608+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - Destroyed
2025-07-02 09:58:19,716+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Uptime: 4 minutes, 26 seconds and 484 milliseconds (nexus-oss-edition/*********)
2025-07-02 09:58:19,717+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Shutting down
2025-07-02 09:58:19,801+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop TASKS
2025-07-02 09:58:19,803+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.OrientQuartzSchedulerSPI - Scheduler put into stand-by mode
2025-07-02 09:58:19,817+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop CAPABILITIES
2025-07-02 09:58:19,909+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop REPOSITORIES
2025-07-02 09:58:20,001+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SERVICES
2025-07-02 09:58:20,807+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' removed from EhcacheManager.
2025-07-02 09:58:20,904+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' successfully destroyed in EhcacheManager.
2025-07-02 09:58:21,408+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SECURITY
2025-07-02 09:58:21,425+0800 INFO  [FelixStartLevel]  *SYSTEM org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Disabled session validation scheduler.
2025-07-02 09:58:21,427+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop EVENTS
2025-07-02 09:58:21,428+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SCHEMAS
2025-07-02 09:58:21,503+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop UPGRADE
2025-07-02 09:58:21,504+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop RESTORE
2025-07-02 09:58:21,504+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop STORAGE
2025-07-02 09:58:21,509+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget-group#odata-query-cache' removed from EhcacheManager.
2025-07-02 09:58:21,511+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthenticatingRealm.authenticationCache' removed from EhcacheManager.
2025-07-02 09:58:21,512+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthorizingRealm.authorizationCache' removed from EhcacheManager.
2025-07-02 09:58:21,600+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'maven-central#negative-cache' removed from EhcacheManager.
2025-07-02 09:58:21,609+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#odata-query-cache' removed from EhcacheManager.
2025-07-02 09:58:21,609+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#negative-cache' removed from EhcacheManager.
2025-07-02 09:58:22,110+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.ehcache.EhCacheManagerProvider - Cache-manager closed
2025-07-02 09:58:22,123+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] stopping ...
2025-07-02 09:58:22,717+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] stopped
2025-07-02 09:58:22,722+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] closing ...
2025-07-02 09:58:22,810+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] closed
2025-07-02 09:58:22,914+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping 4 pools
2025-07-02 09:58:23,005+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: analytics
2025-07-02 09:58:23,007+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: security
2025-07-02 09:58:23,008+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: component
2025-07-02 09:58:23,008+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: config
2025-07-02 09:58:23,010+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server is shutting down...
2025-07-02 09:58:23,011+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Shutting down protocols
2025-07-02 09:58:23,101+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.plugin.OServerPluginManager - Shutting down plugins:
2025-07-02 09:58:23,104+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.plugin.OServerPluginManager - - jmx
2025-07-02 09:58:23,105+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server shutdown complete
2025-07-02 09:58:23,108+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - Orient Engine is shutting down...
2025-07-02 09:58:23,111+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: analytics...
2025-07-02 09:58:23,511+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: component...
2025-07-02 09:58:24,214+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: security...
2025-07-02 09:58:24,513+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: config...
2025-07-02 09:58:24,805+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: OSystem...
2025-07-02 09:58:26,510+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - OrientDB Engine shutdown complete
2025-07-02 09:58:26,712+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - Shutdown
2025-07-02 09:58:26,810+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop KERNEL
2025-07-02 09:58:47,857+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.pax.logging.NexusLogActivator - start
2025-07-02 09:58:51,056+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.features.internal.FeaturesWrapper - Fast FeaturesService starting
2025-07-02 09:58:57,452+0800 INFO  [FelixStartLevel]  *SYSTEM ROOT - bundle org.apache.felix.scr:2.1.30 (56) Starting with globalExtender setting: false
2025-07-02 09:58:57,650+0800 INFO  [FelixStartLevel]  *SYSTEM ROOT - bundle org.apache.felix.scr:2.1.30 (56)  Version = 2.1.30
2025-07-02 09:58:59,553+0800 WARN  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4JInitialiser - Your logging framework class org.ops4j.pax.logging.slf4j.Slf4jLogger is not known - if it needs access to the standard println methods on the console you will need to register it by calling registerLoggingSystemPackage
2025-07-02 09:58:59,556+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Package org.ops4j.pax.logging.slf4j registered; all classes within it or subpackages of it will be allowed to print to System.out and System.err
2025-07-02 09:58:59,564+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Replaced standard System.out and System.err PrintStreams with SLF4JPrintStreams
2025-07-02 09:58:59,569+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Redirected System.out and System.err to SLF4J for this context
2025-07-02 09:58:59,652+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder - Properties:
2025-07-02 09:58:59,655+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   application-host='0.0.0.0'
2025-07-02 09:58:59,655+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   application-port='8081'
2025-07-02 09:58:59,655+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   fabric.etc='/opt/sonatype/nexus/etc/fabric'
2025-07-02 09:58:59,655+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   jetty.etc='/opt/sonatype/nexus/etc/jetty'
2025-07-02 09:58:59,656+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.base='/opt/sonatype/nexus'
2025-07-02 09:58:59,656+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.data='/nexus-data'
2025-07-02 09:58:59,656+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.etc='/opt/sonatype/nexus/etc/karaf'
2025-07-02 09:58:59,657+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.home='/opt/sonatype/nexus'
2025-07-02 09:58:59,659+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.instances='/nexus-data/instances'
2025-07-02 09:58:59,660+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   logback.etc='/opt/sonatype/nexus/etc/logback'
2025-07-02 09:58:59,662+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-args='/opt/sonatype/nexus/etc/jetty/jetty.xml,/opt/sonatype/nexus/etc/jetty/jetty-http.xml,/opt/sonatype/nexus/etc/jetty/jetty-requestlog.xml'
2025-07-02 09:58:59,662+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-context-path='/'
2025-07-02 09:58:59,663+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-edition='nexus-pro-edition'
2025-07-02 09:58:59,743+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-features='nexus-pro-feature'
2025-07-02 09:58:59,744+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus.hazelcast.discovery.isEnabled='true'
2025-07-02 09:58:59,745+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   ssl.etc='/opt/sonatype/nexus/etc/ssl'
2025-07-02 09:58:59,747+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - Java: 1.8.0_442, OpenJDK 64-Bit Server VM, Red Hat, Inc., 25.442-b06
2025-07-02 09:58:59,747+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - OS: Linux, 6.10.14-linuxkit, amd64
2025-07-02 09:58:59,747+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - User: nexus, en, /opt/sonatype/nexus
2025-07-02 09:58:59,748+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - CWD: /opt/sonatype/nexus
2025-07-02 09:58:59,758+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - TMP: /nexus-data/tmp
2025-07-02 09:58:59,842+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Starting
2025-07-02 09:58:59,850+0800 INFO  [FelixStartLevel]  *SYSTEM org.eclipse.jetty.util.log - Logging initialized @24196ms to org.eclipse.jetty.util.log.Slf4jLog
2025-07-02 09:58:59,856+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty.xml
2025-07-02 09:59:00,760+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty-http.xml
2025-07-02 09:59:00,974+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty-requestlog.xml
2025-07-02 09:59:01,159+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Starting: Server@60f698bb{STOPPED}[9.4.53.v20231009]
2025-07-02 09:59:01,255+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.Server - jetty-9.4.53.v20231009; built: 2023-10-09T12:29:09.265Z; git: 27bde00a0b95a1d5bbee0eae7984f891d2d0f8c9; jvm 1.8.0_442-b06
2025-07-02 09:59:02,350+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - DefaultSessionIdManager workerName=node0
2025-07-02 09:59:02,353+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - No SessionScavenger set, using defaults
2025-07-02 09:59:02,356+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - node0 Scavenging every 600000ms
2025-07-02 09:59:02,445+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Initializing
2025-07-02 09:59:02,554+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.OssNexusEdition - Loading OSS Edition
2025-07-02 09:59:02,642+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Installing: nexus-oss-edition/********* (nexus-orient/*********)
2025-07-02 09:59:23,354+0800 INFO  [jetty-main-1]  *SYSTEM org.ehcache.core.osgi.EhcacheActivator - Detected OSGi Environment (core is in bundle: org.ehcache [152]): Using OSGi Based Service Loading
2025-07-02 09:59:25,945+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Installed: nexus-oss-edition/********* (nexus-orient/*********)
2025-07-02 09:59:28,177+0800 INFO  [jetty-main-1]  *SYSTEM org.apache.shiro.nexus.NexusWebSessionManager - Global session timeout: 1800000 ms
2025-07-02 09:59:28,178+0800 INFO  [jetty-main-1]  *SYSTEM org.apache.shiro.nexus.NexusWebSessionManager - Session-cookie prototype: name=NXSESSIONID, secure=true
2025-07-02 09:59:28,355+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.common [*********]
2025-07-02 09:59:28,962+0800 INFO  [jetty-main-1]  *SYSTEM org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-07-02 09:59:29,644+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.common [*********]
2025-07-02 09:59:29,646+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.hibernate.validator [6.2.0.Final]
2025-07-02 09:59:29,856+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.hibernate.validator [6.2.0.Final]
2025-07-02 09:59:29,857+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore-api [*********]
2025-07-02 09:59:30,060+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore-api [*********]
2025-07-02 09:59:30,141+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cache [*********]
2025-07-02 09:59:30,349+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cache [*********]
2025-07-02 09:59:30,350+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.jmx [*********]
2025-07-02 09:59:30,545+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.jmx [*********]
2025-07-02 09:59:30,547+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.crypto [*********]
2025-07-02 09:59:31,559+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.crypto [*********]
2025-07-02 09:59:31,642+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.security [*********]
2025-07-02 09:59:33,648+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.security [*********]
2025-07-02 09:59:33,653+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.datastore [*********]
2025-07-02 09:59:34,145+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.datastore [*********]
2025-07-02 09:59:34,147+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.supportzip-api [*********]
2025-07-02 09:59:34,446+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.supportzip-api [*********]
2025-07-02 09:59:34,448+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.thread [*********]
2025-07-02 09:59:34,556+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.thread [*********]
2025-07-02 09:59:34,557+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.scheduling [*********]
2025-07-02 09:59:34,860+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.scheduling [*********]
2025-07-02 09:59:34,861+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore [*********]
2025-07-02 09:59:35,258+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore [*********]
2025-07-02 09:59:35,341+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.apache.tika.core [1.28.4]
2025-07-02 09:59:35,445+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.apache.tika.core [1.28.4]
2025-07-02 09:59:35,448+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.orient [*********]
2025-07-02 09:59:35,948+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.orient [*********]
2025-07-02 09:59:35,949+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.upgrade [*********]
2025-07-02 09:59:36,152+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.upgrade [*********]
2025-07-02 09:59:36,153+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore-file [*********]
2025-07-02 09:59:36,542+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore-file [*********]
2025-07-02 09:59:36,543+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.capability [*********]
2025-07-02 09:59:36,671+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.capability [*********]
2025-07-02 09:59:36,672+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.commands [*********]
2025-07-02 09:59:36,842+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.commands [*********]
2025-07-02 09:59:36,843+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.email [*********]
2025-07-02 09:59:36,951+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.email [*********]
2025-07-02 09:59:36,952+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.httpclient [*********]
2025-07-02 09:59:37,061+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.httpclient [*********]
2025-07-02 09:59:37,063+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.servlet [*********]
2025-07-02 09:59:37,166+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.servlet [*********]
2025-07-02 09:59:37,167+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.ssl [*********]
2025-07-02 09:59:37,264+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.ssl [*********]
2025-07-02 09:59:37,264+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.base [*********]
2025-07-02 09:59:37,657+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.metrics.MetricsModule - Metrics support configured
2025-07-02 09:59:37,759+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.metrics.MetricsModule - Metrics support configured
2025-07-02 09:59:41,549+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.base [*********]
2025-07-02 09:59:41,555+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.extdirect [*********]
2025-07-02 09:59:42,059+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.extdirect [*********]
2025-07-02 09:59:42,061+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.siesta [*********]
2025-07-02 09:59:42,357+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.siesta [*********]
2025-07-02 09:59:42,358+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rest-jackson2 [*********]
2025-07-02 09:59:42,446+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rest-jackson2 [*********]
2025-07-02 09:59:42,448+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.swagger [*********]
2025-07-02 09:59:42,554+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.swagger [*********]
2025-07-02 09:59:42,555+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rapture [*********]
2025-07-02 09:59:43,461+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rapture [*********]
2025-07-02 09:59:43,541+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.datastore-mybatis [*********]
2025-07-02 09:59:43,965+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.datastore-mybatis [*********]
2025-07-02 09:59:43,965+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.quartz [*********]
2025-07-02 09:59:44,249+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.quartz [*********]
2025-07-02 09:59:44,250+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.oss-edition [*********]
2025-07-02 09:59:44,354+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.oss-edition [*********]
2025-07-02 09:59:44,357+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Running lifecycle phases [KERNEL, STORAGE, RESTORE, UPGRADE, SCHEMAS, EVENTS, SECURITY, SERVICES, REPOSITORIES, CAPABILITIES, TASKS]
2025-07-02 09:59:44,357+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start KERNEL
2025-07-02 09:59:44,361+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.log.overrides.file.LogbackLoggerOverrides - File: /nexus-data/etc/logback/logback-overrides.xml
2025-07-02 09:59:44,442+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.log.LogbackLogManager - Configuring
2025-07-02 09:59:44,548+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Installing: [nexus-oss-feature/*********, nexus-cma-extra/*********, nexus-ossindex-plugin/*********]
2025-07-02 10:00:22,593+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Installed: [nexus-oss-feature/*********, nexus-cma-extra/*********, nexus-ossindex-plugin/*********]
2025-07-02 10:00:22,849+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-audit-plugin [*********]
2025-07-02 10:00:24,453+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-audit-plugin [*********]
2025-07-02 10:00:24,763+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-ssl-plugin [*********]
2025-07-02 10:00:25,850+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-ssl-plugin [*********]
2025-07-02 10:00:28,650+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.selector [*********]
2025-07-02 10:00:29,553+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.selector [*********]
2025-07-02 10:00:29,651+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.elasticsearch [*********]
2025-07-02 10:00:29,942+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.elasticsearch [*********]
2025-07-02 10:00:29,944+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.repository-content [*********]
2025-07-02 10:00:32,752+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.repository-content [*********]
2025-07-02 10:00:32,853+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.repository-config [*********]
2025-07-02 10:00:33,160+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.repository-config [*********]
2025-07-02 10:00:33,161+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-coreui-plugin [*********]
2025-07-02 10:00:33,863+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-coreui-plugin [*********]
2025-07-02 10:00:34,246+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-httpbridge [*********]
2025-07-02 10:00:34,947+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-httpbridge [*********]
2025-07-02 10:00:36,753+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-blobstore-tasks [*********]
2025-07-02 10:00:37,172+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-blobstore-tasks [*********]
2025-07-02 10:00:37,179+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cleanup-config [*********]
2025-07-02 10:00:37,366+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cleanup-config [*********]
2025-07-02 10:00:37,441+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.core [*********]
2025-07-02 10:00:38,847+0800 ERROR [Thread-77]  *SYSTEM org.sonatype.nexus.internal.node.orient.OrientLocalNodeAccess - Failed to determine hostname, using nodeId instead.
2025-07-02 10:00:38,956+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.core [*********]
2025-07-02 10:00:38,960+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-maven [*********]
2025-07-02 10:00:39,847+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-maven [*********]
2025-07-02 10:00:39,850+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-script-plugin [*********]
2025-07-02 10:00:40,149+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-script-plugin [*********]
2025-07-02 10:00:40,253+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-task-log-cleanup [*********]
2025-07-02 10:00:40,358+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-task-log-cleanup [*********]
2025-07-02 10:00:40,599+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-blobstore-s3 [*********]
2025-07-02 10:00:41,442+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-blobstore-s3 [*********]
2025-07-02 10:00:41,551+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-onboarding-plugin [*********]
2025-07-02 10:00:42,055+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-onboarding-plugin [*********]
2025-07-02 10:00:42,164+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-default-role-plugin [*********]
2025-07-02 10:00:42,360+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-default-role-plugin [*********]
2025-07-02 10:00:42,542+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-apt [*********]
2025-07-02 10:00:43,047+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-apt [*********]
2025-07-02 10:00:44,042+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-raw [*********]
2025-07-02 10:00:44,347+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-raw [*********]
2025-07-02 10:00:44,449+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cleanup [*********]
2025-07-02 10:00:44,555+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cleanup [*********]
2025-07-02 10:00:45,246+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ldap-plugin [*********]
2025-07-02 10:00:45,660+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ldap-plugin [*********]
2025-07-02 10:00:47,047+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-proui-plugin [*********]
2025-07-02 10:00:47,344+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-proui-plugin [*********]
2025-07-02 10:00:47,546+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-proximanova-plugin [*********]
2025-07-02 10:00:47,650+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-proximanova-plugin [*********]
2025-07-02 10:00:50,344+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_insight_scan_insight-scanner-core_2.36.66-01_insight-scanner-core-2.36.66-01.jar [0.0.0]
2025-07-02 10:00:50,563+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_insight_scan_insight-scanner-core_2.36.66-01_insight-scanner-core-2.36.66-01.jar [0.0.0]
2025-07-02 10:00:50,571+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_insight_scan_insight-scanner-model-io_2.36.66-01_insight-scanner-model-io-2.36.66-01.jar [0.0.0]
2025-07-02 10:00:50,663+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_insight_scan_insight-scanner-model-io_2.36.66-01_insight-scanner-model-io-2.36.66-01.jar [0.0.0]
2025-07-02 10:00:50,744+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-healthcheck-base [*********]
2025-07-02 10:00:55,855+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-healthcheck-base [*********]
2025-07-02 10:00:55,952+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_licensing_license-bundle_1.6.0_license-bundle-1.6.0.jar [0.0.0]
2025-07-02 10:00:56,153+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_licensing_license-bundle_1.6.0_license-bundle-1.6.0.jar [0.0.0]
2025-07-02 10:00:56,155+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.licensing-extension [*********]
2025-07-02 10:00:56,353+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.licensing-extension [*********]
2025-07-02 10:00:56,361+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-analytics-plugin [*********]
2025-07-02 10:00:57,448+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-analytics-plugin [*********]
2025-07-02 10:00:57,460+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-licensing-plugin [*********]
2025-07-02 10:00:57,568+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-licensing-plugin [*********]
2025-07-02 10:00:58,261+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-npm [*********]
2025-07-02 10:00:58,861+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-npm [*********]
2025-07-02 10:00:58,864+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-nuget [*********]
2025-07-02 10:00:59,851+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-nuget [*********]
2025-07-02 10:00:59,853+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-rubygems [*********]
2025-07-02 10:01:00,346+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-rubygems [*********]
2025-07-02 10:01:00,348+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rest-client [*********]
2025-07-02 10:01:00,442+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rest-client [*********]
2025-07-02 10:01:00,444+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-migration-plugin [*********]
2025-07-02 10:01:01,047+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-migration-plugin [*********]
2025-07-02 10:01:01,352+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-vulnerability-plugin [*********]
2025-07-02 10:01:01,646+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-vulnerability-plugin [*********]
2025-07-02 10:01:01,647+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-outreach-plugin [*********]
2025-07-02 10:01:01,755+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-outreach-plugin [*********]
2025-07-02 10:01:01,843+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-rutauth-plugin [*********]
2025-07-02 10:01:01,857+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-rutauth-plugin [*********]
2025-07-02 10:01:02,058+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-clm-oss-plugin [*********]
2025-07-02 10:01:02,161+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-clm-oss-plugin [*********]
2025-07-02 10:01:02,559+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-docker [*********]
2025-07-02 10:01:05,351+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-docker [*********]
2025-07-02 10:01:05,956+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-yum [*********]
2025-07-02 10:01:06,446+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-yum [*********]
2025-07-02 10:01:06,657+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING nexus-blobstore-azure-cloud [*********]
2025-07-02 10:01:06,850+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED nexus-blobstore-azure-cloud [*********]
2025-07-02 10:01:07,148+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ahc-plugin [*********]
2025-07-02 10:01:07,264+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ahc-plugin [*********]
2025-07-02 10:01:07,747+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-helm [*********]
2025-07-02 10:01:07,962+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-helm [*********]
2025-07-02 10:01:08,055+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-gitlfs [*********]
2025-07-02 10:01:08,241+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-gitlfs [*********]
2025-07-02 10:01:08,243+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-pypi [*********]
2025-07-02 10:01:08,565+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-pypi [*********]
2025-07-02 10:01:08,657+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-conda [*********]
2025-07-02 10:01:08,951+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-conda [*********]
2025-07-02 10:01:08,963+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-conan [*********]
2025-07-02 10:01:09,343+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-conan [*********]
2025-07-02 10:01:09,358+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-r [*********]
2025-07-02 10:01:09,559+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-r [*********]
2025-07-02 10:01:09,654+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-cocoapods [*********]
2025-07-02 10:01:09,764+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-cocoapods [*********]
2025-07-02 10:01:09,845+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-golang [*********]
2025-07-02 10:01:09,959+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-golang [*********]
2025-07-02 10:01:10,048+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-p2 [*********]
2025-07-02 10:01:10,245+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-p2 [*********]
2025-07-02 10:01:10,444+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-bower [*********]
2025-07-02 10:01:10,558+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-bower [*********]
2025-07-02 10:01:10,850+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ossindex-plugin [*********]
2025-07-02 10:01:10,940+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ossindex-plugin [*********]
2025-07-02 10:01:11,055+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start STORAGE
2025-07-02 10:01:11,167+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.node.orient.OrientLocalNodeAccess - ID: E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A
2025-07-02 10:01:13,251+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - OrientDB version: 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 10:01:13,357+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server v2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x) is starting up...
2025-07-02 10:01:13,365+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Databases directory: /nexus-data/db
2025-07-02 10:01:14,153+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Configuration of usage of soft references inside of containers of results of SQL execution
2025-07-02 10:01:14,165+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Initial and maximum values of heap memory usage are NOT equal, containers of results of SQL executors will NOT use soft references by default
2025-07-02 10:01:14,167+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Auto configuration of disk cache size.
2025-07-02 10:01:14,843+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - 2684354560 B/2560 MB/2 GB of physical memory were detected on machine
2025-07-02 10:01:14,864+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Soft memory limit for this process is set to -1 B/-1 MB/-1 GB
2025-07-02 10:01:14,864+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Hard memory limit for this process is set to -1 B/-1 MB/-1 GB
2025-07-02 10:01:14,941+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Detected memory limit for current process is 2684354560 B/2560 MB/2 GB
2025-07-02 10:01:14,944+0800 WARN  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Not enough physical memory available for DISKCACHE: 2,560MB (heap=1,536MB direct=512MB). Set lower Maximum Heap (-Xmx setting on JVM) and restart OrientDB. Now running with DISKCACHE=256MB
2025-07-02 10:01:14,945+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - OrientDB config DISKCACHE=256MB (heap=1,536MB direct=512MB os=2,560MB)
2025-07-02 10:01:15,546+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Found ORIENTDB_ROOT_PASSWORD variable, using this value as root's password
2025-07-02 10:01:16,057+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.handler.OJMXPlugin - JMX plugin installed and active: profilerManaged=true
2025-07-02 10:01:16,069+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Studio available at $ANSI{blue http://localhost:2480/studio/index.html}
2025-07-02 10:01:16,070+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - $ANSI{green:italic OrientDB Server is active} v2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x).
2025-07-02 10:01:16,071+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - OrientDB Studio has been deprecated and it's no longer available
2025-07-02 10:01:16,071+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - Activated
2025-07-02 10:01:16,572+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start RESTORE
2025-07-02 10:01:17,252+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/security' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 10:01:18,859+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/analytics' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 10:01:19,188+0800 INFO  [ForkJoinPool.commonPool-worker-1]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/component' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 10:01:20,346+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/config' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 10:01:20,672+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start UPGRADE
2025-07-02 10:01:23,264+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SCHEMAS
2025-07-02 10:01:23,446+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool config with per-core limit of 16
2025-07-02 10:01:24,050+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start EVENTS
2025-07-02 10:01:24,752+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SECURITY
2025-07-02 10:01:24,755+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.security.internal.DefaultSecuritySystem - Unlimited strength JCE policy detected
2025-07-02 10:01:24,757+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool security with per-core limit of 16
2025-07-02 10:01:24,763+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.security.internal.RealmManagerImpl - Using default configuration: OrientRealmConfiguration{realmNames=[NexusAuthenticatingRealm, NexusAuthorizingRealm]}
2025-07-02 10:01:24,772+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.RuntimeCacheManagerProvider - Cache-provider: ehcache
2025-07-02 10:01:24,777+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.ehcache.EhCacheManagerProvider - Creating cache-manager with configuration: file:/opt/sonatype/nexus/etc/fabric/ehcache.xml
2025-07-02 10:01:26,864+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache NexusAuthenticatingRealm.authenticationCache will be supplemented by template nexus-default
2025-07-02 10:01:27,244+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthenticatingRealm.authenticationCache' created in EhcacheManager.
2025-07-02 10:01:27,259+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthenticatingRealm.authenticationCache
2025-07-02 10:01:27,268+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthenticatingRealm.authenticationCache
2025-07-02 10:01:27,273+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache NexusAuthorizingRealm.authorizationCache will be supplemented by template nexus-default
2025-07-02 10:01:27,343+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthorizingRealm.authorizationCache' created in EhcacheManager.
2025-07-02 10:01:27,347+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthorizingRealm.authorizationCache
2025-07-02 10:01:27,347+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthorizingRealm.authorizationCache
2025-07-02 10:01:27,359+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SERVICES
2025-07-02 10:01:27,372+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.httpclient.HttpClientManagerImpl - Using default configuration: OrientHttpClientConfiguration{connection=null, proxy=null, authentication=null}
2025-07-02 10:01:27,642+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool analytics with per-core limit of 16
2025-07-02 10:01:28,264+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache SYSTEM_INFORMATION will be supplemented by template nexus-default
2025-07-02 10:01:28,455+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' created in EhcacheManager.
2025-07-02 10:01:28,461+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=SYSTEM_INFORMATION
2025-07-02 10:01:28,462+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=SYSTEM_INFORMATION
2025-07-02 10:01:28,570+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Detected 2 engine-factories
2025-07-02 10:01:28,578+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Engine-factory: Oracle Nashorn v1.8.0_442; language=ECMAScript, version=ECMA - 262 Edition 5.1, names=[nashorn, Nashorn, js, JS, JavaScript, javascript, ECMAScript, ecmascript], mime-types=[application/javascript, application/ecmascript, text/javascript, text/ecmascript], extensions=[js]
2025-07-02 10:01:28,648+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Engine-factory: Groovy Scripting Engine v2.0; language=Groovy, version=3.0.19, names=[groovy, Groovy], mime-types=[application/x-groovy], extensions=[groovy]
2025-07-02 10:01:28,649+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Default language: groovy
2025-07-02 10:01:28,666+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.elasticsearch.internal.NodeProvider - Creating node with config: /opt/sonatype/nexus/etc/fabric/elasticsearch.yml
2025-07-02 10:01:29,147+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] version[2.4.3], pid[1], build[d38a34e/2016-12-07T16:28:56Z]
2025-07-02 10:01:29,149+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] initializing ...
2025-07-02 10:01:29,153+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.plugins - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] modules [], plugins [content-auth-plugin], sites []
2025-07-02 10:01:29,171+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.env - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] using [1] data paths, mounts [[/nexus-data (/run/host_mark/Users)]], net usable_space [620.8gb], net total_space [926.3gb], spins? [possibly], types [fakeowner]
2025-07-02 10:01:29,171+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.env - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] heap size [1.5gb], compressed ordinary object pointers [true]
2025-07-02 10:01:32,068+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] initialized
2025-07-02 10:01:32,069+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] starting ...
2025-07-02 10:01:32,076+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.transport - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] publish_address {local[1]}, bound_addresses {local[1]}
2025-07-02 10:01:32,080+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.discovery - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] nexus/7Q5WxJQxSY6uliqEiEDLEg
2025-07-02 10:01:32,143+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.cluster.service - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] new_master {E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A}{7Q5WxJQxSY6uliqEiEDLEg}{local}{local[1]}{local=true, master=true}, reason: local-disco-initial_connect(master)
2025-07-02 10:01:32,148+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] started
2025-07-02 10:01:32,455+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.gateway - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] recovered [5] indices into cluster_state
2025-07-02 10:01:35,654+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.cluster.routing.allocation - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] Cluster health status changed from [RED] to [GREEN] (reason: [shards started [[2e9a1e67e8a325bcd6ee9f6790ff6c769e791d56][0]] ...]).
2025-07-02 10:01:35,862+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool component with per-core limit of 16
2025-07-02 10:01:36,057+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.QuartzSchedulerProvider - Thread-pool size: 20, Thread-pool priority: 5
2025-07-02 10:01:36,351+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.JobStoreImpl - Instance name: nexus; ID: E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A
2025-07-02 10:01:36,351+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.JobStoreImpl - Initialized
2025-07-02 10:01:36,364+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.QuartzSchedulerProvider - Quartz Scheduler v2.3.2
2025-07-02 10:01:38,159+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] : state=WAITING
2025-07-02 10:01:38,246+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Task log cleanup' [tasklog.cleanup] : state=WAITING
2025-07-02 10:01:38,246+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] : state=WAITING
2025-07-02 10:01:38,247+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Cleanup service' [repository.cleanup] : state=WAITING
2025-07-02 10:01:39,371+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start REPOSITORIES
2025-07-02 10:01:39,758+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.blobstore.file.internal.OrientFileBlobStoreMetricsStore - Loading blob store metrics file /nexus-data/blobs/default/E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A-metrics.properties null
2025-07-02 10:01:41,475+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetHostedGalleryFacet - Enabled NuGet feed cooperation for nuget-hosted
2025-07-02 10:01:42,159+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository nuget-group set to Ready to Connect.
2025-07-02 10:01:42,165+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetGroupGalleryFacet - Enabled NuGet feed cooperation for nuget-group
2025-07-02 10:01:42,168+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget-group#odata-query-cache will be supplemented by template nexus-default
2025-07-02 10:01:42,248+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget-group#odata-query-cache' created in EhcacheManager.
2025-07-02 10:01:42,253+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget-group#odata-query-cache
2025-07-02 10:01:42,257+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget-group#odata-query-cache
2025-07-02 10:01:43,653+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository nuget.org-proxy set to Ready to Connect.
2025-07-02 10:01:43,660+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget.org-proxy#negative-cache will be supplemented by template nexus-default
2025-07-02 10:01:43,744+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#negative-cache' created in EhcacheManager.
2025-07-02 10:01:43,747+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#negative-cache
2025-07-02 10:01:43,748+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#negative-cache
2025-07-02 10:01:43,748+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetProxyGalleryFacetImpl - Enabled NuGet feed cooperation for nuget.org-proxy
2025-07-02 10:01:43,749+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget.org-proxy#odata-query-cache will be supplemented by template nexus-default
2025-07-02 10:01:43,752+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#odata-query-cache' created in EhcacheManager.
2025-07-02 10:01:43,753+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#odata-query-cache
2025-07-02 10:01:43,754+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#odata-query-cache
2025-07-02 10:01:43,951+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository maven-central set to Ready to Connect.
2025-07-02 10:01:43,954+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache maven-central#negative-cache will be supplemented by template nexus-default
2025-07-02 10:01:43,956+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'maven-central#negative-cache' created in EhcacheManager.
2025-07-02 10:01:43,958+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=maven-central#negative-cache
2025-07-02 10:01:43,958+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=maven-central#negative-cache
2025-07-02 10:01:44,157+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start CAPABILITIES
2025-07-02 10:01:45,745+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Initialized
2025-07-02 10:01:45,747+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start TASKS
2025-07-02 10:01:46,972+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] removed
2025-07-02 10:01:47,262+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] : state=WAITING
2025-07-02 10:01:47,650+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.scheduling.TaskSchedulerImpl - Task 'Metric aggregation' [content.usage.aggregation] scheduled: cron
2025-07-02 10:01:47,659+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] runNow
2025-07-02 10:01:47,662+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change WAITING -> RUNNING
2025-07-02 10:01:48,149+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] removed
2025-07-02 10:01:48,159+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] : state=WAITING
2025-07-02 10:01:48,165+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.scheduling.TaskSchedulerImpl - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] scheduled: cron
2025-07-02 10:01:48,248+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.search.elasticsearch.IndexStartupRebuildManager - Skipping rebuild of repository indexes
2025-07-02 10:01:48,444+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.OrientQuartzSchedulerSPI - Scheduler put into ready mode
2025-07-02 10:01:49,761+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle - UI plugin descriptors:
2025-07-02 10:01:50,160+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rapture
2025-07-02 10:01:50,254+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-coreui-plugin
2025-07-02 10:01:50,355+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle - ExtJS UI plugin descriptors:
2025-07-02 10:01:50,448+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rapture
2025-07-02 10:01:50,543+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-blobstore-s3
2025-07-02 10:01:50,550+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-proximanova-plugin
2025-07-02 10:01:50,645+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rutauth-plugin
2025-07-02 10:01:50,658+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-blobstore-azure-cloud
2025-07-02 10:01:50,851+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-coreui-plugin
2025-07-02 10:01:50,857+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-proui-plugin
2025-07-02 10:01:50,950+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-maven
2025-07-02 10:01:50,953+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-onboarding-plugin
2025-07-02 10:01:50,954+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-npm
2025-07-02 10:01:50,958+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-nuget
2025-07-02 10:01:50,958+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-rubygems
2025-07-02 10:01:50,959+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-docker
2025-07-02 10:01:50,959+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-pypi
2025-07-02 10:01:50,960+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-analytics-plugin
2025-07-02 10:01:51,154+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.webresources.WebResourceServlet - Max-age: 30 days (2592000 seconds)
2025-07-02 10:01:51,559+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.servlet.DirectJNgineServlet - Servlet GLOBAL configuration: debug=false, providersUrl=service/extdirect, minify=false, batchRequestsMultithreadingEnabled=true, batchRequestsMinThreadsPoolSize=16, batchRequestsMaxThreadsPoolSize=80, batchRequestsMaxThreadsPerRequest=8, batchRequestsMaxThreadKeepAliveSeconds=60, gsonBuilderConfiguratorClass=org.sonatype.nexus.extdirect.internal.ExtDirectGsonBuilderConfigurator, dispatcherClass=com.softwarementors.extjs.djn.servlet.ssm.SsmDispatcher, jsonRequestProcessorThreadClass=org.sonatype.nexus.extdirect.internal.ExtDirectJsonRequestProcessorThread, contextPath=--not specified: calculated via Javascript--, createSourceFiles=true
2025-07-02 10:01:51,646+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.servlet.DirectJNgineServlet - Servlet GLOBAL configuration: registryConfiguratorClass=
2025-07-02 10:01:51,755+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.jscodegen.CodeFileGenerator - Creating source files for APIs...
2025-07-02 10:01:55,358+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - JAX-RS RuntimeDelegate: org.sonatype.nexus.siesta.internal.resteasy.SisuResteasyProviderFactory@41c9bd92
2025-07-02 10:01:55,959+0800 INFO  [jetty-main-1]  *SYSTEM org.jboss.resteasy.plugins.validation.i18n - RESTEASY008550: Unable to find CDI supporting ValidatorFactory. Using default ValidatorFactory
2025-07-02 10:01:59,142+0800 INFO  [quartz-10-thread-1]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change RUNNING -> WAITING (OK)
2025-07-02 10:02:08,257+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - Initialized
2025-07-02 10:02:08,955+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.repository.httpbridge.internal.ViewServlet - Initialized
2025-07-02 10:02:10,565+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.handler.ContextHandler - Started o.e.j.w.WebAppContext@7fc06c66{Sonatype Nexus,/,null,AVAILABLE}
2025-07-02 10:02:11,655+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.AbstractConnector - Started ServerConnector@4b3a7fd4{HTTP/1.1, (http/1.1)}{0.0.0.0:8081}
2025-07-02 10:02:11,659+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.Server - Started @216005ms
2025-07-02 10:02:11,660+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - 
-------------------------------------------------

Started Sonatype Nexus OSS 3.70.4-02

-------------------------------------------------
2025-07-02 10:02:37,666+0800 INFO  [qtp943050798-101]  *UNKNOWN org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Enabling session validation scheduler...
2025-07-02 10:02:37,764+0800 INFO  [qtp943050798-101]  *UNKNOWN org.sonatype.nexus.internal.security.anonymous.AnonymousManagerImpl - Using default configuration: OrientAnonymousConfiguration{enabled=true, userId='anonymous', realmName='NexusAuthorizingRealm'}
2025-07-02 10:10:00,718+0800 INFO  [quartz-10-thread-2]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 10:10:01,117+0800 INFO  [quartz-10-thread-2]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 10:20:00,028+0800 INFO  [quartz-10-thread-3]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 10:20:00,115+0800 INFO  [quartz-10-thread-3]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 10:30:00,053+0800 INFO  [quartz-10-thread-4]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 10:30:00,138+0800 INFO  [quartz-10-thread-4]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 10:40:00,022+0800 INFO  [quartz-10-thread-5]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 10:40:00,054+0800 INFO  [quartz-10-thread-5]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 10:48:27,970+0800 WARN  [SIGTERM handler]  *SYSTEM com.orientechnologies.orient.core.OSignalHandler - Received signal: SIGTERM
2025-07-02 10:48:28,476+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpbridge.internal.ViewServlet - Destroyed
2025-07-02 10:48:28,478+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - Destroyed
2025-07-02 10:48:28,579+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Uptime: 49 minutes, 52 seconds and 801 milliseconds (nexus-oss-edition/*********)
2025-07-02 10:48:28,581+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Shutting down
2025-07-02 10:48:28,585+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop TASKS
2025-07-02 10:48:28,588+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.OrientQuartzSchedulerSPI - Scheduler put into stand-by mode
2025-07-02 10:48:28,592+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop CAPABILITIES
2025-07-02 10:48:28,771+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop REPOSITORIES
2025-07-02 10:48:28,870+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SERVICES
2025-07-02 10:48:29,073+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' removed from EhcacheManager.
2025-07-02 10:48:29,077+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' successfully destroyed in EhcacheManager.
2025-07-02 10:48:29,084+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SECURITY
2025-07-02 10:48:29,172+0800 INFO  [FelixStartLevel]  *SYSTEM org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Disabled session validation scheduler.
2025-07-02 10:48:29,173+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop EVENTS
2025-07-02 10:48:29,173+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SCHEMAS
2025-07-02 10:48:29,176+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop UPGRADE
2025-07-02 10:48:29,177+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop RESTORE
2025-07-02 10:48:29,177+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop STORAGE
2025-07-02 10:48:29,178+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget-group#odata-query-cache' removed from EhcacheManager.
2025-07-02 10:48:29,179+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthenticatingRealm.authenticationCache' removed from EhcacheManager.
2025-07-02 10:48:29,180+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthorizingRealm.authorizationCache' removed from EhcacheManager.
2025-07-02 10:48:29,181+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'maven-central#negative-cache' removed from EhcacheManager.
2025-07-02 10:48:29,181+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#odata-query-cache' removed from EhcacheManager.
2025-07-02 10:48:29,182+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#negative-cache' removed from EhcacheManager.
2025-07-02 10:48:29,382+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.ehcache.EhCacheManagerProvider - Cache-manager closed
2025-07-02 10:48:29,385+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] stopping ...
2025-07-02 10:48:29,774+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] stopped
2025-07-02 10:48:29,777+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] closing ...
2025-07-02 10:48:29,873+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] closed
2025-07-02 10:48:29,978+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping 4 pools
2025-07-02 10:48:29,979+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: analytics
2025-07-02 10:48:29,980+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: security
2025-07-02 10:48:30,068+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: component
2025-07-02 10:48:30,069+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: config
2025-07-02 10:48:30,072+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server is shutting down...
2025-07-02 10:48:30,073+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Shutting down protocols
2025-07-02 10:48:30,173+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.plugin.OServerPluginManager - Shutting down plugins:
2025-07-02 10:48:30,366+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.plugin.OServerPluginManager - - jmx
2025-07-02 10:48:30,368+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server shutdown complete
2025-07-02 10:48:30,370+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - Orient Engine is shutting down...
2025-07-02 10:48:30,374+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: analytics...
2025-07-02 10:48:31,670+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: security...
2025-07-02 10:48:31,871+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: component...
2025-07-02 10:48:32,080+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: config...
2025-07-02 10:48:32,474+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: OSystem...
2025-07-02 10:48:33,774+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - OrientDB Engine shutdown complete
2025-07-02 10:48:33,880+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - Shutdown
2025-07-02 10:48:33,887+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop KERNEL
2025-07-02 14:18:55,916+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.pax.logging.NexusLogActivator - start
2025-07-02 14:18:59,023+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.features.internal.FeaturesWrapper - Fast FeaturesService starting
2025-07-02 14:19:06,015+0800 INFO  [FelixStartLevel]  *SYSTEM ROOT - bundle org.apache.felix.scr:2.1.30 (56) Starting with globalExtender setting: false
2025-07-02 14:19:06,030+0800 INFO  [FelixStartLevel]  *SYSTEM ROOT - bundle org.apache.felix.scr:2.1.30 (56)  Version = 2.1.30
2025-07-02 14:19:08,124+0800 WARN  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4JInitialiser - Your logging framework class org.ops4j.pax.logging.slf4j.Slf4jLogger is not known - if it needs access to the standard println methods on the console you will need to register it by calling registerLoggingSystemPackage
2025-07-02 14:19:08,129+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Package org.ops4j.pax.logging.slf4j registered; all classes within it or subpackages of it will be allowed to print to System.out and System.err
2025-07-02 14:19:08,200+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Replaced standard System.out and System.err PrintStreams with SLF4JPrintStreams
2025-07-02 14:19:08,202+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Redirected System.out and System.err to SLF4J for this context
2025-07-02 14:19:08,215+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder - Properties:
2025-07-02 14:19:08,216+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   application-host='0.0.0.0'
2025-07-02 14:19:08,216+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   application-port='8081'
2025-07-02 14:19:08,217+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   fabric.etc='/opt/sonatype/nexus/etc/fabric'
2025-07-02 14:19:08,217+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   jetty.etc='/opt/sonatype/nexus/etc/jetty'
2025-07-02 14:19:08,217+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.base='/opt/sonatype/nexus'
2025-07-02 14:19:08,217+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.data='/nexus-data'
2025-07-02 14:19:08,218+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.etc='/opt/sonatype/nexus/etc/karaf'
2025-07-02 14:19:08,218+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.home='/opt/sonatype/nexus'
2025-07-02 14:19:08,218+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.instances='/nexus-data/instances'
2025-07-02 14:19:08,220+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   logback.etc='/opt/sonatype/nexus/etc/logback'
2025-07-02 14:19:08,220+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-args='/opt/sonatype/nexus/etc/jetty/jetty.xml,/opt/sonatype/nexus/etc/jetty/jetty-http.xml,/opt/sonatype/nexus/etc/jetty/jetty-requestlog.xml'
2025-07-02 14:19:08,220+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-context-path='/'
2025-07-02 14:19:08,221+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-edition='nexus-pro-edition'
2025-07-02 14:19:08,221+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-features='nexus-pro-feature'
2025-07-02 14:19:08,221+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus.hazelcast.discovery.isEnabled='true'
2025-07-02 14:19:08,221+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   ssl.etc='/opt/sonatype/nexus/etc/ssl'
2025-07-02 14:19:08,222+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - Java: 1.8.0_442, OpenJDK 64-Bit Server VM, Red Hat, Inc., 25.442-b06
2025-07-02 14:19:08,297+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - OS: Linux, 6.10.14-linuxkit, amd64
2025-07-02 14:19:08,298+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - User: nexus, en, /opt/sonatype/nexus
2025-07-02 14:19:08,298+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - CWD: /opt/sonatype/nexus
2025-07-02 14:19:08,305+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - TMP: /nexus-data/tmp
2025-07-02 14:19:08,396+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Starting
2025-07-02 14:19:08,405+0800 INFO  [FelixStartLevel]  *SYSTEM org.eclipse.jetty.util.log - Logging initialized @24580ms to org.eclipse.jetty.util.log.Slf4jLog
2025-07-02 14:19:08,413+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty.xml
2025-07-02 14:19:10,016+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty-http.xml
2025-07-02 14:19:10,223+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty-requestlog.xml
2025-07-02 14:19:10,404+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Starting: Server@5c896696{STOPPED}[9.4.53.v20231009]
2025-07-02 14:19:10,501+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.Server - jetty-9.4.53.v20231009; built: 2023-10-09T12:29:09.265Z; git: 27bde00a0b95a1d5bbee0eae7984f891d2d0f8c9; jvm 1.8.0_442-b06
2025-07-02 14:19:11,008+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - DefaultSessionIdManager workerName=node0
2025-07-02 14:19:11,012+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - No SessionScavenger set, using defaults
2025-07-02 14:19:11,103+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - node0 Scavenging every 600000ms
2025-07-02 14:19:11,204+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Initializing
2025-07-02 14:19:11,318+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.OssNexusEdition - Loading OSS Edition
2025-07-02 14:19:11,398+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Installing: nexus-oss-edition/********* (nexus-orient/*********)
2025-07-02 14:19:29,311+0800 INFO  [jetty-main-1]  *SYSTEM org.ehcache.core.osgi.EhcacheActivator - Detected OSGi Environment (core is in bundle: org.ehcache [152]): Using OSGi Based Service Loading
2025-07-02 14:19:31,501+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Installed: nexus-oss-edition/********* (nexus-orient/*********)
2025-07-02 14:19:34,201+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Uptime: 49 seconds and 904 milliseconds (nexus-oss-edition/*********)
2025-07-02 14:19:34,210+0800 ERROR [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Failed to stop nexus
java.lang.NullPointerException: null
	at org.sonatype.nexus.extender.NexusContextListener.moveToPhase(NexusContextListener.java:334)
	at org.sonatype.nexus.extender.NexusContextListener.contextDestroyed(NexusContextListener.java:282)
	at org.sonatype.nexus.extender.NexusBundleExtender.stop(NexusBundleExtender.java:56)
	at org.apache.felix.framework.util.SecureAction.stopActivator(SecureAction.java:720)
	at org.apache.felix.framework.Felix.stopBundle(Felix.java:2795)
	at org.apache.felix.framework.Felix.setActiveStartLevel(Felix.java:1557)
	at org.apache.felix.framework.FrameworkStartLevelImpl.run(FrameworkStartLevelImpl.java:308)
	at java.lang.Thread.run(Thread.java:750)
2025-07-02 14:19:35,099+0800 INFO  [jetty-main-1]  *SYSTEM org.apache.shiro.nexus.NexusWebSessionManager - Global session timeout: 1800000 ms
2025-07-02 14:19:35,104+0800 INFO  [jetty-main-1]  *SYSTEM org.apache.shiro.nexus.NexusWebSessionManager - Session-cookie prototype: name=NXSESSIONID, secure=true
2025-07-02 14:19:35,307+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Stopping
2025-07-02 14:19:35,399+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Stopping: Server@5c896696{STARTING}[9.4.53.v20231009]
2025-07-02 14:19:35,703+0800 ERROR [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Failed to initialize
com.google.inject.CreationException: Unable to create injector, see the following errors:

1) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  at WebGuiceEnvironment.<init>(WebGuiceEnvironment.java:42)
      \_ for 1st parameter
  at WebGuiceEnvironment.class(WebGuiceEnvironment.java:42)
  while locating WebGuiceEnvironment
  at ShiroWebModule.configureShiro(ShiroWebModule.java:140)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating WebEnvironment
  while locating Environment

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at SingleParameterInjector.inject(SingleParameterInjector.java:40)
	at SingleParameterInjector.getAll(SingleParameterInjector.java:60)
	at ConstructorInjector.provision(ConstructorInjector.java:113)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

2) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  at WebGuiceEnvironment.<init>(WebGuiceEnvironment.java:42)
      \_ for 1st parameter
  at WebGuiceEnvironment.class(WebGuiceEnvironment.java:42)
  while locating WebGuiceEnvironment
  at ShiroWebModule.configureShiro(ShiroWebModule.java:140)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating WebEnvironment

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at SingleParameterInjector.inject(SingleParameterInjector.java:40)
	at SingleParameterInjector.getAll(SingleParameterInjector.java:60)
	at ConstructorInjector.provision(ConstructorInjector.java:113)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

3) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating FilterChainResolver

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

4) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  at GuiceShiroFilter.<init>(GuiceShiroFilter.java:35)
      \_ for 2nd parameter
  at ShiroWebModule.configureShiro(ShiroWebModule.java:141)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating GuiceShiroFilter

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at SingleParameterInjector.inject(SingleParameterInjector.java:40)
	at SingleParameterInjector.getAll(SingleParameterInjector.java:60)
	at ConstructorInjector.provision(ConstructorInjector.java:113)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

4 errors

======================
Full classname legend:
======================
AbstractHandler:                                     "org.eclipse.jetty.server.handler.AbstractHandler"
AbstractLifeCycle:                                   "org.eclipse.jetty.util.component.AbstractLifeCycle"
AbstractTracked:                                     "org.osgi.util.tracker.AbstractTracked"
BeanTypeListener$1:                                  "org.apache.shiro.guice.BeanTypeListener$1"
BindingTracker:                                      "org.eclipse.sisu.osgi.BindingTracker"
BootstrapListener:                                   "org.sonatype.nexus.bootstrap.osgi.BootstrapListener"
BundleContextImpl:                                   "org.apache.felix.framework.BundleContextImpl"
ConstructorBindingImpl$Factory:                      "com.google.inject.internal.ConstructorBindingImpl$Factory"
ConstructorInjector:                                 "com.google.inject.internal.ConstructorInjector"
ContainerLifeCycle:                                  "org.eclipse.jetty.util.component.ContainerLifeCycle"
ContextHandler:                                      "org.eclipse.jetty.server.handler.ContextHandler"
DefaultBeanLocator:                                  "org.eclipse.sisu.inject.DefaultBeanLocator"
DefaultConstructionProxyFactory$FastClassProxy:      "com.google.inject.internal.DefaultConstructionProxyFactory$FastClassProxy"
DynamicFilterChainManager:                           "org.sonatype.nexus.security.DynamicFilterChainManager"
DynamicFilterChainManager$$FastClassByGuice$$ea8fb3: "org.sonatype.nexus.security.DynamicFilterChainManager$$FastClassByGuice$$ea8fb3"
Environment:                                         "org.apache.shiro.env.Environment"
FactoryProxy:                                        "com.google.inject.internal.FactoryProxy"
FilterChainManager:                                  "org.apache.shiro.web.filter.mgt.FilterChainManager"
FilterChainResolver:                                 "org.apache.shiro.web.filter.mgt.FilterChainResolver"
Guice:                                               "com.google.inject.Guice"
GuiceShiroFilter:                                    "org.apache.shiro.guice.web.GuiceShiroFilter"
InjectorImpl:                                        "com.google.inject.internal.InjectorImpl"
InjectorImpl$1:                                      "com.google.inject.internal.InjectorImpl$1"
InstrumentedHandler:                                 "com.codahale.metrics.jetty9.InstrumentedHandler"
InternalFactoryToProviderAdapter:                    "com.google.inject.internal.InternalFactoryToProviderAdapter"
InternalInjectorCreator:                             "com.google.inject.internal.InternalInjectorCreator"
JettyServer$JettyMainThread:                         "org.sonatype.nexus.bootstrap.jetty.JettyServer$JettyMainThread"
ListenerTracker:                                     "org.sonatype.nexus.bootstrap.osgi.ListenerTracker"
MembersInjectorImpl:                                 "com.google.inject.internal.MembersInjectorImpl"
NexusContextListener:                                "org.sonatype.nexus.extender.NexusContextListener"
NexusContextModule:                                  "org.sonatype.nexus.extender.NexusContextModule"
ProviderToInternalFactoryAdapter:                    "com.google.inject.internal.ProviderToInternalFactoryAdapter"
Server:                                              "org.eclipse.jetty.server.Server"
ServiceBindings:                                     "org.eclipse.sisu.osgi.ServiceBindings"
ServiceTracker:                                      "org.osgi.util.tracker.ServiceTracker"
ServiceTracker$Tracked:                              "org.osgi.util.tracker.ServiceTracker$Tracked"
ServletContextHandler:                               "org.eclipse.jetty.servlet.ServletContextHandler"
ServletHandler:                                      "org.eclipse.jetty.servlet.ServletHandler"
ShiroWebModule:                                      "org.apache.shiro.guice.web.ShiroWebModule"
SingleParameterInjector:                             "com.google.inject.internal.SingleParameterInjector"
SingletonScope$1:                                    "com.google.inject.internal.SingletonScope$1"
WebAppContext:                                       "org.eclipse.jetty.webapp.WebAppContext"
WebEnvironment:                                      "org.apache.shiro.web.env.WebEnvironment"
WebGuiceEnvironment:                                 "org.apache.shiro.guice.web.WebGuiceEnvironment"
WebSecurityModule:                                   "org.sonatype.nexus.security.WebSecurityModule"
WireModule:                                          "org.eclipse.sisu.wire.WireModule"
========================
End of classname legend:
========================

	at com.google.inject.internal.Errors.throwCreationExceptionIfErrorsExist(Errors.java:589)
	at com.google.inject.internal.InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:190)
	at com.google.inject.internal.InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at com.google.inject.Guice.createInjector(Guice.java:87)
	at com.google.inject.Guice.createInjector(Guice.java:69)
	at com.google.inject.Guice.createInjector(Guice.java:59)
	at org.sonatype.nexus.extender.NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at org.sonatype.nexus.bootstrap.osgi.ListenerTracker.addingService(ListenerTracker.java:47)
	at org.sonatype.nexus.bootstrap.osgi.ListenerTracker.addingService(ListenerTracker.java:1)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:183)
	at org.osgi.util.tracker.ServiceTracker.open(ServiceTracker.java:321)
	at org.osgi.util.tracker.ServiceTracker.open(ServiceTracker.java:264)
	at org.sonatype.nexus.bootstrap.osgi.BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at org.eclipse.jetty.server.handler.ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at org.eclipse.jetty.servlet.ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at org.eclipse.jetty.server.handler.ContextHandler.contextInitialized(ContextHandler.java:1002)
	at org.eclipse.jetty.servlet.ServletHandler.initialize(ServletHandler.java:765)
	at org.eclipse.jetty.servlet.ServletContextHandler.startContext(ServletContextHandler.java:379)
	at org.eclipse.jetty.webapp.WebAppContext.startWebapp(WebAppContext.java:1449)
	at org.eclipse.jetty.webapp.WebAppContext.startContext(WebAppContext.java:1414)
	at org.eclipse.jetty.server.handler.ContextHandler.doStart(ContextHandler.java:916)
	at org.eclipse.jetty.servlet.ServletContextHandler.doStart(ServletContextHandler.java:288)
	at org.eclipse.jetty.webapp.WebAppContext.doStart(WebAppContext.java:524)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:97)
	at com.codahale.metrics.jetty9.InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:97)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.server.Server.start(Server.java:423)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:97)
	at org.eclipse.jetty.server.Server.doStart(Server.java:387)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.sonatype.nexus.bootstrap.jetty.JettyServer$JettyMainThread.run(JettyServer.java:274)
2025-07-02 14:19:35,907+0800 WARN  [jetty-main-1]  *SYSTEM org.eclipse.jetty.webapp.WebAppContext - Failed startup of context o.e.j.w.WebAppContext@6e3d8067{Sonatype Nexus,/,null,UNAVAILABLE}
com.google.inject.CreationException: Unable to create injector, see the following errors:

1) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  at WebGuiceEnvironment.<init>(WebGuiceEnvironment.java:42)
      \_ for 1st parameter
  at WebGuiceEnvironment.class(WebGuiceEnvironment.java:42)
  while locating WebGuiceEnvironment
  at ShiroWebModule.configureShiro(ShiroWebModule.java:140)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating WebEnvironment
  while locating Environment

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at SingleParameterInjector.inject(SingleParameterInjector.java:40)
	at SingleParameterInjector.getAll(SingleParameterInjector.java:60)
	at ConstructorInjector.provision(ConstructorInjector.java:113)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

2) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  at WebGuiceEnvironment.<init>(WebGuiceEnvironment.java:42)
      \_ for 1st parameter
  at WebGuiceEnvironment.class(WebGuiceEnvironment.java:42)
  while locating WebGuiceEnvironment
  at ShiroWebModule.configureShiro(ShiroWebModule.java:140)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating WebEnvironment

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at SingleParameterInjector.inject(SingleParameterInjector.java:40)
	at SingleParameterInjector.getAll(SingleParameterInjector.java:60)
	at ConstructorInjector.provision(ConstructorInjector.java:113)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

3) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating FilterChainResolver

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

4) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  at GuiceShiroFilter.<init>(GuiceShiroFilter.java:35)
      \_ for 2nd parameter
  at ShiroWebModule.configureShiro(ShiroWebModule.java:141)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating GuiceShiroFilter

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at SingleParameterInjector.inject(SingleParameterInjector.java:40)
	at SingleParameterInjector.getAll(SingleParameterInjector.java:60)
	at ConstructorInjector.provision(ConstructorInjector.java:113)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

4 errors

======================
Full classname legend:
======================
AbstractHandler:                                     "org.eclipse.jetty.server.handler.AbstractHandler"
AbstractLifeCycle:                                   "org.eclipse.jetty.util.component.AbstractLifeCycle"
AbstractTracked:                                     "org.osgi.util.tracker.AbstractTracked"
BeanTypeListener$1:                                  "org.apache.shiro.guice.BeanTypeListener$1"
BindingTracker:                                      "org.eclipse.sisu.osgi.BindingTracker"
BootstrapListener:                                   "org.sonatype.nexus.bootstrap.osgi.BootstrapListener"
BundleContextImpl:                                   "org.apache.felix.framework.BundleContextImpl"
ConstructorBindingImpl$Factory:                      "com.google.inject.internal.ConstructorBindingImpl$Factory"
ConstructorInjector:                                 "com.google.inject.internal.ConstructorInjector"
ContainerLifeCycle:                                  "org.eclipse.jetty.util.component.ContainerLifeCycle"
ContextHandler:                                      "org.eclipse.jetty.server.handler.ContextHandler"
DefaultBeanLocator:                                  "org.eclipse.sisu.inject.DefaultBeanLocator"
DefaultConstructionProxyFactory$FastClassProxy:      "com.google.inject.internal.DefaultConstructionProxyFactory$FastClassProxy"
DynamicFilterChainManager:                           "org.sonatype.nexus.security.DynamicFilterChainManager"
DynamicFilterChainManager$$FastClassByGuice$$ea8fb3: "org.sonatype.nexus.security.DynamicFilterChainManager$$FastClassByGuice$$ea8fb3"
Environment:                                         "org.apache.shiro.env.Environment"
FactoryProxy:                                        "com.google.inject.internal.FactoryProxy"
FilterChainManager:                                  "org.apache.shiro.web.filter.mgt.FilterChainManager"
FilterChainResolver:                                 "org.apache.shiro.web.filter.mgt.FilterChainResolver"
Guice:                                               "com.google.inject.Guice"
GuiceShiroFilter:                                    "org.apache.shiro.guice.web.GuiceShiroFilter"
InjectorImpl:                                        "com.google.inject.internal.InjectorImpl"
InjectorImpl$1:                                      "com.google.inject.internal.InjectorImpl$1"
InstrumentedHandler:                                 "com.codahale.metrics.jetty9.InstrumentedHandler"
InternalFactoryToProviderAdapter:                    "com.google.inject.internal.InternalFactoryToProviderAdapter"
InternalInjectorCreator:                             "com.google.inject.internal.InternalInjectorCreator"
JettyServer$JettyMainThread:                         "org.sonatype.nexus.bootstrap.jetty.JettyServer$JettyMainThread"
ListenerTracker:                                     "org.sonatype.nexus.bootstrap.osgi.ListenerTracker"
MembersInjectorImpl:                                 "com.google.inject.internal.MembersInjectorImpl"
NexusContextListener:                                "org.sonatype.nexus.extender.NexusContextListener"
NexusContextModule:                                  "org.sonatype.nexus.extender.NexusContextModule"
ProviderToInternalFactoryAdapter:                    "com.google.inject.internal.ProviderToInternalFactoryAdapter"
Server:                                              "org.eclipse.jetty.server.Server"
ServiceBindings:                                     "org.eclipse.sisu.osgi.ServiceBindings"
ServiceTracker:                                      "org.osgi.util.tracker.ServiceTracker"
ServiceTracker$Tracked:                              "org.osgi.util.tracker.ServiceTracker$Tracked"
ServletContextHandler:                               "org.eclipse.jetty.servlet.ServletContextHandler"
ServletHandler:                                      "org.eclipse.jetty.servlet.ServletHandler"
ShiroWebModule:                                      "org.apache.shiro.guice.web.ShiroWebModule"
SingleParameterInjector:                             "com.google.inject.internal.SingleParameterInjector"
SingletonScope$1:                                    "com.google.inject.internal.SingletonScope$1"
WebAppContext:                                       "org.eclipse.jetty.webapp.WebAppContext"
WebEnvironment:                                      "org.apache.shiro.web.env.WebEnvironment"
WebGuiceEnvironment:                                 "org.apache.shiro.guice.web.WebGuiceEnvironment"
WebSecurityModule:                                   "org.sonatype.nexus.security.WebSecurityModule"
WireModule:                                          "org.eclipse.sisu.wire.WireModule"
========================
End of classname legend:
========================

	at com.google.inject.internal.Errors.throwCreationExceptionIfErrorsExist(Errors.java:589)
	at com.google.inject.internal.InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:190)
	at com.google.inject.internal.InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at com.google.inject.Guice.createInjector(Guice.java:87)
	at com.google.inject.Guice.createInjector(Guice.java:69)
	at com.google.inject.Guice.createInjector(Guice.java:59)
	at org.sonatype.nexus.extender.NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at org.sonatype.nexus.bootstrap.osgi.ListenerTracker.addingService(ListenerTracker.java:47)
	at org.sonatype.nexus.bootstrap.osgi.ListenerTracker.addingService(ListenerTracker.java:1)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:183)
	at org.osgi.util.tracker.ServiceTracker.open(ServiceTracker.java:321)
	at org.osgi.util.tracker.ServiceTracker.open(ServiceTracker.java:264)
	at org.sonatype.nexus.bootstrap.osgi.BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at org.eclipse.jetty.server.handler.ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at org.eclipse.jetty.servlet.ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at org.eclipse.jetty.server.handler.ContextHandler.contextInitialized(ContextHandler.java:1002)
	at org.eclipse.jetty.servlet.ServletHandler.initialize(ServletHandler.java:765)
	at org.eclipse.jetty.servlet.ServletContextHandler.startContext(ServletContextHandler.java:379)
	at org.eclipse.jetty.webapp.WebAppContext.startWebapp(WebAppContext.java:1449)
	at org.eclipse.jetty.webapp.WebAppContext.startContext(WebAppContext.java:1414)
	at org.eclipse.jetty.server.handler.ContextHandler.doStart(ContextHandler.java:916)
	at org.eclipse.jetty.servlet.ServletContextHandler.doStart(ServletContextHandler.java:288)
	at org.eclipse.jetty.webapp.WebAppContext.doStart(WebAppContext.java:524)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:97)
	at com.codahale.metrics.jetty9.InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:97)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.server.Server.start(Server.java:423)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:97)
	at org.eclipse.jetty.server.Server.doStart(Server.java:387)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.sonatype.nexus.bootstrap.jetty.JettyServer$JettyMainThread.run(JettyServer.java:274)
2025-07-02 14:19:35,912+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - node0 Stopped scavenging
2025-07-02 14:19:36,195+0800 ERROR [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Failed to start
com.google.inject.CreationException: Unable to create injector, see the following errors:

1) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  at WebGuiceEnvironment.<init>(WebGuiceEnvironment.java:42)
      \_ for 1st parameter
  at WebGuiceEnvironment.class(WebGuiceEnvironment.java:42)
  while locating WebGuiceEnvironment
  at ShiroWebModule.configureShiro(ShiroWebModule.java:140)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating WebEnvironment
  while locating Environment

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at SingleParameterInjector.inject(SingleParameterInjector.java:40)
	at SingleParameterInjector.getAll(SingleParameterInjector.java:60)
	at ConstructorInjector.provision(ConstructorInjector.java:113)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

2) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  at WebGuiceEnvironment.<init>(WebGuiceEnvironment.java:42)
      \_ for 1st parameter
  at WebGuiceEnvironment.class(WebGuiceEnvironment.java:42)
  while locating WebGuiceEnvironment
  at ShiroWebModule.configureShiro(ShiroWebModule.java:140)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating WebEnvironment

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at SingleParameterInjector.inject(SingleParameterInjector.java:40)
	at SingleParameterInjector.getAll(SingleParameterInjector.java:60)
	at ConstructorInjector.provision(ConstructorInjector.java:113)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

3) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating FilterChainResolver

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

4) [Guice/ErrorInjectingConstructor]: IllegalStateException: Invalid BundleContext.
  at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:56)
  at WebSecurityModule.bindSingleton(WebSecurityModule.java:87)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating DynamicFilterChainManager
  while locating FilterChainManager
  at WebSecurityModule.configureShiroWeb(WebSecurityModule.java:78)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  at GuiceShiroFilter.<init>(GuiceShiroFilter.java:35)
      \_ for 2nd parameter
  at ShiroWebModule.configureShiro(ShiroWebModule.java:141)
      \_ installed by: WireModule -> NexusContextModule -> WebSecurityModule
  while locating GuiceShiroFilter

Learn more:
  https://github.com/google/guice/wiki/ERROR_INJECTING_CONSTRUCTOR
Caused by: IllegalStateException: Invalid BundleContext.
	at BundleContextImpl.checkValidity(BundleContextImpl.java:491)
	at BundleContextImpl.createFilter(BundleContextImpl.java:107)
	at ServiceTracker.<init>(ServiceTracker.java:187)
	at BindingTracker.<init>(BindingTracker.java:46)
	at ServiceBindings.subscribe(ServiceBindings.java:120)
	at DefaultBeanLocator.watch(DefaultBeanLocator.java:80)
	at DynamicFilterChainManager.<init>(DynamicFilterChainManager.java:61)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.GUICE$TRAMPOLINE(<generated>)
	at DynamicFilterChainManager$$FastClassByGuice$$ea8fb3.apply(<generated>)
	at DefaultConstructionProxyFactory$FastClassProxy.newInstance(DefaultConstructionProxyFactory.java:82)
	at ConstructorInjector.provision(ConstructorInjector.java:114)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at FactoryProxy.get(FactoryProxy.java:60)
	at InjectorImpl$1.get(InjectorImpl.java:1148)
	at InjectorImpl.getInstance(InjectorImpl.java:1181)
	at BeanTypeListener$1.injectMembers(BeanTypeListener.java:106)
	at MembersInjectorImpl.injectMembers(MembersInjectorImpl.java:159)
	at ConstructorInjector.provision(ConstructorInjector.java:124)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at SingleParameterInjector.inject(SingleParameterInjector.java:40)
	at SingleParameterInjector.getAll(SingleParameterInjector.java:60)
	at ConstructorInjector.provision(ConstructorInjector.java:113)
	at ConstructorInjector.construct(ConstructorInjector.java:91)
	at ConstructorBindingImpl$Factory.get(ConstructorBindingImpl.java:300)
	at ProviderToInternalFactoryAdapter.get(ProviderToInternalFactoryAdapter.java:40)
	at SingletonScope$1.get(SingletonScope.java:169)
	at InternalFactoryToProviderAdapter.get(InternalFactoryToProviderAdapter.java:45)
	at InternalInjectorCreator.loadEagerSingletons(InternalInjectorCreator.java:213)
	at InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:186)
	at InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at Guice.createInjector(Guice.java:87)
	at Guice.createInjector(Guice.java:69)
	at Guice.createInjector(Guice.java:59)
	at NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at ListenerTracker.addingService(ListenerTracker.java:47)
	at ListenerTracker.addingService(ListenerTracker.java:1)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at AbstractTracked.trackAdding(AbstractTracked.java:256)
	at AbstractTracked.trackInitial(AbstractTracked.java:183)
	at ServiceTracker.open(ServiceTracker.java:321)
	at ServiceTracker.open(ServiceTracker.java:264)
	at BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at ContextHandler.contextInitialized(ContextHandler.java:1002)
	at ServletHandler.initialize(ServletHandler.java:765)
	at ServletContextHandler.startContext(ServletContextHandler.java:379)
	at WebAppContext.startWebapp(WebAppContext.java:1449)
	at WebAppContext.startContext(WebAppContext.java:1414)
	at ContextHandler.doStart(ContextHandler.java:916)
	at ServletContextHandler.doStart(ServletContextHandler.java:288)
	at WebAppContext.doStart(WebAppContext.java:524)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at Server.start(Server.java:423)
	at ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at AbstractHandler.doStart(AbstractHandler.java:97)
	at Server.doStart(Server.java:387)
	at AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at JettyServer$JettyMainThread.run(JettyServer.java:274)

4 errors

======================
Full classname legend:
======================
AbstractHandler:                                     "org.eclipse.jetty.server.handler.AbstractHandler"
AbstractLifeCycle:                                   "org.eclipse.jetty.util.component.AbstractLifeCycle"
AbstractTracked:                                     "org.osgi.util.tracker.AbstractTracked"
BeanTypeListener$1:                                  "org.apache.shiro.guice.BeanTypeListener$1"
BindingTracker:                                      "org.eclipse.sisu.osgi.BindingTracker"
BootstrapListener:                                   "org.sonatype.nexus.bootstrap.osgi.BootstrapListener"
BundleContextImpl:                                   "org.apache.felix.framework.BundleContextImpl"
ConstructorBindingImpl$Factory:                      "com.google.inject.internal.ConstructorBindingImpl$Factory"
ConstructorInjector:                                 "com.google.inject.internal.ConstructorInjector"
ContainerLifeCycle:                                  "org.eclipse.jetty.util.component.ContainerLifeCycle"
ContextHandler:                                      "org.eclipse.jetty.server.handler.ContextHandler"
DefaultBeanLocator:                                  "org.eclipse.sisu.inject.DefaultBeanLocator"
DefaultConstructionProxyFactory$FastClassProxy:      "com.google.inject.internal.DefaultConstructionProxyFactory$FastClassProxy"
DynamicFilterChainManager:                           "org.sonatype.nexus.security.DynamicFilterChainManager"
DynamicFilterChainManager$$FastClassByGuice$$ea8fb3: "org.sonatype.nexus.security.DynamicFilterChainManager$$FastClassByGuice$$ea8fb3"
Environment:                                         "org.apache.shiro.env.Environment"
FactoryProxy:                                        "com.google.inject.internal.FactoryProxy"
FilterChainManager:                                  "org.apache.shiro.web.filter.mgt.FilterChainManager"
FilterChainResolver:                                 "org.apache.shiro.web.filter.mgt.FilterChainResolver"
Guice:                                               "com.google.inject.Guice"
GuiceShiroFilter:                                    "org.apache.shiro.guice.web.GuiceShiroFilter"
InjectorImpl:                                        "com.google.inject.internal.InjectorImpl"
InjectorImpl$1:                                      "com.google.inject.internal.InjectorImpl$1"
InstrumentedHandler:                                 "com.codahale.metrics.jetty9.InstrumentedHandler"
InternalFactoryToProviderAdapter:                    "com.google.inject.internal.InternalFactoryToProviderAdapter"
InternalInjectorCreator:                             "com.google.inject.internal.InternalInjectorCreator"
JettyServer$JettyMainThread:                         "org.sonatype.nexus.bootstrap.jetty.JettyServer$JettyMainThread"
ListenerTracker:                                     "org.sonatype.nexus.bootstrap.osgi.ListenerTracker"
MembersInjectorImpl:                                 "com.google.inject.internal.MembersInjectorImpl"
NexusContextListener:                                "org.sonatype.nexus.extender.NexusContextListener"
NexusContextModule:                                  "org.sonatype.nexus.extender.NexusContextModule"
ProviderToInternalFactoryAdapter:                    "com.google.inject.internal.ProviderToInternalFactoryAdapter"
Server:                                              "org.eclipse.jetty.server.Server"
ServiceBindings:                                     "org.eclipse.sisu.osgi.ServiceBindings"
ServiceTracker:                                      "org.osgi.util.tracker.ServiceTracker"
ServiceTracker$Tracked:                              "org.osgi.util.tracker.ServiceTracker$Tracked"
ServletContextHandler:                               "org.eclipse.jetty.servlet.ServletContextHandler"
ServletHandler:                                      "org.eclipse.jetty.servlet.ServletHandler"
ShiroWebModule:                                      "org.apache.shiro.guice.web.ShiroWebModule"
SingleParameterInjector:                             "com.google.inject.internal.SingleParameterInjector"
SingletonScope$1:                                    "com.google.inject.internal.SingletonScope$1"
WebAppContext:                                       "org.eclipse.jetty.webapp.WebAppContext"
WebEnvironment:                                      "org.apache.shiro.web.env.WebEnvironment"
WebGuiceEnvironment:                                 "org.apache.shiro.guice.web.WebGuiceEnvironment"
WebSecurityModule:                                   "org.sonatype.nexus.security.WebSecurityModule"
WireModule:                                          "org.eclipse.sisu.wire.WireModule"
========================
End of classname legend:
========================

	at com.google.inject.internal.Errors.throwCreationExceptionIfErrorsExist(Errors.java:589)
	at com.google.inject.internal.InternalInjectorCreator.injectDynamically(InternalInjectorCreator.java:190)
	at com.google.inject.internal.InternalInjectorCreator.build(InternalInjectorCreator.java:113)
	at com.google.inject.Guice.createInjector(Guice.java:87)
	at com.google.inject.Guice.createInjector(Guice.java:69)
	at com.google.inject.Guice.createInjector(Guice.java:59)
	at org.sonatype.nexus.extender.NexusContextListener.contextInitialized(NexusContextListener.java:185)
	at org.sonatype.nexus.bootstrap.osgi.ListenerTracker.addingService(ListenerTracker.java:47)
	at org.sonatype.nexus.bootstrap.osgi.ListenerTracker.addingService(ListenerTracker.java:1)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:871)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:183)
	at org.osgi.util.tracker.ServiceTracker.open(ServiceTracker.java:321)
	at org.osgi.util.tracker.ServiceTracker.open(ServiceTracker.java:264)
	at org.sonatype.nexus.bootstrap.osgi.BootstrapListener.contextInitialized(BootstrapListener.java:76)
	at org.eclipse.jetty.server.handler.ContextHandler.callContextInitialized(ContextHandler.java:1073)
	at org.eclipse.jetty.servlet.ServletContextHandler.callContextInitialized(ServletContextHandler.java:572)
	at org.eclipse.jetty.server.handler.ContextHandler.contextInitialized(ContextHandler.java:1002)
	at org.eclipse.jetty.servlet.ServletHandler.initialize(ServletHandler.java:765)
	at org.eclipse.jetty.servlet.ServletContextHandler.startContext(ServletContextHandler.java:379)
	at org.eclipse.jetty.webapp.WebAppContext.startWebapp(WebAppContext.java:1449)
	at org.eclipse.jetty.webapp.WebAppContext.startContext(WebAppContext.java:1414)
	at org.eclipse.jetty.server.handler.ContextHandler.doStart(ContextHandler.java:916)
	at org.eclipse.jetty.servlet.ServletContextHandler.doStart(ServletContextHandler.java:288)
	at org.eclipse.jetty.webapp.WebAppContext.doStart(WebAppContext.java:524)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:97)
	at com.codahale.metrics.jetty9.InstrumentedHandler.doStart(InstrumentedHandler.java:101)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:117)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:97)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.server.Server.start(Server.java:423)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:97)
	at org.eclipse.jetty.server.Server.doStart(Server.java:387)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:73)
	at org.sonatype.nexus.bootstrap.jetty.JettyServer$JettyMainThread.run(JettyServer.java:274)
	Suppressed: java.lang.InterruptedException: null
		at java.lang.Object.wait(Native Method)
		at java.lang.Thread.join(Thread.java:1265)
		at org.eclipse.jetty.util.thread.QueuedThreadPool.joinThreads(QueuedThreadPool.java:320)
		at org.eclipse.jetty.util.thread.QueuedThreadPool.doStop(QueuedThreadPool.java:244)
		at org.eclipse.jetty.util.component.AbstractLifeCycle.stop(AbstractLifeCycle.java:94)
		at org.eclipse.jetty.util.component.ContainerLifeCycle.stop(ContainerLifeCycle.java:180)
		at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:147)
		... 4 common frames omitted
2025-07-02 14:19:36,203+0800 INFO  [FelixStartLevel]  *SYSTEM org.eclipse.jetty.server.handler.ContextHandler - Stopped o.e.j.w.WebAppContext@6e3d8067{Sonatype Nexus,/,null,STOPPED}
2025-07-02 14:19:36,204+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Stopped
2025-07-02 14:19:36,701+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.features.internal.FeaturesWrapper - Fast FeaturesService stopping
2025-07-02 14:22:55,640+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.pax.logging.NexusLogActivator - start
2025-07-02 14:22:58,632+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.features.internal.FeaturesWrapper - Fast FeaturesService starting
2025-07-02 14:23:04,934+0800 INFO  [FelixStartLevel]  *SYSTEM ROOT - bundle org.apache.felix.scr:2.1.30 (56) Starting with globalExtender setting: false
2025-07-02 14:23:04,951+0800 INFO  [FelixStartLevel]  *SYSTEM ROOT - bundle org.apache.felix.scr:2.1.30 (56)  Version = 2.1.30
2025-07-02 14:23:06,844+0800 WARN  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4JInitialiser - Your logging framework class org.ops4j.pax.logging.slf4j.Slf4jLogger is not known - if it needs access to the standard println methods on the console you will need to register it by calling registerLoggingSystemPackage
2025-07-02 14:23:06,849+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Package org.ops4j.pax.logging.slf4j registered; all classes within it or subpackages of it will be allowed to print to System.out and System.err
2025-07-02 14:23:06,920+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Replaced standard System.out and System.err PrintStreams with SLF4JPrintStreams
2025-07-02 14:23:06,924+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Redirected System.out and System.err to SLF4J for this context
2025-07-02 14:23:06,942+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder - Properties:
2025-07-02 14:23:06,943+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   application-host='0.0.0.0'
2025-07-02 14:23:06,943+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   application-port='8081'
2025-07-02 14:23:06,944+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   fabric.etc='/opt/sonatype/nexus/etc/fabric'
2025-07-02 14:23:06,944+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   jetty.etc='/opt/sonatype/nexus/etc/jetty'
2025-07-02 14:23:06,944+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.base='/opt/sonatype/nexus'
2025-07-02 14:23:06,944+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.data='/nexus-data'
2025-07-02 14:23:06,945+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.etc='/opt/sonatype/nexus/etc/karaf'
2025-07-02 14:23:06,945+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.home='/opt/sonatype/nexus'
2025-07-02 14:23:06,945+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.instances='/nexus-data/instances'
2025-07-02 14:23:06,946+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   logback.etc='/opt/sonatype/nexus/etc/logback'
2025-07-02 14:23:06,946+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-args='/opt/sonatype/nexus/etc/jetty/jetty.xml,/opt/sonatype/nexus/etc/jetty/jetty-http.xml,/opt/sonatype/nexus/etc/jetty/jetty-requestlog.xml'
2025-07-02 14:23:07,014+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-context-path='/'
2025-07-02 14:23:07,018+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-edition='nexus-pro-edition'
2025-07-02 14:23:07,021+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-features='nexus-pro-feature'
2025-07-02 14:23:07,021+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus.hazelcast.discovery.isEnabled='true'
2025-07-02 14:23:07,022+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   ssl.etc='/opt/sonatype/nexus/etc/ssl'
2025-07-02 14:23:07,024+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - Java: 1.8.0_442, OpenJDK 64-Bit Server VM, Red Hat, Inc., 25.442-b06
2025-07-02 14:23:07,025+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - OS: Linux, 6.10.14-linuxkit, amd64
2025-07-02 14:23:07,025+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - User: nexus, en, /opt/sonatype/nexus
2025-07-02 14:23:07,025+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - CWD: /opt/sonatype/nexus
2025-07-02 14:23:07,033+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - TMP: /nexus-data/tmp
2025-07-02 14:23:07,127+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Starting
2025-07-02 14:23:07,137+0800 INFO  [FelixStartLevel]  *SYSTEM org.eclipse.jetty.util.log - Logging initialized @22971ms to org.eclipse.jetty.util.log.Slf4jLog
2025-07-02 14:23:07,144+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty.xml
2025-07-02 14:23:08,125+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty-http.xml
2025-07-02 14:23:08,321+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty-requestlog.xml
2025-07-02 14:23:08,421+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Starting: Server@7711fd8d{STOPPED}[9.4.53.v20231009]
2025-07-02 14:23:08,524+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.Server - jetty-9.4.53.v20231009; built: 2023-10-09T12:29:09.265Z; git: 27bde00a0b95a1d5bbee0eae7984f891d2d0f8c9; jvm 1.8.0_442-b06
2025-07-02 14:23:09,114+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - DefaultSessionIdManager workerName=node0
2025-07-02 14:23:09,118+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - No SessionScavenger set, using defaults
2025-07-02 14:23:09,130+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - node0 Scavenging every 660000ms
2025-07-02 14:23:09,226+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Initializing
2025-07-02 14:23:09,414+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.OssNexusEdition - Loading OSS Edition
2025-07-02 14:23:09,431+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Installing: nexus-oss-edition/********* (nexus-orient/*********)
2025-07-02 14:23:25,934+0800 INFO  [jetty-main-1]  *SYSTEM org.ehcache.core.osgi.EhcacheActivator - Detected OSGi Environment (core is in bundle: org.ehcache [152]): Using OSGi Based Service Loading
2025-07-02 14:23:28,426+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Installed: nexus-oss-edition/********* (nexus-orient/*********)
2025-07-02 14:23:31,644+0800 INFO  [jetty-main-1]  *SYSTEM org.apache.shiro.nexus.NexusWebSessionManager - Global session timeout: 1800000 ms
2025-07-02 14:23:31,649+0800 INFO  [jetty-main-1]  *SYSTEM org.apache.shiro.nexus.NexusWebSessionManager - Session-cookie prototype: name=NXSESSIONID, secure=true
2025-07-02 14:23:31,919+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.common [*********]
2025-07-02 14:23:32,315+0800 INFO  [jetty-main-1]  *SYSTEM org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-07-02 14:23:32,829+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.common [*********]
2025-07-02 14:23:32,830+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.hibernate.validator [6.2.0.Final]
2025-07-02 14:23:33,122+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.hibernate.validator [6.2.0.Final]
2025-07-02 14:23:33,125+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore-api [*********]
2025-07-02 14:23:33,330+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore-api [*********]
2025-07-02 14:23:33,332+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cache [*********]
2025-07-02 14:23:33,734+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cache [*********]
2025-07-02 14:23:33,736+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.jmx [*********]
2025-07-02 14:23:34,214+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.jmx [*********]
2025-07-02 14:23:34,217+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.crypto [*********]
2025-07-02 14:23:35,336+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.crypto [*********]
2025-07-02 14:23:35,340+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.security [*********]
2025-07-02 14:23:36,340+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.security [*********]
2025-07-02 14:23:36,341+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.datastore [*********]
2025-07-02 14:23:36,737+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.datastore [*********]
2025-07-02 14:23:36,738+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.supportzip-api [*********]
2025-07-02 14:23:36,836+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.supportzip-api [*********]
2025-07-02 14:23:36,838+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.thread [*********]
2025-07-02 14:23:36,935+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.thread [*********]
2025-07-02 14:23:36,936+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.scheduling [*********]
2025-07-02 14:23:37,322+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.scheduling [*********]
2025-07-02 14:23:37,323+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore [*********]
2025-07-02 14:23:37,630+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore [*********]
2025-07-02 14:23:37,631+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.apache.tika.core [1.28.4]
2025-07-02 14:23:37,818+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.apache.tika.core [1.28.4]
2025-07-02 14:23:37,821+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.orient [*********]
2025-07-02 14:23:38,414+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.orient [*********]
2025-07-02 14:23:38,415+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.upgrade [*********]
2025-07-02 14:23:38,617+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.upgrade [*********]
2025-07-02 14:23:38,618+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore-file [*********]
2025-07-02 14:23:38,921+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore-file [*********]
2025-07-02 14:23:38,922+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.capability [*********]
2025-07-02 14:23:39,120+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.capability [*********]
2025-07-02 14:23:39,121+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.commands [*********]
2025-07-02 14:23:39,223+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.commands [*********]
2025-07-02 14:23:39,224+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.email [*********]
2025-07-02 14:23:39,324+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.email [*********]
2025-07-02 14:23:39,325+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.httpclient [*********]
2025-07-02 14:23:39,435+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.httpclient [*********]
2025-07-02 14:23:39,437+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.servlet [*********]
2025-07-02 14:23:39,624+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.servlet [*********]
2025-07-02 14:23:39,626+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.ssl [*********]
2025-07-02 14:23:39,718+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.ssl [*********]
2025-07-02 14:23:39,719+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.base [*********]
2025-07-02 14:23:40,025+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.metrics.MetricsModule - Metrics support configured
2025-07-02 14:23:40,121+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.metrics.MetricsModule - Metrics support configured
2025-07-02 14:23:43,536+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.base [*********]
2025-07-02 14:23:43,615+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.extdirect [*********]
2025-07-02 14:23:44,120+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.extdirect [*********]
2025-07-02 14:23:44,122+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.siesta [*********]
2025-07-02 14:23:44,414+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.siesta [*********]
2025-07-02 14:23:44,414+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rest-jackson2 [*********]
2025-07-02 14:23:44,432+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rest-jackson2 [*********]
2025-07-02 14:23:44,433+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.swagger [*********]
2025-07-02 14:23:44,618+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.swagger [*********]
2025-07-02 14:23:44,619+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rapture [*********]
2025-07-02 14:23:45,630+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rapture [*********]
2025-07-02 14:23:45,632+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.datastore-mybatis [*********]
2025-07-02 14:23:45,843+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.datastore-mybatis [*********]
2025-07-02 14:23:45,913+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.quartz [*********]
2025-07-02 14:23:46,217+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.quartz [*********]
2025-07-02 14:23:46,218+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.oss-edition [*********]
2025-07-02 14:23:46,314+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.oss-edition [*********]
2025-07-02 14:23:46,317+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Running lifecycle phases [KERNEL, STORAGE, RESTORE, UPGRADE, SCHEMAS, EVENTS, SECURITY, SERVICES, REPOSITORIES, CAPABILITIES, TASKS]
2025-07-02 14:23:46,318+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start KERNEL
2025-07-02 14:23:46,320+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.log.overrides.file.LogbackLoggerOverrides - File: /nexus-data/etc/logback/logback-overrides.xml
2025-07-02 14:23:46,321+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.log.LogbackLogManager - Configuring
2025-07-02 14:23:46,421+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Installing: [nexus-oss-feature/*********, nexus-cma-extra/*********, nexus-ossindex-plugin/*********]
2025-07-02 14:24:21,353+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Installed: [nexus-oss-feature/*********, nexus-cma-extra/*********, nexus-ossindex-plugin/*********]
2025-07-02 14:24:21,536+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-audit-plugin [*********]
2025-07-02 14:24:22,441+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-audit-plugin [*********]
2025-07-02 14:24:22,634+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-ssl-plugin [*********]
2025-07-02 14:24:23,326+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-ssl-plugin [*********]
2025-07-02 14:24:26,823+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.selector [*********]
2025-07-02 14:24:27,831+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.selector [*********]
2025-07-02 14:24:27,923+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.elasticsearch [*********]
2025-07-02 14:24:28,135+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.elasticsearch [*********]
2025-07-02 14:24:28,216+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.repository-content [*********]
2025-07-02 14:24:30,820+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.repository-content [*********]
2025-07-02 14:24:30,835+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.repository-config [*********]
2025-07-02 14:24:31,136+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.repository-config [*********]
2025-07-02 14:24:31,138+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-coreui-plugin [*********]
2025-07-02 14:24:31,733+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-coreui-plugin [*********]
2025-07-02 14:24:31,929+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-httpbridge [*********]
2025-07-02 14:24:32,131+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-httpbridge [*********]
2025-07-02 14:24:34,117+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-blobstore-tasks [*********]
2025-07-02 14:24:34,524+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-blobstore-tasks [*********]
2025-07-02 14:24:34,532+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cleanup-config [*********]
2025-07-02 14:24:34,723+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cleanup-config [*********]
2025-07-02 14:24:34,725+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.core [*********]
2025-07-02 14:24:36,528+0800 ERROR [Thread-77]  *SYSTEM org.sonatype.nexus.internal.node.orient.OrientLocalNodeAccess - Failed to determine hostname, using nodeId instead.
2025-07-02 14:24:36,830+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.core [*********]
2025-07-02 14:24:36,919+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-maven [*********]
2025-07-02 14:24:37,714+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-maven [*********]
2025-07-02 14:24:37,717+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-script-plugin [*********]
2025-07-02 14:24:38,030+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-script-plugin [*********]
2025-07-02 14:24:38,219+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-task-log-cleanup [*********]
2025-07-02 14:24:38,326+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-task-log-cleanup [*********]
2025-07-02 14:24:38,559+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-blobstore-s3 [*********]
2025-07-02 14:24:39,121+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-blobstore-s3 [*********]
2025-07-02 14:24:39,129+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-onboarding-plugin [*********]
2025-07-02 14:24:39,217+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-onboarding-plugin [*********]
2025-07-02 14:24:39,223+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-default-role-plugin [*********]
2025-07-02 14:24:39,327+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-default-role-plugin [*********]
2025-07-02 14:24:39,424+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-apt [*********]
2025-07-02 14:24:39,822+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-apt [*********]
2025-07-02 14:24:40,822+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-raw [*********]
2025-07-02 14:24:41,136+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-raw [*********]
2025-07-02 14:24:41,327+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cleanup [*********]
2025-07-02 14:24:41,618+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cleanup [*********]
2025-07-02 14:24:42,231+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ldap-plugin [*********]
2025-07-02 14:24:43,327+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ldap-plugin [*********]
2025-07-02 14:24:44,530+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-proui-plugin [*********]
2025-07-02 14:24:44,850+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-proui-plugin [*********]
2025-07-02 14:24:44,920+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-proximanova-plugin [*********]
2025-07-02 14:24:44,932+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-proximanova-plugin [*********]
2025-07-02 14:24:46,127+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_insight_scan_insight-scanner-core_2.36.66-01_insight-scanner-core-2.36.66-01.jar [0.0.0]
2025-07-02 14:24:46,234+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_insight_scan_insight-scanner-core_2.36.66-01_insight-scanner-core-2.36.66-01.jar [0.0.0]
2025-07-02 14:24:46,320+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_insight_scan_insight-scanner-model-io_2.36.66-01_insight-scanner-model-io-2.36.66-01.jar [0.0.0]
2025-07-02 14:24:46,334+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_insight_scan_insight-scanner-model-io_2.36.66-01_insight-scanner-model-io-2.36.66-01.jar [0.0.0]
2025-07-02 14:24:46,336+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-healthcheck-base [*********]
2025-07-02 14:24:51,416+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-healthcheck-base [*********]
2025-07-02 14:24:51,436+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_licensing_license-bundle_1.6.0_license-bundle-1.6.0.jar [0.0.0]
2025-07-02 14:24:52,029+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_licensing_license-bundle_1.6.0_license-bundle-1.6.0.jar [0.0.0]
2025-07-02 14:24:52,040+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.licensing-extension [*********]
2025-07-02 14:24:52,332+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.licensing-extension [*********]
2025-07-02 14:24:52,334+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-analytics-plugin [*********]
2025-07-02 14:24:52,725+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-analytics-plugin [*********]
2025-07-02 14:24:52,727+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-licensing-plugin [*********]
2025-07-02 14:24:52,836+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-licensing-plugin [*********]
2025-07-02 14:24:53,250+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-npm [*********]
2025-07-02 14:24:53,818+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-npm [*********]
2025-07-02 14:24:53,821+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-nuget [*********]
2025-07-02 14:24:54,829+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-nuget [*********]
2025-07-02 14:24:54,831+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-rubygems [*********]
2025-07-02 14:24:55,131+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-rubygems [*********]
2025-07-02 14:24:55,133+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rest-client [*********]
2025-07-02 14:24:55,225+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rest-client [*********]
2025-07-02 14:24:55,227+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-migration-plugin [*********]
2025-07-02 14:24:55,619+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-migration-plugin [*********]
2025-07-02 14:24:55,924+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-vulnerability-plugin [*********]
2025-07-02 14:24:56,615+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-vulnerability-plugin [*********]
2025-07-02 14:24:56,627+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-outreach-plugin [*********]
2025-07-02 14:24:56,837+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-outreach-plugin [*********]
2025-07-02 14:24:57,127+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-rutauth-plugin [*********]
2025-07-02 14:24:57,731+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-rutauth-plugin [*********]
2025-07-02 14:24:57,915+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-clm-oss-plugin [*********]
2025-07-02 14:24:57,940+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-clm-oss-plugin [*********]
2025-07-02 14:24:58,038+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-docker [*********]
2025-07-02 14:24:59,525+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-docker [*********]
2025-07-02 14:24:59,723+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-yum [*********]
2025-07-02 14:25:00,029+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-yum [*********]
2025-07-02 14:25:00,217+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING nexus-blobstore-azure-cloud [*********]
2025-07-02 14:25:00,334+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED nexus-blobstore-azure-cloud [*********]
2025-07-02 14:25:00,614+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ahc-plugin [*********]
2025-07-02 14:25:00,728+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ahc-plugin [*********]
2025-07-02 14:25:01,315+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-helm [*********]
2025-07-02 14:25:01,517+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-helm [*********]
2025-07-02 14:25:01,526+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-gitlfs [*********]
2025-07-02 14:25:01,621+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-gitlfs [*********]
2025-07-02 14:25:01,622+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-pypi [*********]
2025-07-02 14:25:01,922+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-pypi [*********]
2025-07-02 14:25:01,932+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-conda [*********]
2025-07-02 14:25:02,031+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-conda [*********]
2025-07-02 14:25:02,121+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-conan [*********]
2025-07-02 14:25:02,331+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-conan [*********]
2025-07-02 14:25:02,422+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-r [*********]
2025-07-02 14:25:02,625+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-r [*********]
2025-07-02 14:25:02,634+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-cocoapods [*********]
2025-07-02 14:25:02,736+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-cocoapods [*********]
2025-07-02 14:25:02,823+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-golang [*********]
2025-07-02 14:25:02,932+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-golang [*********]
2025-07-02 14:25:03,017+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-p2 [*********]
2025-07-02 14:25:03,129+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-p2 [*********]
2025-07-02 14:25:03,523+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-bower [*********]
2025-07-02 14:25:03,726+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-bower [*********]
2025-07-02 14:25:03,828+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ossindex-plugin [*********]
2025-07-02 14:25:03,926+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ossindex-plugin [*********]
2025-07-02 14:25:04,029+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start STORAGE
2025-07-02 14:25:04,139+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.node.orient.OrientLocalNodeAccess - ID: E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A
2025-07-02 14:25:06,617+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - OrientDB version: 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 14:25:06,656+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server v2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x) is starting up...
2025-07-02 14:25:06,722+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Databases directory: /nexus-data/db
2025-07-02 14:25:08,119+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Configuration of usage of soft references inside of containers of results of SQL execution
2025-07-02 14:25:08,134+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Initial and maximum values of heap memory usage are NOT equal, containers of results of SQL executors will NOT use soft references by default
2025-07-02 14:25:08,218+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Auto configuration of disk cache size.
2025-07-02 14:25:09,123+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - 2684354560 B/2560 MB/2 GB of physical memory were detected on machine
2025-07-02 14:25:09,221+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Soft memory limit for this process is set to -1 B/-1 MB/-1 GB
2025-07-02 14:25:09,223+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Hard memory limit for this process is set to -1 B/-1 MB/-1 GB
2025-07-02 14:25:09,227+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Detected memory limit for current process is 2684354560 B/2560 MB/2 GB
2025-07-02 14:25:09,321+0800 WARN  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Not enough physical memory available for DISKCACHE: 2,560MB (heap=1,536MB direct=512MB). Set lower Maximum Heap (-Xmx setting on JVM) and restart OrientDB. Now running with DISKCACHE=256MB
2025-07-02 14:25:09,323+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - OrientDB config DISKCACHE=256MB (heap=1,536MB direct=512MB os=2,560MB)
2025-07-02 14:25:10,124+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Found ORIENTDB_ROOT_PASSWORD variable, using this value as root's password
2025-07-02 14:25:11,227+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.handler.OJMXPlugin - JMX plugin installed and active: profilerManaged=true
2025-07-02 14:25:11,318+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Studio available at $ANSI{blue http://localhost:2480/studio/index.html}
2025-07-02 14:25:11,320+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - $ANSI{green:italic OrientDB Server is active} v2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x).
2025-07-02 14:25:11,321+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - OrientDB Studio has been deprecated and it's no longer available
2025-07-02 14:25:11,321+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - Activated
2025-07-02 14:25:12,327+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start RESTORE
2025-07-02 14:25:13,719+0800 INFO  [ForkJoinPool.commonPool-worker-1]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/component' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 14:25:15,516+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/security' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 14:25:15,747+0800 INFO  [ForkJoinPool.commonPool-worker-1]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/config' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 14:25:16,460+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/analytics' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 14:25:17,015+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start UPGRADE
2025-07-02 14:25:19,620+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SCHEMAS
2025-07-02 14:25:19,720+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool config with per-core limit of 16
2025-07-02 14:25:20,217+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start EVENTS
2025-07-02 14:25:20,923+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SECURITY
2025-07-02 14:25:20,926+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.security.internal.DefaultSecuritySystem - Unlimited strength JCE policy detected
2025-07-02 14:25:20,928+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool security with per-core limit of 16
2025-07-02 14:25:20,936+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.security.internal.RealmManagerImpl - Using default configuration: OrientRealmConfiguration{realmNames=[NexusAuthenticatingRealm, NexusAuthorizingRealm]}
2025-07-02 14:25:20,945+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.RuntimeCacheManagerProvider - Cache-provider: ehcache
2025-07-02 14:25:20,951+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.ehcache.EhCacheManagerProvider - Creating cache-manager with configuration: file:/opt/sonatype/nexus/etc/fabric/ehcache.xml
2025-07-02 14:25:22,941+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache NexusAuthenticatingRealm.authenticationCache will be supplemented by template nexus-default
2025-07-02 14:25:23,217+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthenticatingRealm.authenticationCache' created in EhcacheManager.
2025-07-02 14:25:23,230+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthenticatingRealm.authenticationCache
2025-07-02 14:25:23,237+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthenticatingRealm.authenticationCache
2025-07-02 14:25:23,321+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache NexusAuthorizingRealm.authorizationCache will be supplemented by template nexus-default
2025-07-02 14:25:23,325+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthorizingRealm.authorizationCache' created in EhcacheManager.
2025-07-02 14:25:23,327+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthorizingRealm.authorizationCache
2025-07-02 14:25:23,327+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthorizingRealm.authorizationCache
2025-07-02 14:25:23,339+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SERVICES
2025-07-02 14:25:23,431+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.httpclient.HttpClientManagerImpl - Using default configuration: OrientHttpClientConfiguration{connection=null, proxy=null, authentication=null}
2025-07-02 14:25:23,617+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool analytics with per-core limit of 16
2025-07-02 14:25:24,321+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache SYSTEM_INFORMATION will be supplemented by template nexus-default
2025-07-02 14:25:24,420+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' created in EhcacheManager.
2025-07-02 14:25:24,423+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=SYSTEM_INFORMATION
2025-07-02 14:25:24,424+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=SYSTEM_INFORMATION
2025-07-02 14:25:24,438+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Detected 2 engine-factories
2025-07-02 14:25:24,517+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Engine-factory: Oracle Nashorn v1.8.0_442; language=ECMAScript, version=ECMA - 262 Edition 5.1, names=[nashorn, Nashorn, js, JS, JavaScript, javascript, ECMAScript, ecmascript], mime-types=[application/javascript, application/ecmascript, text/javascript, text/ecmascript], extensions=[js]
2025-07-02 14:25:24,525+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Engine-factory: Groovy Scripting Engine v2.0; language=Groovy, version=3.0.19, names=[groovy, Groovy], mime-types=[application/x-groovy], extensions=[groovy]
2025-07-02 14:25:24,525+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Default language: groovy
2025-07-02 14:25:24,542+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.elasticsearch.internal.NodeProvider - Creating node with config: /opt/sonatype/nexus/etc/fabric/elasticsearch.yml
2025-07-02 14:25:25,226+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] version[2.4.3], pid[1], build[d38a34e/2016-12-07T16:28:56Z]
2025-07-02 14:25:25,228+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] initializing ...
2025-07-02 14:25:25,232+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.plugins - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] modules [], plugins [content-auth-plugin], sites []
2025-07-02 14:25:25,251+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.env - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] using [1] data paths, mounts [[/nexus-data (/run/host_mark/Users)]], net usable_space [626.2gb], net total_space [926.3gb], spins? [possibly], types [fakeowner]
2025-07-02 14:25:25,253+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.env - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] heap size [1.5gb], compressed ordinary object pointers [true]
2025-07-02 14:25:29,629+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] initialized
2025-07-02 14:25:29,636+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] starting ...
2025-07-02 14:25:29,650+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.transport - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] publish_address {local[1]}, bound_addresses {local[1]}
2025-07-02 14:25:29,655+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.discovery - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] nexus/Wl9zwjovTkGlDk23rdYF4Q
2025-07-02 14:25:29,720+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.cluster.service - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] new_master {E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A}{Wl9zwjovTkGlDk23rdYF4Q}{local}{local[1]}{local=true, master=true}, reason: local-disco-initial_connect(master)
2025-07-02 14:25:29,728+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] started
2025-07-02 14:25:30,321+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.gateway - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] recovered [5] indices into cluster_state
2025-07-02 14:25:34,327+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.cluster.routing.allocation - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] Cluster health status changed from [RED] to [GREEN] (reason: [shards started [[2e9a1e67e8a325bcd6ee9f6790ff6c769e791d56][0], [2e9a1e67e8a325bcd6ee9f6790ff6c769e791d56][0]] ...]).
2025-07-02 14:25:34,431+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool component with per-core limit of 16
2025-07-02 14:25:34,537+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.QuartzSchedulerProvider - Thread-pool size: 20, Thread-pool priority: 5
2025-07-02 14:25:34,558+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.JobStoreImpl - Instance name: nexus; ID: E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A
2025-07-02 14:25:34,559+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.JobStoreImpl - Initialized
2025-07-02 14:25:34,625+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.QuartzSchedulerProvider - Quartz Scheduler v2.3.2
2025-07-02 14:25:35,837+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Task log cleanup' [tasklog.cleanup] : state=WAITING
2025-07-02 14:25:35,844+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] : state=WAITING
2025-07-02 14:25:35,915+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Cleanup service' [repository.cleanup] : state=WAITING
2025-07-02 14:25:35,916+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] : state=WAITING
2025-07-02 14:25:37,321+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start REPOSITORIES
2025-07-02 14:25:38,038+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.blobstore.file.internal.OrientFileBlobStoreMetricsStore - Loading blob store metrics file /nexus-data/blobs/default/E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A-metrics.properties null
2025-07-02 14:25:39,836+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetHostedGalleryFacet - Enabled NuGet feed cooperation for nuget-hosted
2025-07-02 14:25:40,537+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository nuget-group set to Ready to Connect.
2025-07-02 14:25:40,613+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetGroupGalleryFacet - Enabled NuGet feed cooperation for nuget-group
2025-07-02 14:25:40,616+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget-group#odata-query-cache will be supplemented by template nexus-default
2025-07-02 14:25:40,622+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget-group#odata-query-cache' created in EhcacheManager.
2025-07-02 14:25:40,623+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget-group#odata-query-cache
2025-07-02 14:25:40,624+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget-group#odata-query-cache
2025-07-02 14:25:41,319+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository nuget.org-proxy set to Ready to Connect.
2025-07-02 14:25:41,321+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget.org-proxy#negative-cache will be supplemented by template nexus-default
2025-07-02 14:25:41,324+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#negative-cache' created in EhcacheManager.
2025-07-02 14:25:41,327+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#negative-cache
2025-07-02 14:25:41,327+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#negative-cache
2025-07-02 14:25:41,328+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetProxyGalleryFacetImpl - Enabled NuGet feed cooperation for nuget.org-proxy
2025-07-02 14:25:41,329+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget.org-proxy#odata-query-cache will be supplemented by template nexus-default
2025-07-02 14:25:41,333+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#odata-query-cache' created in EhcacheManager.
2025-07-02 14:25:41,335+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#odata-query-cache
2025-07-02 14:25:41,335+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#odata-query-cache
2025-07-02 14:25:41,615+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository maven-central set to Ready to Connect.
2025-07-02 14:25:41,616+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache maven-central#negative-cache will be supplemented by template nexus-default
2025-07-02 14:25:41,618+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'maven-central#negative-cache' created in EhcacheManager.
2025-07-02 14:25:41,620+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=maven-central#negative-cache
2025-07-02 14:25:41,621+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=maven-central#negative-cache
2025-07-02 14:25:41,719+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start CAPABILITIES
2025-07-02 14:25:43,120+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Initialized
2025-07-02 14:25:43,122+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start TASKS
2025-07-02 14:25:44,125+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] removed
2025-07-02 14:25:44,333+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] : state=WAITING
2025-07-02 14:25:44,619+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.scheduling.TaskSchedulerImpl - Task 'Metric aggregation' [content.usage.aggregation] scheduled: cron
2025-07-02 14:25:44,624+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] runNow
2025-07-02 14:25:44,625+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change WAITING -> RUNNING
2025-07-02 14:25:45,120+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] removed
2025-07-02 14:25:45,129+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] : state=WAITING
2025-07-02 14:25:45,136+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.scheduling.TaskSchedulerImpl - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] scheduled: cron
2025-07-02 14:25:45,226+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.search.elasticsearch.IndexStartupRebuildManager - Skipping rebuild of repository indexes
2025-07-02 14:25:45,422+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.OrientQuartzSchedulerSPI - Scheduler put into ready mode
2025-07-02 14:25:47,016+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle - UI plugin descriptors:
2025-07-02 14:25:47,439+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rapture
2025-07-02 14:25:47,730+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-coreui-plugin
2025-07-02 14:25:47,748+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle - ExtJS UI plugin descriptors:
2025-07-02 14:25:47,815+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rapture
2025-07-02 14:25:47,817+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-blobstore-s3
2025-07-02 14:25:47,818+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-proximanova-plugin
2025-07-02 14:25:47,819+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rutauth-plugin
2025-07-02 14:25:47,819+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-blobstore-azure-cloud
2025-07-02 14:25:47,820+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-coreui-plugin
2025-07-02 14:25:47,822+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-proui-plugin
2025-07-02 14:25:47,825+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-maven
2025-07-02 14:25:47,826+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-onboarding-plugin
2025-07-02 14:25:47,826+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-npm
2025-07-02 14:25:47,827+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-nuget
2025-07-02 14:25:47,827+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-rubygems
2025-07-02 14:25:47,828+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-docker
2025-07-02 14:25:47,828+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-pypi
2025-07-02 14:25:47,828+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-analytics-plugin
2025-07-02 14:25:47,939+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.webresources.WebResourceServlet - Max-age: 30 days (2592000 seconds)
2025-07-02 14:25:48,418+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.servlet.DirectJNgineServlet - Servlet GLOBAL configuration: debug=false, providersUrl=service/extdirect, minify=false, batchRequestsMultithreadingEnabled=true, batchRequestsMinThreadsPoolSize=16, batchRequestsMaxThreadsPoolSize=80, batchRequestsMaxThreadsPerRequest=8, batchRequestsMaxThreadKeepAliveSeconds=60, gsonBuilderConfiguratorClass=org.sonatype.nexus.extdirect.internal.ExtDirectGsonBuilderConfigurator, dispatcherClass=com.softwarementors.extjs.djn.servlet.ssm.SsmDispatcher, jsonRequestProcessorThreadClass=org.sonatype.nexus.extdirect.internal.ExtDirectJsonRequestProcessorThread, contextPath=--not specified: calculated via Javascript--, createSourceFiles=true
2025-07-02 14:25:48,423+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.servlet.DirectJNgineServlet - Servlet GLOBAL configuration: registryConfiguratorClass=
2025-07-02 14:25:48,629+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.jscodegen.CodeFileGenerator - Creating source files for APIs...
2025-07-02 14:25:52,124+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - JAX-RS RuntimeDelegate: org.sonatype.nexus.siesta.internal.resteasy.SisuResteasyProviderFactory@42b5f1a7
2025-07-02 14:25:53,118+0800 INFO  [jetty-main-1]  *SYSTEM org.jboss.resteasy.plugins.validation.i18n - RESTEASY008550: Unable to find CDI supporting ValidatorFactory. Using default ValidatorFactory
2025-07-02 14:25:58,527+0800 INFO  [quartz-10-thread-1]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change RUNNING -> WAITING (OK)
2025-07-02 14:26:09,133+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - Initialized
2025-07-02 14:26:09,327+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.repository.httpbridge.internal.ViewServlet - Initialized
2025-07-02 14:26:10,038+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.handler.ContextHandler - Started o.e.j.w.WebAppContext@39ff742b{Sonatype Nexus,/,null,AVAILABLE}
2025-07-02 14:26:11,117+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.AbstractConnector - Started ServerConnector@c06b8d9{HTTP/1.1, (http/1.1)}{0.0.0.0:8081}
2025-07-02 14:26:11,122+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.Server - Started @206957ms
2025-07-02 14:26:11,123+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - 
-------------------------------------------------

Started Sonatype Nexus OSS 3.70.4-02

-------------------------------------------------
2025-07-02 14:26:13,331+0800 INFO  [qtp778203830-119]  *UNKNOWN org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Enabling session validation scheduler...
2025-07-02 14:26:13,439+0800 INFO  [qtp778203830-119]  *UNKNOWN org.sonatype.nexus.internal.security.anonymous.AnonymousManagerImpl - Using default configuration: OrientAnonymousConfiguration{enabled=true, userId='anonymous', realmName='NexusAuthorizingRealm'}
2025-07-02 14:30:00,043+0800 INFO  [quartz-10-thread-2]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 14:30:00,220+0800 INFO  [quartz-10-thread-2]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 14:31:57,819+0800 WARN  [SIGTERM handler]  *SYSTEM com.orientechnologies.orient.core.OSignalHandler - Received signal: SIGTERM
2025-07-02 14:31:58,219+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpbridge.internal.ViewServlet - Destroyed
2025-07-02 14:31:58,222+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - Destroyed
2025-07-02 14:31:58,318+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Uptime: 9 minutes, 14 seconds and 61 milliseconds (nexus-oss-edition/*********)
2025-07-02 14:31:58,319+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Shutting down
2025-07-02 14:31:58,326+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop TASKS
2025-07-02 14:31:58,331+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.OrientQuartzSchedulerSPI - Scheduler put into stand-by mode
2025-07-02 14:31:58,419+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop CAPABILITIES
2025-07-02 14:31:58,528+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop REPOSITORIES
2025-07-02 14:31:58,629+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SERVICES
2025-07-02 14:31:59,217+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' removed from EhcacheManager.
2025-07-02 14:31:59,227+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' successfully destroyed in EhcacheManager.
2025-07-02 14:31:59,239+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SECURITY
2025-07-02 14:31:59,322+0800 INFO  [FelixStartLevel]  *SYSTEM org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Disabled session validation scheduler.
2025-07-02 14:31:59,323+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop EVENTS
2025-07-02 14:31:59,323+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SCHEMAS
2025-07-02 14:31:59,420+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop UPGRADE
2025-07-02 14:31:59,422+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop RESTORE
2025-07-02 14:31:59,423+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop STORAGE
2025-07-02 14:31:59,429+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget-group#odata-query-cache' removed from EhcacheManager.
2025-07-02 14:31:59,430+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthenticatingRealm.authenticationCache' removed from EhcacheManager.
2025-07-02 14:31:59,517+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthorizingRealm.authorizationCache' removed from EhcacheManager.
2025-07-02 14:31:59,520+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'maven-central#negative-cache' removed from EhcacheManager.
2025-07-02 14:31:59,520+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#odata-query-cache' removed from EhcacheManager.
2025-07-02 14:31:59,521+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#negative-cache' removed from EhcacheManager.
2025-07-02 14:31:59,822+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.ehcache.EhCacheManagerProvider - Cache-manager closed
2025-07-02 14:31:59,827+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] stopping ...
2025-07-02 14:32:00,155+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] stopped
2025-07-02 14:32:00,220+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] closing ...
2025-07-02 14:32:00,231+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] closed
2025-07-02 14:32:00,433+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping 4 pools
2025-07-02 14:32:00,436+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: analytics
2025-07-02 14:32:00,518+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: security
2025-07-02 14:32:00,518+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: component
2025-07-02 14:32:00,519+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: config
2025-07-02 14:32:00,521+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server is shutting down...
2025-07-02 14:32:00,522+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Shutting down protocols
2025-07-02 14:32:00,526+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.plugin.OServerPluginManager - Shutting down plugins:
2025-07-02 14:32:00,718+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.plugin.OServerPluginManager - - jmx
2025-07-02 14:32:00,721+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server shutdown complete
2025-07-02 14:32:00,722+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - Orient Engine is shutting down...
2025-07-02 14:32:00,724+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: analytics...
2025-07-02 14:32:01,229+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: component...
2025-07-02 14:32:01,825+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: security...
2025-07-02 14:32:02,118+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: config...
2025-07-02 14:32:02,223+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: OSystem...
2025-07-02 14:32:03,625+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - OrientDB Engine shutdown complete
2025-07-02 14:32:03,718+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - Shutdown
2025-07-02 14:32:03,725+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop KERNEL
2025-07-02 17:08:41,100+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.pax.logging.NexusLogActivator - start
2025-07-02 17:08:45,281+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.features.internal.FeaturesWrapper - Fast FeaturesService starting
2025-07-02 17:08:54,309+0800 INFO  [FelixStartLevel]  *SYSTEM ROOT - bundle org.apache.felix.scr:2.1.30 (56) Starting with globalExtender setting: false
2025-07-02 17:08:54,395+0800 INFO  [FelixStartLevel]  *SYSTEM ROOT - bundle org.apache.felix.scr:2.1.30 (56)  Version = 2.1.30
2025-07-02 17:08:56,985+0800 WARN  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4JInitialiser - Your logging framework class org.ops4j.pax.logging.slf4j.Slf4jLogger is not known - if it needs access to the standard println methods on the console you will need to register it by calling registerLoggingSystemPackage
2025-07-02 17:08:56,991+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Package org.ops4j.pax.logging.slf4j registered; all classes within it or subpackages of it will be allowed to print to System.out and System.err
2025-07-02 17:08:56,997+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Replaced standard System.out and System.err PrintStreams with SLF4JPrintStreams
2025-07-02 17:08:57,000+0800 INFO  [FelixStartLevel]  *SYSTEM uk.org.lidalia.sysoutslf4j.context.SysOutOverSLF4J - Redirected System.out and System.err to SLF4J for this context
2025-07-02 17:08:57,086+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder - Properties:
2025-07-02 17:08:57,088+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   application-host='0.0.0.0'
2025-07-02 17:08:57,089+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   application-port='8081'
2025-07-02 17:08:57,089+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   fabric.etc='/opt/sonatype/nexus/etc/fabric'
2025-07-02 17:08:57,090+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   jetty.etc='/opt/sonatype/nexus/etc/jetty'
2025-07-02 17:08:57,090+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.base='/opt/sonatype/nexus'
2025-07-02 17:08:57,091+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.data='/nexus-data'
2025-07-02 17:08:57,091+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.etc='/opt/sonatype/nexus/etc/karaf'
2025-07-02 17:08:57,092+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.home='/opt/sonatype/nexus'
2025-07-02 17:08:57,093+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   karaf.instances='/nexus-data/instances'
2025-07-02 17:08:57,096+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   logback.etc='/opt/sonatype/nexus/etc/logback'
2025-07-02 17:08:57,097+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-args='/opt/sonatype/nexus/etc/jetty/jetty.xml,/opt/sonatype/nexus/etc/jetty/jetty-http.xml,/opt/sonatype/nexus/etc/jetty/jetty-requestlog.xml'
2025-07-02 17:08:57,098+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-context-path='/'
2025-07-02 17:08:57,098+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-edition='nexus-pro-edition'
2025-07-02 17:08:57,099+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus-features='nexus-pro-feature'
2025-07-02 17:08:57,099+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   nexus.hazelcast.discovery.isEnabled='true'
2025-07-02 17:08:57,100+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.ConfigurationBuilder -   ssl.etc='/opt/sonatype/nexus/etc/ssl'
2025-07-02 17:08:57,103+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - Java: 1.8.0_442, OpenJDK 64-Bit Server VM, Red Hat, Inc., 25.442-b06
2025-07-02 17:08:57,181+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - OS: Linux, 6.10.14-linuxkit, amd64
2025-07-02 17:08:57,182+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - User: nexus, en, /opt/sonatype/nexus
2025-07-02 17:08:57,182+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - CWD: /opt/sonatype/nexus
2025-07-02 17:08:57,191+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.Launcher - TMP: /nexus-data/tmp
2025-07-02 17:08:57,282+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Starting
2025-07-02 17:08:57,292+0800 INFO  [FelixStartLevel]  *SYSTEM org.eclipse.jetty.util.log - Logging initialized @31206ms to org.eclipse.jetty.util.log.Slf4jLog
2025-07-02 17:08:57,301+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty.xml
2025-07-02 17:08:58,392+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty-http.xml
2025-07-02 17:08:58,589+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Applying configuration: file:/opt/sonatype/nexus/etc/jetty/jetty-requestlog.xml
2025-07-02 17:08:58,785+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - Starting: Server@659f544a{STOPPED}[9.4.53.v20231009]
2025-07-02 17:08:58,907+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.Server - jetty-9.4.53.v20231009; built: 2023-10-09T12:29:09.265Z; git: 27bde00a0b95a1d5bbee0eae7984f891d2d0f8c9; jvm 1.8.0_442-b06
2025-07-02 17:08:59,880+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - DefaultSessionIdManager workerName=node0
2025-07-02 17:08:59,885+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - No SessionScavenger set, using defaults
2025-07-02 17:08:59,890+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.session - node0 Scavenging every 600000ms
2025-07-02 17:08:59,983+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Initializing
2025-07-02 17:09:00,105+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.OssNexusEdition - Loading OSS Edition
2025-07-02 17:09:00,188+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Installing: nexus-oss-edition/********* (nexus-orient/*********)
2025-07-02 17:09:31,733+0800 INFO  [jetty-main-1]  *SYSTEM org.ehcache.core.osgi.EhcacheActivator - Detected OSGi Environment (core is in bundle: org.ehcache [152]): Using OSGi Based Service Loading
2025-07-02 17:09:41,836+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Installed: nexus-oss-edition/********* (nexus-orient/*********)
2025-07-02 17:09:47,073+0800 INFO  [jetty-main-1]  *SYSTEM org.apache.shiro.nexus.NexusWebSessionManager - Global session timeout: 1800000 ms
2025-07-02 17:09:47,085+0800 INFO  [jetty-main-1]  *SYSTEM org.apache.shiro.nexus.NexusWebSessionManager - Session-cookie prototype: name=NXSESSIONID, secure=true
2025-07-02 17:09:47,397+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.common [*********]
2025-07-02 17:09:48,499+0800 INFO  [jetty-main-1]  *SYSTEM org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-07-02 17:09:49,979+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.common [*********]
2025-07-02 17:09:49,994+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.hibernate.validator [6.2.0.Final]
2025-07-02 17:09:50,569+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.hibernate.validator [6.2.0.Final]
2025-07-02 17:09:50,573+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore-api [*********]
2025-07-02 17:09:51,010+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore-api [*********]
2025-07-02 17:09:51,012+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cache [*********]
2025-07-02 17:09:51,493+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cache [*********]
2025-07-02 17:09:51,539+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.jmx [*********]
2025-07-02 17:09:51,893+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.jmx [*********]
2025-07-02 17:09:51,971+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.crypto [*********]
2025-07-02 17:09:54,704+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.crypto [*********]
2025-07-02 17:09:54,725+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.security [*********]
2025-07-02 17:09:59,183+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.security [*********]
2025-07-02 17:09:59,189+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.datastore [*********]
2025-07-02 17:10:00,472+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.datastore [*********]
2025-07-02 17:10:00,480+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.supportzip-api [*********]
2025-07-02 17:10:01,573+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.supportzip-api [*********]
2025-07-02 17:10:01,599+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.thread [*********]
2025-07-02 17:10:01,999+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.thread [*********]
2025-07-02 17:10:02,002+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.scheduling [*********]
2025-07-02 17:10:03,091+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.scheduling [*********]
2025-07-02 17:10:03,095+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore [*********]
2025-07-02 17:10:04,185+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore [*********]
2025-07-02 17:10:04,187+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.apache.tika.core [1.28.4]
2025-07-02 17:10:04,483+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.apache.tika.core [1.28.4]
2025-07-02 17:10:04,495+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.orient [*********]
2025-07-02 17:10:05,588+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.orient [*********]
2025-07-02 17:10:05,592+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.upgrade [*********]
2025-07-02 17:10:05,896+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.upgrade [*********]
2025-07-02 17:10:05,899+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.blobstore-file [*********]
2025-07-02 17:10:06,499+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.blobstore-file [*********]
2025-07-02 17:10:06,502+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.capability [*********]
2025-07-02 17:10:06,897+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.capability [*********]
2025-07-02 17:10:06,898+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.commands [*********]
2025-07-02 17:10:07,108+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.commands [*********]
2025-07-02 17:10:07,109+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.email [*********]
2025-07-02 17:10:07,399+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.email [*********]
2025-07-02 17:10:07,486+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.httpclient [*********]
2025-07-02 17:10:07,701+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.httpclient [*********]
2025-07-02 17:10:07,703+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.servlet [*********]
2025-07-02 17:10:07,910+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.servlet [*********]
2025-07-02 17:10:07,916+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.ssl [*********]
2025-07-02 17:10:08,102+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.ssl [*********]
2025-07-02 17:10:08,105+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.base [*********]
2025-07-02 17:10:09,097+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.metrics.MetricsModule - Metrics support configured
2025-07-02 17:10:09,608+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.metrics.MetricsModule - Metrics support configured
2025-07-02 17:10:14,979+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.base [*********]
2025-07-02 17:10:14,986+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.extdirect [*********]
2025-07-02 17:10:15,880+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.extdirect [*********]
2025-07-02 17:10:15,887+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.siesta [*********]
2025-07-02 17:10:16,197+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.siesta [*********]
2025-07-02 17:10:16,199+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rest-jackson2 [*********]
2025-07-02 17:10:16,293+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rest-jackson2 [*********]
2025-07-02 17:10:16,296+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.swagger [*********]
2025-07-02 17:10:16,499+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.swagger [*********]
2025-07-02 17:10:16,579+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rapture [*********]
2025-07-02 17:10:17,383+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rapture [*********]
2025-07-02 17:10:17,585+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.datastore-mybatis [*********]
2025-07-02 17:10:18,091+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.datastore-mybatis [*********]
2025-07-02 17:10:18,093+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.quartz [*********]
2025-07-02 17:10:18,988+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.quartz [*********]
2025-07-02 17:10:19,007+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.oss-edition [*********]
2025-07-02 17:10:19,198+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.oss-edition [*********]
2025-07-02 17:10:19,205+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Running lifecycle phases [KERNEL, STORAGE, RESTORE, UPGRADE, SCHEMAS, EVENTS, SECURITY, SERVICES, REPOSITORIES, CAPABILITIES, TASKS]
2025-07-02 17:10:19,207+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start KERNEL
2025-07-02 17:10:19,216+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.log.overrides.file.LogbackLoggerOverrides - File: /nexus-data/etc/logback/logback-overrides.xml
2025-07-02 17:10:19,279+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.log.LogbackLogManager - Configuring
2025-07-02 17:10:19,391+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Installing: [nexus-oss-feature/*********, nexus-cma-extra/*********, nexus-ossindex-plugin/*********]
2025-07-02 17:11:15,306+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Installed: [nexus-oss-feature/*********, nexus-cma-extra/*********, nexus-ossindex-plugin/*********]
2025-07-02 17:11:15,688+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-audit-plugin [*********]
2025-07-02 17:11:17,204+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-audit-plugin [*********]
2025-07-02 17:11:17,680+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-ssl-plugin [*********]
2025-07-02 17:11:18,986+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-ssl-plugin [*********]
2025-07-02 17:11:22,781+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.selector [*********]
2025-07-02 17:11:23,111+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.selector [*********]
2025-07-02 17:11:23,121+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.elasticsearch [*********]
2025-07-02 17:11:23,378+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.elasticsearch [*********]
2025-07-02 17:11:23,386+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.repository-content [*********]
2025-07-02 17:11:26,801+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.repository-content [*********]
2025-07-02 17:11:26,893+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.repository-config [*********]
2025-07-02 17:11:27,387+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.repository-config [*********]
2025-07-02 17:11:27,390+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-coreui-plugin [*********]
2025-07-02 17:11:28,099+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-coreui-plugin [*********]
2025-07-02 17:11:28,481+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-httpbridge [*********]
2025-07-02 17:11:28,698+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-httpbridge [*********]
2025-07-02 17:11:30,386+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-blobstore-tasks [*********]
2025-07-02 17:11:30,581+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-blobstore-tasks [*********]
2025-07-02 17:11:30,585+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cleanup-config [*********]
2025-07-02 17:11:30,881+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cleanup-config [*********]
2025-07-02 17:11:30,886+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.core [*********]
2025-07-02 17:11:33,590+0800 ERROR [Thread-77]  *SYSTEM org.sonatype.nexus.internal.node.orient.OrientLocalNodeAccess - Failed to determine hostname, using nodeId instead.
2025-07-02 17:11:34,193+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.core [*********]
2025-07-02 17:11:34,287+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-maven [*********]
2025-07-02 17:11:35,001+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-maven [*********]
2025-07-02 17:11:35,083+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-script-plugin [*********]
2025-07-02 17:11:35,581+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-script-plugin [*********]
2025-07-02 17:11:35,703+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-task-log-cleanup [*********]
2025-07-02 17:11:35,890+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-task-log-cleanup [*********]
2025-07-02 17:11:36,468+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-blobstore-s3 [*********]
2025-07-02 17:11:37,401+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-blobstore-s3 [*********]
2025-07-02 17:11:37,416+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-onboarding-plugin [*********]
2025-07-02 17:11:37,586+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-onboarding-plugin [*********]
2025-07-02 17:11:37,621+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-default-role-plugin [*********]
2025-07-02 17:11:37,884+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-default-role-plugin [*********]
2025-07-02 17:11:37,982+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-apt [*********]
2025-07-02 17:11:38,296+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-apt [*********]
2025-07-02 17:11:39,389+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.plugins.nexus-repository-raw [*********]
2025-07-02 17:11:39,788+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.plugins.nexus-repository-raw [*********]
2025-07-02 17:11:39,901+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.cleanup [*********]
2025-07-02 17:11:40,185+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.cleanup [*********]
2025-07-02 17:11:40,780+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ldap-plugin [*********]
2025-07-02 17:11:42,085+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ldap-plugin [*********]
2025-07-02 17:11:43,193+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-proui-plugin [*********]
2025-07-02 17:11:43,509+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-proui-plugin [*********]
2025-07-02 17:11:43,595+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-proximanova-plugin [*********]
2025-07-02 17:11:43,699+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-proximanova-plugin [*********]
2025-07-02 17:11:45,184+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_insight_scan_insight-scanner-core_2.36.66-01_insight-scanner-core-2.36.66-01.jar [0.0.0]
2025-07-02 17:11:45,204+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_insight_scan_insight-scanner-core_2.36.66-01_insight-scanner-core-2.36.66-01.jar [0.0.0]
2025-07-02 17:11:45,211+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_insight_scan_insight-scanner-model-io_2.36.66-01_insight-scanner-model-io-2.36.66-01.jar [0.0.0]
2025-07-02 17:11:45,290+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_insight_scan_insight-scanner-model-io_2.36.66-01_insight-scanner-model-io-2.36.66-01.jar [0.0.0]
2025-07-02 17:11:45,291+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-healthcheck-base [*********]
2025-07-02 17:11:51,399+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-healthcheck-base [*********]
2025-07-02 17:11:51,491+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING wrap_file_system_com_sonatype_licensing_license-bundle_1.6.0_license-bundle-1.6.0.jar [0.0.0]
2025-07-02 17:11:51,984+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED wrap_file_system_com_sonatype_licensing_license-bundle_1.6.0_license-bundle-1.6.0.jar [0.0.0]
2025-07-02 17:11:51,987+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.licensing-extension [*********]
2025-07-02 17:11:52,194+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.licensing-extension [*********]
2025-07-02 17:11:52,201+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-analytics-plugin [*********]
2025-07-02 17:11:52,690+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-analytics-plugin [*********]
2025-07-02 17:11:52,695+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-licensing-plugin [*********]
2025-07-02 17:11:52,881+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-licensing-plugin [*********]
2025-07-02 17:11:53,411+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-npm [*********]
2025-07-02 17:11:53,989+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-npm [*********]
2025-07-02 17:11:53,995+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-nuget [*********]
2025-07-02 17:11:55,100+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-nuget [*********]
2025-07-02 17:11:55,193+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-rubygems [*********]
2025-07-02 17:11:56,302+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-rubygems [*********]
2025-07-02 17:11:56,380+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING org.sonatype.nexus.rest-client [*********]
2025-07-02 17:11:56,414+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED org.sonatype.nexus.rest-client [*********]
2025-07-02 17:11:56,416+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-migration-plugin [*********]
2025-07-02 17:11:56,892+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-migration-plugin [*********]
2025-07-02 17:11:57,289+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-vulnerability-plugin [*********]
2025-07-02 17:11:57,597+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-vulnerability-plugin [*********]
2025-07-02 17:11:57,602+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-outreach-plugin [*********]
2025-07-02 17:11:57,801+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-outreach-plugin [*********]
2025-07-02 17:11:57,889+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-rutauth-plugin [*********]
2025-07-02 17:11:57,983+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-rutauth-plugin [*********]
2025-07-02 17:11:58,283+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-clm-oss-plugin [*********]
2025-07-02 17:11:58,302+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-clm-oss-plugin [*********]
2025-07-02 17:11:58,787+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-docker [*********]
2025-07-02 17:12:04,692+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-docker [*********]
2025-07-02 17:12:06,082+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-yum [*********]
2025-07-02 17:12:07,688+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-yum [*********]
2025-07-02 17:12:08,383+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING nexus-blobstore-azure-cloud [*********]
2025-07-02 17:12:08,585+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED nexus-blobstore-azure-cloud [*********]
2025-07-02 17:12:08,983+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ahc-plugin [*********]
2025-07-02 17:12:09,192+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ahc-plugin [*********]
2025-07-02 17:12:10,489+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-helm [*********]
2025-07-02 17:12:10,796+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-helm [*********]
2025-07-02 17:12:10,890+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-gitlfs [*********]
2025-07-02 17:12:11,085+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-gitlfs [*********]
2025-07-02 17:12:11,088+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-pypi [*********]
2025-07-02 17:12:11,692+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-pypi [*********]
2025-07-02 17:12:11,789+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-conda [*********]
2025-07-02 17:12:11,893+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-conda [*********]
2025-07-02 17:12:11,986+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-conan [*********]
2025-07-02 17:12:13,081+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-conan [*********]
2025-07-02 17:12:13,218+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-r [*********]
2025-07-02 17:12:13,609+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-r [*********]
2025-07-02 17:12:13,692+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-cocoapods [*********]
2025-07-02 17:12:13,784+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-cocoapods [*********]
2025-07-02 17:12:13,880+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-golang [*********]
2025-07-02 17:12:14,093+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-golang [*********]
2025-07-02 17:12:14,183+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-p2 [*********]
2025-07-02 17:12:14,297+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-p2 [*********]
2025-07-02 17:12:14,639+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-repository-bower [*********]
2025-07-02 17:12:14,792+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-repository-bower [*********]
2025-07-02 17:12:15,180+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATING com.sonatype.nexus.plugins.nexus-ossindex-plugin [*********]
2025-07-02 17:12:15,479+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusBundleTracker - ACTIVATED com.sonatype.nexus.plugins.nexus-ossindex-plugin [*********]
2025-07-02 17:12:15,682+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start STORAGE
2025-07-02 17:12:16,002+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.node.orient.OrientLocalNodeAccess - ID: E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A
2025-07-02 17:12:18,214+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - OrientDB version: 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 17:12:18,396+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server v2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x) is starting up...
2025-07-02 17:12:18,484+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Databases directory: /nexus-data/db
2025-07-02 17:12:19,487+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Configuration of usage of soft references inside of containers of results of SQL execution
2025-07-02 17:12:19,577+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Initial and maximum values of heap memory usage are NOT equal, containers of results of SQL executors will NOT use soft references by default
2025-07-02 17:12:19,578+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Auto configuration of disk cache size.
2025-07-02 17:12:20,493+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - 2684354560 B/2560 MB/2 GB of physical memory were detected on machine
2025-07-02 17:12:20,689+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Soft memory limit for this process is set to -1 B/-1 MB/-1 GB
2025-07-02 17:12:20,693+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Hard memory limit for this process is set to -1 B/-1 MB/-1 GB
2025-07-02 17:12:20,778+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.common.jna.ONative - Detected memory limit for current process is 2684354560 B/2560 MB/2 GB
2025-07-02 17:12:20,786+0800 WARN  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - Not enough physical memory available for DISKCACHE: 2,560MB (heap=1,536MB direct=512MB). Set lower Maximum Heap (-Xmx setting on JVM) and restart OrientDB. Now running with DISKCACHE=256MB
2025-07-02 17:12:20,788+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.engine.OMemoryAndLocalPaginatedEnginesInitializer - OrientDB config DISKCACHE=256MB (heap=1,536MB direct=512MB os=2,560MB)
2025-07-02 17:12:21,193+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Found ORIENTDB_ROOT_PASSWORD variable, using this value as root's password
2025-07-02 17:12:21,600+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.handler.OJMXPlugin - JMX plugin installed and active: profilerManaged=true
2025-07-02 17:12:21,608+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Studio available at $ANSI{blue http://localhost:2480/studio/index.html}
2025-07-02 17:12:21,609+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - $ANSI{green:italic OrientDB Server is active} v2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x).
2025-07-02 17:12:21,610+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - OrientDB Studio has been deprecated and it's no longer available
2025-07-02 17:12:21,610+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - Activated
2025-07-02 17:12:22,187+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start RESTORE
2025-07-02 17:12:23,796+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/security' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 17:12:25,492+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/analytics' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 17:12:26,290+0800 INFO  [ForkJoinPool.commonPool-worker-1]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/component' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 17:12:28,193+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.storage.impl.local.paginated.OLocalPaginatedStorage - Storage 'plocal:/nexus-data/db/config' is opened under OrientDB distribution : 2.2.37 (build a7541e7ceeabf592dd9a7b2928b6c023cbc73193, branch 2.2.x)
2025-07-02 17:12:28,607+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start UPGRADE
2025-07-02 17:12:32,579+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SCHEMAS
2025-07-02 17:12:32,702+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool config with per-core limit of 16
2025-07-02 17:12:33,405+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start EVENTS
2025-07-02 17:12:34,497+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SECURITY
2025-07-02 17:12:34,500+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.security.internal.DefaultSecuritySystem - Unlimited strength JCE policy detected
2025-07-02 17:12:34,505+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool security with per-core limit of 16
2025-07-02 17:12:34,515+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.security.internal.RealmManagerImpl - Using default configuration: OrientRealmConfiguration{realmNames=[NexusAuthenticatingRealm, NexusAuthorizingRealm]}
2025-07-02 17:12:34,589+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.RuntimeCacheManagerProvider - Cache-provider: ehcache
2025-07-02 17:12:34,796+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.ehcache.EhCacheManagerProvider - Creating cache-manager with configuration: file:/opt/sonatype/nexus/etc/fabric/ehcache.xml
2025-07-02 17:12:38,289+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache NexusAuthenticatingRealm.authenticationCache will be supplemented by template nexus-default
2025-07-02 17:12:38,580+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthenticatingRealm.authenticationCache' created in EhcacheManager.
2025-07-02 17:12:38,681+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthenticatingRealm.authenticationCache
2025-07-02 17:12:38,689+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthenticatingRealm.authenticationCache
2025-07-02 17:12:38,788+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache NexusAuthorizingRealm.authorizationCache will be supplemented by template nexus-default
2025-07-02 17:12:38,792+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthorizingRealm.authorizationCache' created in EhcacheManager.
2025-07-02 17:12:38,795+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthorizingRealm.authorizationCache
2025-07-02 17:12:38,797+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=NexusAuthorizingRealm.authorizationCache
2025-07-02 17:12:38,892+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start SERVICES
2025-07-02 17:12:38,991+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.httpclient.HttpClientManagerImpl - Using default configuration: OrientHttpClientConfiguration{connection=null, proxy=null, authentication=null}
2025-07-02 17:12:39,377+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool analytics with per-core limit of 16
2025-07-02 17:12:39,897+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache SYSTEM_INFORMATION will be supplemented by template nexus-default
2025-07-02 17:12:39,980+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' created in EhcacheManager.
2025-07-02 17:12:39,988+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=SYSTEM_INFORMATION
2025-07-02 17:12:39,989+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=SYSTEM_INFORMATION
2025-07-02 17:12:40,093+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Detected 2 engine-factories
2025-07-02 17:12:40,099+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Engine-factory: Oracle Nashorn v1.8.0_442; language=ECMAScript, version=ECMA - 262 Edition 5.1, names=[nashorn, Nashorn, js, JS, JavaScript, javascript, ECMAScript, ecmascript], mime-types=[application/javascript, application/ecmascript, text/javascript, text/ecmascript], extensions=[js]
2025-07-02 17:12:40,110+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Engine-factory: Groovy Scripting Engine v2.0; language=Groovy, version=3.0.19, names=[groovy, Groovy], mime-types=[application/x-groovy], extensions=[groovy]
2025-07-02 17:12:40,110+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.script.ScriptEngineManagerProvider - Default language: groovy
2025-07-02 17:12:40,178+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.elasticsearch.internal.NodeProvider - Creating node with config: /opt/sonatype/nexus/etc/fabric/elasticsearch.yml
2025-07-02 17:12:41,014+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] version[2.4.3], pid[1], build[d38a34e/2016-12-07T16:28:56Z]
2025-07-02 17:12:41,021+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] initializing ...
2025-07-02 17:12:41,085+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.plugins - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] modules [], plugins [content-auth-plugin], sites []
2025-07-02 17:12:41,118+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.env - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] using [1] data paths, mounts [[/nexus-data (/run/host_mark/Users)]], net usable_space [610.5gb], net total_space [926.3gb], spins? [possibly], types [fakeowner]
2025-07-02 17:12:41,119+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.env - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] heap size [1.5gb], compressed ordinary object pointers [true]
2025-07-02 17:12:45,211+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] initialized
2025-07-02 17:12:45,213+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] starting ...
2025-07-02 17:12:45,288+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.transport - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] publish_address {local[1]}, bound_addresses {local[1]}
2025-07-02 17:12:45,295+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.discovery - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] nexus/RhX3V3RaQA64VSawj2s0Iw
2025-07-02 17:12:45,300+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.cluster.service - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] new_master {E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A}{RhX3V3RaQA64VSawj2s0Iw}{local}{local[1]}{local=true, master=true}, reason: local-disco-initial_connect(master)
2025-07-02 17:12:45,307+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] started
2025-07-02 17:12:46,181+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.gateway - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] recovered [5] indices into cluster_state
2025-07-02 17:12:51,019+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][clusterService#updateTask][T#1]]  *SYSTEM org.elasticsearch.cluster.routing.allocation - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] Cluster health status changed from [RED] to [GREEN] (reason: [shards started [[2e9a1e67e8a325bcd6ee9f6790ff6c769e791d56][0]] ...]).
2025-07-02 17:12:51,186+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Configuring OrientDB pool component with per-core limit of 16
2025-07-02 17:12:51,383+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.QuartzSchedulerProvider - Thread-pool size: 20, Thread-pool priority: 5
2025-07-02 17:12:51,478+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.JobStoreImpl - Instance name: nexus; ID: E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A
2025-07-02 17:12:51,479+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.JobStoreImpl - Initialized
2025-07-02 17:12:51,499+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.QuartzSchedulerProvider - Quartz Scheduler v2.3.2
2025-07-02 17:12:53,490+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Task log cleanup' [tasklog.cleanup] : state=WAITING
2025-07-02 17:12:53,578+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] : state=WAITING
2025-07-02 17:12:53,579+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Cleanup service' [repository.cleanup] : state=WAITING
2025-07-02 17:12:53,580+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] : state=WAITING
2025-07-02 17:12:54,992+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start REPOSITORIES
2025-07-02 17:12:56,490+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.blobstore.file.internal.OrientFileBlobStoreMetricsStore - Loading blob store metrics file /nexus-data/blobs/default/E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A-metrics.properties null
2025-07-02 17:12:58,594+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetHostedGalleryFacet - Enabled NuGet feed cooperation for nuget-hosted
2025-07-02 17:12:59,599+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository nuget-group set to Ready to Connect.
2025-07-02 17:12:59,602+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetGroupGalleryFacet - Enabled NuGet feed cooperation for nuget-group
2025-07-02 17:12:59,604+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget-group#odata-query-cache will be supplemented by template nexus-default
2025-07-02 17:12:59,613+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget-group#odata-query-cache' created in EhcacheManager.
2025-07-02 17:12:59,615+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget-group#odata-query-cache
2025-07-02 17:12:59,616+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget-group#odata-query-cache
2025-07-02 17:13:00,986+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository nuget.org-proxy set to Ready to Connect.
2025-07-02 17:13:00,988+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget.org-proxy#negative-cache will be supplemented by template nexus-default
2025-07-02 17:13:00,991+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#negative-cache' created in EhcacheManager.
2025-07-02 17:13:00,992+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#negative-cache
2025-07-02 17:13:00,993+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#negative-cache
2025-07-02 17:13:00,994+0800 INFO  [FelixStartLevel]  *SYSTEM com.sonatype.nexus.repository.nuget.orient.internal.v2.OrientNugetProxyGalleryFacetImpl - Enabled NuGet feed cooperation for nuget.org-proxy
2025-07-02 17:13:00,995+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache nuget.org-proxy#odata-query-cache will be supplemented by template nexus-default
2025-07-02 17:13:01,000+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#odata-query-cache' created in EhcacheManager.
2025-07-02 17:13:01,002+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#odata-query-cache
2025-07-02 17:13:01,004+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=nuget.org-proxy#odata-query-cache
2025-07-02 17:13:01,291+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpclient.internal.HttpClientFacetImpl - Remote connection status of repository maven-central set to Ready to Connect.
2025-07-02 17:13:01,294+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.ConfigurationMerger - Configuration of cache maven-central#negative-cache will be supplemented by template nexus-default
2025-07-02 17:13:01,388+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'maven-central#negative-cache' created in EhcacheManager.
2025-07-02 17:13:01,390+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=maven-central#negative-cache
2025-07-02 17:13:01,393+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=maven-central#negative-cache
2025-07-02 17:13:01,980+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start CAPABILITIES
2025-07-02 17:13:05,686+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.osgi.BootstrapListener - Initialized
2025-07-02 17:13:05,696+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Start TASKS
2025-07-02 17:13:07,581+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] removed
2025-07-02 17:13:08,207+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] : state=WAITING
2025-07-02 17:13:09,392+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.scheduling.TaskSchedulerImpl - Task 'Metric aggregation' [content.usage.aggregation] scheduled: cron
2025-07-02 17:13:09,793+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] runNow
2025-07-02 17:13:10,081+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change WAITING -> RUNNING
2025-07-02 17:13:12,580+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] removed
2025-07-02 17:13:12,690+0800 INFO  [periodic-4-thread-1]  *SYSTEM org.sonatype.nexus.rapture.internal.LocalSystemCheckService - Health check status changed from true to false for Scheduler
2025-07-02 17:13:12,793+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] : state=WAITING
2025-07-02 17:13:12,998+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.scheduling.TaskSchedulerImpl - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] scheduled: cron
2025-07-02 17:13:13,282+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.search.elasticsearch.IndexStartupRebuildManager - Skipping rebuild of repository indexes
2025-07-02 17:13:13,702+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.OrientQuartzSchedulerSPI - Scheduler put into ready mode
2025-07-02 17:13:16,601+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle - UI plugin descriptors:
2025-07-02 17:13:16,903+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rapture
2025-07-02 17:13:16,999+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-coreui-plugin
2025-07-02 17:13:17,187+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle - ExtJS UI plugin descriptors:
2025-07-02 17:13:17,203+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rapture
2025-07-02 17:13:17,382+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-blobstore-s3
2025-07-02 17:13:17,390+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-proximanova-plugin
2025-07-02 17:13:17,487+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-rutauth-plugin
2025-07-02 17:13:17,491+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-blobstore-azure-cloud
2025-07-02 17:13:17,579+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-coreui-plugin
2025-07-02 17:13:17,585+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-proui-plugin
2025-07-02 17:13:17,587+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-maven
2025-07-02 17:13:17,589+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-onboarding-plugin
2025-07-02 17:13:17,760+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-npm
2025-07-02 17:13:17,768+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-nuget
2025-07-02 17:13:17,796+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-rubygems
2025-07-02 17:13:17,985+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-docker
2025-07-02 17:13:17,990+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-repository-pypi
2025-07-02 17:13:17,991+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.rapture.internal.RaptureWebResourceBundle -   nexus-analytics-plugin
2025-07-02 17:13:18,482+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.internal.webresources.WebResourceServlet - Max-age: 30 days (2592000 seconds)
2025-07-02 17:13:19,386+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.servlet.DirectJNgineServlet - Servlet GLOBAL configuration: debug=false, providersUrl=service/extdirect, minify=false, batchRequestsMultithreadingEnabled=true, batchRequestsMinThreadsPoolSize=16, batchRequestsMaxThreadsPoolSize=80, batchRequestsMaxThreadsPerRequest=8, batchRequestsMaxThreadKeepAliveSeconds=60, gsonBuilderConfiguratorClass=org.sonatype.nexus.extdirect.internal.ExtDirectGsonBuilderConfigurator, dispatcherClass=com.softwarementors.extjs.djn.servlet.ssm.SsmDispatcher, jsonRequestProcessorThreadClass=org.sonatype.nexus.extdirect.internal.ExtDirectJsonRequestProcessorThread, contextPath=--not specified: calculated via Javascript--, createSourceFiles=true
2025-07-02 17:13:19,398+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.servlet.DirectJNgineServlet - Servlet GLOBAL configuration: registryConfiguratorClass=
2025-07-02 17:13:19,983+0800 INFO  [jetty-main-1]  *SYSTEM com.softwarementors.extjs.djn.jscodegen.CodeFileGenerator - Creating source files for APIs...
2025-07-02 17:13:23,790+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - JAX-RS RuntimeDelegate: org.sonatype.nexus.siesta.internal.resteasy.SisuResteasyProviderFactory@fdecbd8
2025-07-02 17:13:24,194+0800 INFO  [periodic-4-thread-1]  *SYSTEM org.sonatype.nexus.rapture.internal.LocalSystemCheckService - Health check status changed from false to true for Lifecycle Phase
2025-07-02 17:13:24,904+0800 INFO  [jetty-main-1]  *SYSTEM org.jboss.resteasy.plugins.validation.i18n - RESTEASY008550: Unable to find CDI supporting ValidatorFactory. Using default ValidatorFactory
2025-07-02 17:13:25,880+0800 INFO  [periodic-4-thread-1]  *SYSTEM org.sonatype.nexus.rapture.internal.LocalSystemCheckService - Health check status changed from false to true for Scheduler
2025-07-02 17:13:28,185+0800 INFO  [quartz-10-thread-1]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change RUNNING -> WAITING (OK)
2025-07-02 17:13:38,896+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - Initialized
2025-07-02 17:13:39,587+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.repository.httpbridge.internal.ViewServlet - Initialized
2025-07-02 17:13:40,303+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.handler.ContextHandler - Started o.e.j.w.WebAppContext@542443a6{Sonatype Nexus,/,null,AVAILABLE}
2025-07-02 17:13:40,789+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.AbstractConnector - Started ServerConnector@495a23db{HTTP/1.1, (http/1.1)}{0.0.0.0:8081}
2025-07-02 17:13:40,793+0800 INFO  [jetty-main-1]  *SYSTEM org.eclipse.jetty.server.Server - Started @314707ms
2025-07-02 17:13:40,881+0800 INFO  [jetty-main-1]  *SYSTEM org.sonatype.nexus.bootstrap.jetty.JettyServer - 
-------------------------------------------------

Started Sonatype Nexus OSS 3.70.4-02

-------------------------------------------------
2025-07-02 17:13:57,207+0800 INFO  [qtp1587376723-105]  *UNKNOWN org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Enabling session validation scheduler...
2025-07-02 17:13:57,300+0800 INFO  [qtp1587376723-105]  *UNKNOWN org.sonatype.nexus.internal.security.anonymous.AnonymousManagerImpl - Using default configuration: OrientAnonymousConfiguration{enabled=true, userId='anonymous', realmName='NexusAuthorizingRealm'}
2025-07-02 17:20:00,280+0800 INFO  [quartz-10-thread-2]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 17:20:00,390+0800 INFO  [quartz-10-thread-2]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 17:30:00,221+0800 INFO  [quartz-10-thread-3]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 17:30:00,597+0800 INFO  [quartz-10-thread-3]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 17:40:00,099+0800 INFO  [quartz-10-thread-4]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 17:40:00,291+0800 INFO  [quartz-10-thread-4]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 17:46:25,093+0800 INFO  [elasticsearch[E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A][scheduler][T#1]]  *SYSTEM org.elasticsearch.monitor.jvm - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] [gc][young][2013][90] duration [783ms], collections [1]/[1.6s], total [783ms]/[15s], memory [380mb]->[307.5mb]/[1.5gb], all_pools {[young] [72mb]->[0b]/[0b]}{[survivor] [3mb]->[2mb]/[0b]}{[old] [305mb]->[305.5mb]/[1.5gb]}
2025-07-02 17:50:00,592+0800 INFO  [quartz-10-thread-5]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 17:50:01,081+0800 INFO  [quartz-10-thread-5]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 18:00:00,185+0800 INFO  [quartz-10-thread-6]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 18:00:00,382+0800 INFO  [quartz-10-thread-6]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 18:01:00,039+0800 INFO  [quartz-10-thread-7]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change WAITING -> RUNNING
2025-07-02 18:01:01,099+0800 INFO  [quartz-10-thread-7]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change RUNNING -> WAITING (OK)
2025-07-02 18:10:00,851+0800 INFO  [quartz-10-thread-8]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 18:10:01,152+0800 INFO  [quartz-10-thread-8]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 18:13:57,324+0800 INFO  [SessionValidationThread-1]  *UNKNOWN org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Validating all active sessions...
2025-07-02 18:13:57,380+0800 INFO  [SessionValidationThread-1]  *UNKNOWN org.ehcache.jsr107.ConfigurationMerger - Configuration of cache shiro-activeSessionCache will be supplemented by template nexus-default
2025-07-02 18:13:57,568+0800 INFO  [SessionValidationThread-1]  *UNKNOWN org.ehcache.core.EhcacheManager - Cache 'shiro-activeSessionCache' created in EhcacheManager.
2025-07-02 18:13:57,573+0800 INFO  [SessionValidationThread-1]  *UNKNOWN org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheConfiguration,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=shiro-activeSessionCache
2025-07-02 18:13:57,576+0800 INFO  [SessionValidationThread-1]  *UNKNOWN org.ehcache.jsr107.Eh107CacheManager - Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=file./opt/sonatype/nexus/etc/fabric/ehcache.xml,Cache=shiro-activeSessionCache
2025-07-02 18:13:57,655+0800 INFO  [SessionValidationThread-1]  *UNKNOWN org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Finished session validation.  No sessions were stopped.
2025-07-02 18:20:00,184+0800 INFO  [quartz-10-thread-9]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 18:20:00,579+0800 INFO  [quartz-10-thread-9]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 18:30:00,065+0800 INFO  [quartz-10-thread-10]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 18:30:00,171+0800 INFO  [quartz-10-thread-10]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 18:40:00,381+0800 INFO  [quartz-10-thread-11]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 18:40:00,591+0800 INFO  [quartz-10-thread-11]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 18:50:00,189+0800 INFO  [quartz-10-thread-12]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 18:50:00,291+0800 INFO  [quartz-10-thread-12]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 19:00:00,149+0800 INFO  [quartz-10-thread-13]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 19:00:00,454+0800 INFO  [quartz-10-thread-13]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 19:01:00,028+0800 INFO  [quartz-10-thread-14]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change WAITING -> RUNNING
2025-07-02 19:01:00,928+0800 INFO  [quartz-10-thread-14]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Metric aggregation' [content.usage.aggregation] state change RUNNING -> WAITING (OK)
2025-07-02 19:10:00,162+0800 INFO  [quartz-10-thread-15]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 19:10:00,443+0800 INFO  [quartz-10-thread-15]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 19:13:57,392+0800 INFO  [SessionValidationThread-1]  *UNKNOWN org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Validating all active sessions...
2025-07-02 19:13:57,406+0800 INFO  [SessionValidationThread-1]  *UNKNOWN org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Finished session validation.  No sessions were stopped.
2025-07-02 19:20:00,158+0800 INFO  [quartz-10-thread-16]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 19:20:00,256+0800 INFO  [quartz-10-thread-16]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 19:30:00,184+0800 INFO  [quartz-10-thread-17]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change WAITING -> RUNNING
2025-07-02 19:30:00,573+0800 INFO  [quartz-10-thread-17]  *SYSTEM org.sonatype.nexus.quartz.internal.task.QuartzTaskInfo - Task 'Storage facet cleanup' [repository.storage-facet-cleanup] state change RUNNING -> WAITING (OK)
2025-07-02 19:37:53,321+0800 WARN  [SIGTERM handler]  *SYSTEM com.orientechnologies.orient.core.OSignalHandler - Received signal: SIGTERM
2025-07-02 19:37:53,974+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.repository.httpbridge.internal.ViewServlet - Destroyed
2025-07-02 19:37:53,978+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.siesta.SiestaServlet - Destroyed
2025-07-02 19:37:54,072+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusContextListener - Uptime: 2 hours, 29 minutes, 27 seconds and 708 milliseconds (nexus-oss-edition/*********)
2025-07-02 19:37:54,072+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Shutting down
2025-07-02 19:37:54,089+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop TASKS
2025-07-02 19:37:54,175+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.quartz.internal.orient.OrientQuartzSchedulerSPI - Scheduler put into stand-by mode
2025-07-02 19:37:54,266+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop CAPABILITIES
2025-07-02 19:37:54,398+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop REPOSITORIES
2025-07-02 19:37:54,488+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SERVICES
2025-07-02 19:37:54,781+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' removed from EhcacheManager.
2025-07-02 19:37:54,785+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'SYSTEM_INFORMATION' successfully destroyed in EhcacheManager.
2025-07-02 19:37:54,804+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SECURITY
2025-07-02 19:37:54,869+0800 INFO  [FelixStartLevel]  *SYSTEM org.apache.shiro.session.mgt.AbstractValidatingSessionManager - Disabled session validation scheduler.
2025-07-02 19:37:54,871+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop EVENTS
2025-07-02 19:37:54,872+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop SCHEMAS
2025-07-02 19:37:54,876+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop UPGRADE
2025-07-02 19:37:54,877+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop RESTORE
2025-07-02 19:37:54,878+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop STORAGE
2025-07-02 19:37:54,884+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget-group#odata-query-cache' removed from EhcacheManager.
2025-07-02 19:37:54,888+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'shiro-activeSessionCache' removed from EhcacheManager.
2025-07-02 19:37:54,889+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthenticatingRealm.authenticationCache' removed from EhcacheManager.
2025-07-02 19:37:54,891+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'NexusAuthorizingRealm.authorizationCache' removed from EhcacheManager.
2025-07-02 19:37:54,895+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'maven-central#negative-cache' removed from EhcacheManager.
2025-07-02 19:37:54,896+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#odata-query-cache' removed from EhcacheManager.
2025-07-02 19:37:54,897+0800 INFO  [FelixStartLevel]  *SYSTEM org.ehcache.core.EhcacheManager - Cache 'nuget.org-proxy#negative-cache' removed from EhcacheManager.
2025-07-02 19:37:55,184+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.cache.internal.ehcache.EhCacheManagerProvider - Cache-manager closed
2025-07-02 19:37:55,188+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] stopping ...
2025-07-02 19:37:56,166+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] stopped
2025-07-02 19:37:56,170+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] closing ...
2025-07-02 19:37:56,276+0800 INFO  [FelixStartLevel]  *SYSTEM org.elasticsearch.node - [E357EA6C-2764C0B5-3B288158-0C30FDF5-844D1E2A] closed
2025-07-02 19:37:56,475+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping 4 pools
2025-07-02 19:37:56,567+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: analytics
2025-07-02 19:37:56,571+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: security
2025-07-02 19:37:56,572+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: component
2025-07-02 19:37:56,574+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseManagerImpl - Stopping pool: config
2025-07-02 19:37:56,577+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server is shutting down...
2025-07-02 19:37:56,579+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - Shutting down protocols
2025-07-02 19:37:56,770+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.plugin.OServerPluginManager - Shutting down plugins:
2025-07-02 19:37:56,775+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.server.plugin.OServerPluginManager - - jmx
2025-07-02 19:37:56,778+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl$1 - OrientDB Server shutdown complete
2025-07-02 19:37:56,867+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - Orient Engine is shutting down...
2025-07-02 19:37:56,874+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: analytics...
2025-07-02 19:37:57,676+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: security...
2025-07-02 19:37:58,581+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: component...
2025-07-02 19:37:59,382+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: config...
2025-07-02 19:37:59,681+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - - shutdown storage: OSystem...
2025-07-02 19:38:02,376+0800 INFO  [FelixStartLevel]  *SYSTEM com.orientechnologies.orient.core.Orient - OrientDB Engine shutdown complete
2025-07-02 19:38:02,473+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.internal.orient.DatabaseServerImpl - Shutdown
2025-07-02 19:38:02,487+0800 INFO  [FelixStartLevel]  *SYSTEM org.sonatype.nexus.extender.NexusLifecycleManager - Stop KERNEL
