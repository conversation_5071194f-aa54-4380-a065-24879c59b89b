version: "3.8"

services:
  nexus:
    image: sonatype/nexus3:3.70.4
    container_name: ecp-nexus
    ports:
      - "8081:8081"
    volumes:
      - ${ROOT_ENV_DIR}/ecp-nexus/data:/nexus-data
      - ${ROOT_ENV_DIR}/ecp-nexus/log:/nexus-data/log
      - ${ROOT_ENV_DIR}/ecp-nexus/conf/admin.password:/nexus-data/admin.password
    environment:
      - TZ=Asia/Shanghai
      # JVM内存优化 - 2GB总内存
      - INSTALL4J_ADD_VM_PARAMS=-Xms512m -Xmx1536m -XX:MaxDirectMemorySize=512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -Djava.util.prefs.userRoot=/nexus-data/user-prefs -Dnexus.scripts.allowCreation=true

    # 内存限制 - 2GB
    deploy:
      resources:
        limits:
          memory: 2560M
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8081 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

    restart: unless-stopped

  nexus-init:
    image: curlimages/curl:latest
    container_name: ecp-nexus-init
    depends_on:
      - nexus
    volumes:
      - ${ROOT_ENV_DIR}/ecp-nexus/scripts:/scripts
    command: /bin/sh -c "sleep 60 && /scripts/init-repos.sh"