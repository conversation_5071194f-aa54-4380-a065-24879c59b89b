1:C 26 Jun 2025 15:25:18.517 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 26 Jun 2025 15:25:18.518 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 26 Jun 2025 15:25:18.519 * Configuration loaded
1:M 26 Jun 2025 15:25:18.520 * monotonic clock: POSIX clock_gettime
1:M 26 Jun 2025 15:25:18.522 * Running mode=standalone, port=6379.
1:M 26 Jun 2025 15:25:18.528 * Server initialized
1:M 26 Jun 2025 15:25:18.531 * Creating AOF base file appendonly.aof.1.base.rdb on server start
1:M 26 Jun 2025 15:25:18.537 * Creating AOF incr file appendonly.aof.1.incr.aof on server start
1:M 26 Jun 2025 15:25:18.541 * Ready to accept connections tcp
1:M 26 Jun 2025 15:35:19.918 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 15:35:19.932 * Background saving started by pid 162
162:C 26 Jun 2025 15:35:19.938 * DB saved on disk
162:C 26 Jun 2025 15:35:19.941 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 15:35:20.038 * Background saving terminated with success
1:M 26 Jun 2025 15:50:21.027 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 15:50:21.034 * Background saving started by pid 373
373:C 26 Jun 2025 15:50:21.041 * DB saved on disk
373:C 26 Jun 2025 15:50:21.042 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 15:50:21.136 * Background saving terminated with success
1:M 26 Jun 2025 15:55:22.051 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 15:55:22.059 * Background saving started by pid 444
444:C 26 Jun 2025 15:55:22.061 * DB saved on disk
444:C 26 Jun 2025 15:55:22.061 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 15:55:22.160 * Background saving terminated with success
1:M 26 Jun 2025 16:32:03.730 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 16:32:03.733 * Background saving started by pid 808
808:C 26 Jun 2025 16:32:03.741 * DB saved on disk
808:C 26 Jun 2025 16:32:03.743 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 16:32:03.836 * Background saving terminated with success
1:M 26 Jun 2025 16:47:04.019 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 16:47:04.024 * Background saving started by pid 1014
1014:C 26 Jun 2025 16:47:04.029 * DB saved on disk
1014:C 26 Jun 2025 16:47:04.030 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 16:47:04.128 * Background saving terminated with success
1:M 26 Jun 2025 17:03:49.576 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 17:03:49.579 * Background saving started by pid 1250
1250:C 26 Jun 2025 17:03:49.588 * DB saved on disk
1250:C 26 Jun 2025 17:03:49.589 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 17:03:49.682 * Background saving terminated with success
1:M 26 Jun 2025 17:08:50.046 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 17:08:50.053 * Background saving started by pid 1321
1321:C 26 Jun 2025 17:08:50.059 * DB saved on disk
1321:C 26 Jun 2025 17:08:50.060 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 17:08:50.159 * Background saving terminated with success
1:M 26 Jun 2025 17:33:52.294 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 17:33:52.297 * Background saving started by pid 1519
1519:C 26 Jun 2025 17:33:52.309 * DB saved on disk
1519:C 26 Jun 2025 17:33:52.310 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 17:33:52.400 * Background saving terminated with success
1:M 26 Jun 2025 17:38:53.023 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 17:38:53.030 * Background saving started by pid 1589
1589:C 26 Jun 2025 17:38:53.035 * DB saved on disk
1589:C 26 Jun 2025 17:38:53.036 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 17:38:53.134 * Background saving terminated with success
1:M 26 Jun 2025 17:54:30.780 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 17:54:30.782 * Background saving started by pid 1806
1806:C 26 Jun 2025 17:54:30.785 * DB saved on disk
1806:C 26 Jun 2025 17:54:30.786 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 17:54:30.887 * Background saving terminated with success
1:M 26 Jun 2025 17:59:31.088 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 17:59:31.093 * Background saving started by pid 1877
1877:C 26 Jun 2025 17:59:31.099 * DB saved on disk
1877:C 26 Jun 2025 17:59:31.100 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 17:59:31.197 * Background saving terminated with success
1:M 26 Jun 2025 18:13:20.841 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 18:13:20.890 * Background saving started by pid 2074
2074:C 26 Jun 2025 18:13:20.902 * DB saved on disk
2074:C 26 Jun 2025 18:13:20.911 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 18:13:20.997 * Background saving terminated with success
1:M 26 Jun 2025 18:18:21.031 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 18:18:21.035 * Background saving started by pid 2144
2144:C 26 Jun 2025 18:18:21.039 * DB saved on disk
2144:C 26 Jun 2025 18:18:21.039 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 18:18:21.137 * Background saving terminated with success
1:M 26 Jun 2025 18:23:22.081 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 18:23:22.084 * Background saving started by pid 2207
2207:C 26 Jun 2025 18:23:22.087 * DB saved on disk
2207:C 26 Jun 2025 18:23:22.087 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 18:23:22.186 * Background saving terminated with success
1:M 26 Jun 2025 18:28:23.093 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 18:28:23.095 * Background saving started by pid 2278
2278:C 26 Jun 2025 18:28:23.098 * DB saved on disk
2278:C 26 Jun 2025 18:28:23.098 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 18:28:23.197 * Background saving terminated with success
1:M 26 Jun 2025 18:44:26.564 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 18:44:26.567 * Background saving started by pid 2503
2503:C 26 Jun 2025 18:44:26.573 * DB saved on disk
2503:C 26 Jun 2025 18:44:26.573 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 18:44:26.672 * Background saving terminated with success
1:M 26 Jun 2025 18:49:28.963 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 18:49:28.972 * Background saving started by pid 2574
2574:C 26 Jun 2025 18:49:29.003 * DB saved on disk
2574:C 26 Jun 2025 18:49:29.009 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 18:49:29.077 * Background saving terminated with success
1:M 26 Jun 2025 18:54:30.101 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 18:54:30.107 * Background saving started by pid 2644
2644:C 26 Jun 2025 18:54:30.111 * DB saved on disk
2644:C 26 Jun 2025 18:54:30.112 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 18:54:30.209 * Background saving terminated with success
1:M 26 Jun 2025 19:14:26.672 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 19:14:26.673 * Background saving started by pid 2926
2926:C 26 Jun 2025 19:14:26.676 * DB saved on disk
2926:C 26 Jun 2025 19:14:26.677 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 19:14:26.776 * Background saving terminated with success
1:M 26 Jun 2025 19:19:27.093 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 19:19:27.096 * Background saving started by pid 2997
2997:C 26 Jun 2025 19:19:27.100 * DB saved on disk
2997:C 26 Jun 2025 19:19:27.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 19:19:27.198 * Background saving terminated with success
1:M 26 Jun 2025 19:24:28.000 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 19:24:28.002 * Background saving started by pid 3067
3067:C 26 Jun 2025 19:24:28.005 * DB saved on disk
3067:C 26 Jun 2025 19:24:28.005 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 19:24:28.104 * Background saving terminated with success
1:M 26 Jun 2025 19:30:38.562 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 19:30:38.573 * Background saving started by pid 3153
3153:C 26 Jun 2025 19:30:38.575 * DB saved on disk
3153:C 26 Jun 2025 19:30:38.576 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 19:30:38.674 * Background saving terminated with success
1:M 26 Jun 2025 19:35:39.054 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 19:35:39.057 * Background saving started by pid 3224
3224:C 26 Jun 2025 19:35:39.065 * DB saved on disk
3224:C 26 Jun 2025 19:35:39.066 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 19:35:39.161 * Background saving terminated with success
1:M 26 Jun 2025 19:44:33.740 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 19:44:33.741 * Background saving started by pid 3349
3349:C 26 Jun 2025 19:44:33.745 * DB saved on disk
3349:C 26 Jun 2025 19:44:33.746 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 19:44:33.843 * Background saving terminated with success
1:M 26 Jun 2025 19:51:57.368 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 19:51:57.370 * Background saving started by pid 3455
3455:C 26 Jun 2025 19:51:57.372 * DB saved on disk
3455:C 26 Jun 2025 19:51:57.372 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 19:51:57.472 * Background saving terminated with success
1:M 26 Jun 2025 20:06:58.084 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 20:06:58.090 * Background saving started by pid 3666
3666:C 26 Jun 2025 20:06:58.101 * DB saved on disk
3666:C 26 Jun 2025 20:06:58.102 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 20:06:58.191 * Background saving terminated with success
1:M 26 Jun 2025 20:42:04.464 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 20:42:04.470 * Background saving started by pid 3876
3876:C 26 Jun 2025 20:42:04.476 * DB saved on disk
3876:C 26 Jun 2025 20:42:04.477 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 20:42:04.572 * Background saving terminated with success
1:M 26 Jun 2025 20:47:05.026 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 20:47:05.029 * Background saving started by pid 3947
3947:C 26 Jun 2025 20:47:05.034 * DB saved on disk
3947:C 26 Jun 2025 20:47:05.035 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 20:47:05.132 * Background saving terminated with success
1:M 26 Jun 2025 21:02:06.015 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 21:02:06.017 * Background saving started by pid 4158
4158:C 26 Jun 2025 21:02:06.019 * DB saved on disk
4158:C 26 Jun 2025 21:02:06.020 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 21:02:06.119 * Background saving terminated with success
1:M 26 Jun 2025 21:36:23.676 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 21:36:23.683 * Background saving started by pid 4641
4641:C 26 Jun 2025 21:36:23.698 * DB saved on disk
4641:C 26 Jun 2025 21:36:23.701 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 21:36:23.790 * Background saving terminated with success
1:M 26 Jun 2025 21:41:24.059 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 21:41:24.060 * Background saving started by pid 4712
4712:C 26 Jun 2025 21:41:24.062 * DB saved on disk
4712:C 26 Jun 2025 21:41:24.062 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 21:41:24.161 * Background saving terminated with success
1:M 26 Jun 2025 21:46:25.022 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 21:46:25.026 * Background saving started by pid 4782
4782:C 26 Jun 2025 21:46:25.033 * DB saved on disk
4782:C 26 Jun 2025 21:46:25.035 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 21:46:25.129 * Background saving terminated with success
1:M 26 Jun 2025 21:51:26.021 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 21:51:26.027 * Background saving started by pid 4853
4853:C 26 Jun 2025 21:51:26.032 * DB saved on disk
4853:C 26 Jun 2025 21:51:26.033 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 21:51:26.129 * Background saving terminated with success
1:M 26 Jun 2025 21:56:27.006 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 21:56:27.011 * Background saving started by pid 4924
4924:C 26 Jun 2025 21:56:27.016 * DB saved on disk
4924:C 26 Jun 2025 21:56:27.017 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 21:56:27.113 * Background saving terminated with success
1:M 26 Jun 2025 22:01:28.004 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 22:01:28.004 * Background saving started by pid 4995
4995:C 26 Jun 2025 22:01:28.007 * DB saved on disk
4995:C 26 Jun 2025 22:01:28.007 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 22:01:28.106 * Background saving terminated with success
1:M 26 Jun 2025 22:06:29.069 * 10 changes in 300 seconds. Saving...
1:M 26 Jun 2025 22:06:29.076 * Background saving started by pid 5066
5066:C 26 Jun 2025 22:06:29.082 * DB saved on disk
5066:C 26 Jun 2025 22:06:29.082 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 22:06:29.180 * Background saving terminated with success
1:M 26 Jun 2025 23:14:45.463 * 1 changes in 900 seconds. Saving...
1:M 26 Jun 2025 23:14:45.465 * Background saving started by pid 5123
5123:C 26 Jun 2025 23:14:45.467 * DB saved on disk
5123:C 26 Jun 2025 23:14:45.468 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 26 Jun 2025 23:14:45.567 * Background saving terminated with success
1:signal-handler (1750988369) Received SIGTERM scheduling shutdown...
1:M 27 Jun 2025 09:39:29.179 * User requested shutdown...
1:M 27 Jun 2025 09:39:29.181 * Calling fsync() on the AOF file.
1:M 27 Jun 2025 09:39:29.181 * Saving the final RDB snapshot before exiting.
1:M 27 Jun 2025 09:39:29.183 * DB saved on disk
1:M 27 Jun 2025 09:39:29.183 # Redis is now ready to exit, bye bye...
1:C 30 Jun 2025 09:49:45.295 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 30 Jun 2025 09:49:45.296 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 30 Jun 2025 09:49:45.297 * Configuration loaded
1:M 30 Jun 2025 09:49:45.299 * monotonic clock: POSIX clock_gettime
1:M 30 Jun 2025 09:49:45.300 * Running mode=standalone, port=6379.
1:M 30 Jun 2025 09:49:45.304 * Server initialized
1:M 30 Jun 2025 09:49:45.307 * Reading RDB base file on AOF loading...
1:M 30 Jun 2025 09:49:45.308 * Loading RDB produced by version 7.2.9
1:M 30 Jun 2025 09:49:45.309 * RDB age 325467 seconds
1:M 30 Jun 2025 09:49:45.310 * RDB memory usage when created 0.38 Mb
1:M 30 Jun 2025 09:49:45.311 * RDB is base AOF
1:M 30 Jun 2025 09:49:45.312 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 30 Jun 2025 09:49:45.312 * DB loaded from base file appendonly.aof.1.base.rdb: 0.006 seconds
1:M 30 Jun 2025 09:49:45.330 * DB loaded from incr file appendonly.aof.1.incr.aof: 0.017 seconds
1:M 30 Jun 2025 09:49:45.331 * DB loaded from append only file: 0.025 seconds
1:M 30 Jun 2025 09:49:45.332 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 30 Jun 2025 09:49:45.333 * Ready to accept connections tcp
1:M 30 Jun 2025 09:50:46.075 * 10000 changes in 60 seconds. Saving...
1:M 30 Jun 2025 09:50:46.076 * Background saving started by pid 36
36:C 30 Jun 2025 09:50:46.078 * DB saved on disk
36:C 30 Jun 2025 09:50:46.078 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 09:50:46.177 * Background saving terminated with success
1:M 30 Jun 2025 10:02:28.596 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 10:02:28.620 * Background saving started by pid 199
199:C 30 Jun 2025 10:02:28.629 * DB saved on disk
199:C 30 Jun 2025 10:02:28.630 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 10:02:28.722 * Background saving terminated with success
1:signal-handler (1751249070) Received SIGTERM scheduling shutdown...
1:M 30 Jun 2025 10:04:30.833 * User requested shutdown...
1:M 30 Jun 2025 10:04:30.836 * Calling fsync() on the AOF file.
1:M 30 Jun 2025 10:04:30.837 * Saving the final RDB snapshot before exiting.
1:M 30 Jun 2025 10:04:30.843 * DB saved on disk
1:M 30 Jun 2025 10:04:30.844 # Redis is now ready to exit, bye bye...
1:C 30 Jun 2025 10:04:31.148 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 30 Jun 2025 10:04:31.150 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 30 Jun 2025 10:04:31.151 * Configuration loaded
1:M 30 Jun 2025 10:04:31.152 * monotonic clock: POSIX clock_gettime
1:M 30 Jun 2025 10:04:31.154 * Running mode=standalone, port=6379.
1:M 30 Jun 2025 10:04:31.156 * Server initialized
1:M 30 Jun 2025 10:04:31.160 * Reading RDB base file on AOF loading...
1:M 30 Jun 2025 10:04:31.162 * Loading RDB produced by version 7.2.9
1:M 30 Jun 2025 10:04:31.163 * RDB age 326353 seconds
1:M 30 Jun 2025 10:04:31.165 * RDB memory usage when created 0.38 Mb
1:M 30 Jun 2025 10:04:31.166 * RDB is base AOF
1:M 30 Jun 2025 10:04:31.168 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 30 Jun 2025 10:04:31.169 * DB loaded from base file appendonly.aof.1.base.rdb: 0.010 seconds
1:M 30 Jun 2025 10:04:31.187 * DB loaded from incr file appendonly.aof.1.incr.aof: 0.017 seconds
1:M 30 Jun 2025 10:04:31.188 * DB loaded from append only file: 0.029 seconds
1:M 30 Jun 2025 10:04:31.190 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 30 Jun 2025 10:04:31.191 * Ready to accept connections tcp
1:signal-handler (1751249104) Received SIGTERM scheduling shutdown...
1:M 30 Jun 2025 10:05:04.519 * User requested shutdown...
1:M 30 Jun 2025 10:05:04.521 * Calling fsync() on the AOF file.
1:M 30 Jun 2025 10:05:04.523 * Saving the final RDB snapshot before exiting.
1:M 30 Jun 2025 10:05:04.528 * DB saved on disk
1:M 30 Jun 2025 10:05:04.529 # Redis is now ready to exit, bye bye...
1:C 30 Jun 2025 10:05:28.755 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 30 Jun 2025 10:05:28.757 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 30 Jun 2025 10:05:28.758 * Configuration loaded
1:M 30 Jun 2025 10:05:28.759 * monotonic clock: POSIX clock_gettime
1:M 30 Jun 2025 10:05:28.760 * Running mode=standalone, port=6379.
1:M 30 Jun 2025 10:05:28.761 * Server initialized
1:M 30 Jun 2025 10:05:28.763 * Reading RDB base file on AOF loading...
1:M 30 Jun 2025 10:05:28.764 * Loading RDB produced by version 7.2.9
1:M 30 Jun 2025 10:05:28.765 * RDB age 326410 seconds
1:M 30 Jun 2025 10:05:28.765 * RDB memory usage when created 0.38 Mb
1:M 30 Jun 2025 10:05:28.766 * RDB is base AOF
1:M 30 Jun 2025 10:05:28.766 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 30 Jun 2025 10:05:28.768 * DB loaded from base file appendonly.aof.1.base.rdb: 0.005 seconds
1:M 30 Jun 2025 10:05:28.807 * DB loaded from incr file appendonly.aof.1.incr.aof: 0.037 seconds
1:M 30 Jun 2025 10:05:28.808 * DB loaded from append only file: 0.046 seconds
1:M 30 Jun 2025 10:05:28.809 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 30 Jun 2025 10:05:28.810 * Ready to accept connections tcp
1:M 30 Jun 2025 10:06:29.024 * 10000 changes in 60 seconds. Saving...
1:M 30 Jun 2025 10:06:29.025 * Background saving started by pid 33
33:C 30 Jun 2025 10:06:29.028 * DB saved on disk
33:C 30 Jun 2025 10:06:29.028 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 10:06:29.127 * Background saving terminated with success
1:signal-handler (1751249262) Received SIGTERM scheduling shutdown...
1:M 30 Jun 2025 10:07:42.649 * User requested shutdown...
1:M 30 Jun 2025 10:07:42.650 * Calling fsync() on the AOF file.
1:M 30 Jun 2025 10:07:42.651 * Saving the final RDB snapshot before exiting.
1:M 30 Jun 2025 10:07:42.654 * DB saved on disk
1:M 30 Jun 2025 10:07:42.654 # Redis is now ready to exit, bye bye...
1:C 30 Jun 2025 10:08:01.525 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 30 Jun 2025 10:08:01.526 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 30 Jun 2025 10:08:01.528 * Configuration loaded
1:M 30 Jun 2025 10:08:01.531 * monotonic clock: POSIX clock_gettime
1:M 30 Jun 2025 10:08:01.533 * Running mode=standalone, port=6379.
1:M 30 Jun 2025 10:08:01.534 * Server initialized
1:M 30 Jun 2025 10:08:01.541 * Reading RDB base file on AOF loading...
1:M 30 Jun 2025 10:08:01.542 * Loading RDB produced by version 7.2.9
1:M 30 Jun 2025 10:08:01.544 * RDB age 326563 seconds
1:M 30 Jun 2025 10:08:01.548 * RDB memory usage when created 0.38 Mb
1:M 30 Jun 2025 10:08:01.553 * RDB is base AOF
1:M 30 Jun 2025 10:08:01.554 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 30 Jun 2025 10:08:01.554 * DB loaded from base file appendonly.aof.1.base.rdb: 0.015 seconds
1:M 30 Jun 2025 10:08:01.574 * DB loaded from incr file appendonly.aof.1.incr.aof: 0.018 seconds
1:M 30 Jun 2025 10:08:01.581 * DB loaded from append only file: 0.037 seconds
1:M 30 Jun 2025 10:08:01.597 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 30 Jun 2025 10:08:01.601 * Ready to accept connections tcp
1:M 30 Jun 2025 10:09:02.068 * 10000 changes in 60 seconds. Saving...
1:M 30 Jun 2025 10:09:02.069 * Background saving started by pid 36
36:C 30 Jun 2025 10:09:02.071 * DB saved on disk
36:C 30 Jun 2025 10:09:02.071 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 10:09:02.171 * Background saving terminated with success
1:M 30 Jun 2025 10:14:03.019 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 10:14:03.024 * Background saving started by pid 109
109:C 30 Jun 2025 10:14:03.028 * DB saved on disk
109:C 30 Jun 2025 10:14:03.029 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 10:14:03.128 * Background saving terminated with success
1:M 30 Jun 2025 10:19:04.010 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 10:19:04.015 * Background saving started by pid 179
179:C 30 Jun 2025 10:19:04.018 * DB saved on disk
179:C 30 Jun 2025 10:19:04.018 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 10:19:04.121 * Background saving terminated with success
1:M 30 Jun 2025 10:28:12.047 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 10:28:12.050 * Background saving started by pid 305
305:C 30 Jun 2025 10:28:12.053 * DB saved on disk
305:C 30 Jun 2025 10:28:12.053 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 10:28:12.151 * Background saving terminated with success
1:M 30 Jun 2025 10:33:13.071 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 10:33:13.077 * Background saving started by pid 376
376:C 30 Jun 2025 10:33:13.082 * DB saved on disk
376:C 30 Jun 2025 10:33:13.083 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 10:33:13.178 * Background saving terminated with success
1:M 30 Jun 2025 10:38:14.065 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 10:38:14.067 * Background saving started by pid 447
447:C 30 Jun 2025 10:38:14.070 * DB saved on disk
447:C 30 Jun 2025 10:38:14.070 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 10:38:14.169 * Background saving terminated with success
1:M 30 Jun 2025 10:43:15.001 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 10:43:15.005 * Background saving started by pid 517
517:C 30 Jun 2025 10:43:15.011 * DB saved on disk
517:C 30 Jun 2025 10:43:15.012 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 10:43:15.107 * Background saving terminated with success
1:M 30 Jun 2025 10:48:16.002 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 10:48:16.005 * Background saving started by pid 587
587:C 30 Jun 2025 10:48:16.011 * DB saved on disk
587:C 30 Jun 2025 10:48:16.011 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 10:48:16.107 * Background saving terminated with success
1:M 30 Jun 2025 11:03:17.092 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 11:03:17.096 * Background saving started by pid 795
795:C 30 Jun 2025 11:03:17.101 * DB saved on disk
795:C 30 Jun 2025 11:03:17.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 11:03:17.199 * Background saving terminated with success
1:M 30 Jun 2025 11:22:26.541 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 11:22:26.544 * Background saving started by pid 1060
1060:C 30 Jun 2025 11:22:26.550 * DB saved on disk
1060:C 30 Jun 2025 11:22:26.551 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 11:22:26.648 * Background saving terminated with success
1:M 30 Jun 2025 11:27:27.032 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 11:27:27.035 * Background saving started by pid 1130
1130:C 30 Jun 2025 11:27:27.040 * DB saved on disk
1130:C 30 Jun 2025 11:27:27.041 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 11:27:27.137 * Background saving terminated with success
1:M 30 Jun 2025 11:38:46.036 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 11:38:46.039 * Background saving started by pid 1291
1291:C 30 Jun 2025 11:38:46.044 * DB saved on disk
1291:C 30 Jun 2025 11:38:46.045 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 11:38:46.142 * Background saving terminated with success
1:M 30 Jun 2025 11:53:47.093 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 11:53:47.095 * Background saving started by pid 1502
1502:C 30 Jun 2025 11:53:47.097 * DB saved on disk
1502:C 30 Jun 2025 11:53:47.098 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 11:53:47.196 * Background saving terminated with success
1:M 30 Jun 2025 13:35:28.969 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 13:35:28.971 * Background saving started by pid 2870
2870:C 30 Jun 2025 13:35:28.975 * DB saved on disk
2870:C 30 Jun 2025 13:35:28.976 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 13:35:29.074 * Background saving terminated with success
1:M 30 Jun 2025 13:40:30.010 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 13:40:30.013 * Background saving started by pid 2941
2941:C 30 Jun 2025 13:40:30.020 * DB saved on disk
2941:C 30 Jun 2025 13:40:30.022 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 13:40:30.116 * Background saving terminated with success
1:M 30 Jun 2025 13:55:31.088 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 13:55:31.091 * Background saving started by pid 3148
3148:C 30 Jun 2025 13:55:31.094 * DB saved on disk
3148:C 30 Jun 2025 13:55:31.094 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 13:55:31.193 * Background saving terminated with success
1:M 30 Jun 2025 14:02:04.470 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 14:02:04.480 * Background saving started by pid 3236
3236:C 30 Jun 2025 14:02:04.494 * DB saved on disk
3236:C 30 Jun 2025 14:02:04.497 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 14:02:04.585 * Background saving terminated with success
1:M 30 Jun 2025 14:09:53.064 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 14:09:53.119 * Background saving started by pid 3338
3338:C 30 Jun 2025 14:09:53.146 * DB saved on disk
3338:C 30 Jun 2025 14:09:53.147 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 14:09:53.239 * Background saving terminated with success
1:M 30 Jun 2025 14:14:54.007 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 14:14:54.013 * Background saving started by pid 3409
3409:C 30 Jun 2025 14:14:54.021 * DB saved on disk
3409:C 30 Jun 2025 14:14:54.022 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 14:14:54.115 * Background saving terminated with success
1:M 30 Jun 2025 14:19:55.043 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 14:19:55.068 * Background saving started by pid 3480
3480:C 30 Jun 2025 14:19:55.081 * DB saved on disk
3480:C 30 Jun 2025 14:19:55.081 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 14:19:55.170 * Background saving terminated with success
1:M 30 Jun 2025 14:24:56.098 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 14:24:56.103 * Background saving started by pid 3549
3549:C 30 Jun 2025 14:24:56.109 * DB saved on disk
3549:C 30 Jun 2025 14:24:56.110 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 14:24:56.205 * Background saving terminated with success
1:M 30 Jun 2025 14:29:57.087 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 14:29:57.089 * Background saving started by pid 3618
3618:C 30 Jun 2025 14:29:57.103 * DB saved on disk
3618:C 30 Jun 2025 14:29:57.105 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 14:29:57.193 * Background saving terminated with success
1:M 30 Jun 2025 14:37:11.009 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 14:37:11.011 * Background saving started by pid 3725
3725:C 30 Jun 2025 14:37:11.014 * DB saved on disk
3725:C 30 Jun 2025 14:37:11.014 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 14:37:11.113 * Background saving terminated with success
1:M 30 Jun 2025 14:46:06.156 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 14:46:06.159 * Background saving started by pid 3852
3852:C 30 Jun 2025 14:46:06.162 * DB saved on disk
3852:C 30 Jun 2025 14:46:06.162 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 14:46:06.262 * Background saving terminated with success
1:M 30 Jun 2025 14:51:07.022 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 14:51:07.025 * Background saving started by pid 3923
3923:C 30 Jun 2025 14:51:07.029 * DB saved on disk
3923:C 30 Jun 2025 14:51:07.030 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 14:51:07.128 * Background saving terminated with success
1:M 30 Jun 2025 15:44:45.278 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 15:44:45.287 * Background saving started by pid 4669
4669:C 30 Jun 2025 15:44:45.295 * DB saved on disk
4669:C 30 Jun 2025 15:44:45.297 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 15:44:45.389 * Background saving terminated with success
1:M 30 Jun 2025 15:49:46.089 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 15:49:46.100 * Background saving started by pid 4740
4740:C 30 Jun 2025 15:49:46.118 * DB saved on disk
4740:C 30 Jun 2025 15:49:46.119 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 15:49:46.203 * Background saving terminated with success
1:M 30 Jun 2025 16:04:47.099 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 16:04:47.105 * Background saving started by pid 4949
4949:C 30 Jun 2025 16:04:47.112 * DB saved on disk
4949:C 30 Jun 2025 16:04:47.114 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 16:04:47.208 * Background saving terminated with success
1:M 30 Jun 2025 16:12:17.760 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 16:12:17.762 * Background saving started by pid 5054
5054:C 30 Jun 2025 16:12:17.766 * DB saved on disk
5054:C 30 Jun 2025 16:12:17.767 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 16:12:17.865 * Background saving terminated with success
1:M 30 Jun 2025 16:27:18.013 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 16:27:18.016 * Background saving started by pid 5263
5263:C 30 Jun 2025 16:27:18.023 * DB saved on disk
5263:C 30 Jun 2025 16:27:18.024 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 16:27:18.119 * Background saving terminated with success
1:M 30 Jun 2025 16:42:19.057 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 16:42:19.063 * Background saving started by pid 5473
5473:C 30 Jun 2025 16:42:19.067 * DB saved on disk
5473:C 30 Jun 2025 16:42:19.068 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 16:42:19.166 * Background saving terminated with success
1:M 30 Jun 2025 16:54:10.823 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 16:54:10.836 * Background saving started by pid 5635
5635:C 30 Jun 2025 16:54:10.842 * DB saved on disk
5635:C 30 Jun 2025 16:54:10.842 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 16:54:10.939 * Background saving terminated with success
1:M 30 Jun 2025 17:11:39.382 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 17:11:39.417 * Background saving started by pid 5678
5678:C 30 Jun 2025 17:11:39.479 * DB saved on disk
5678:C 30 Jun 2025 17:11:39.482 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 17:11:39.527 * Background saving terminated with success
1:M 30 Jun 2025 17:43:34.493 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 17:43:34.497 * Background saving started by pid 6126
6126:C 30 Jun 2025 17:43:34.500 * DB saved on disk
6126:C 30 Jun 2025 17:43:34.501 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 17:43:34.600 * Background saving terminated with success
1:M 30 Jun 2025 17:58:35.012 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 17:58:35.017 * Background saving started by pid 6337
6337:C 30 Jun 2025 17:58:35.022 * DB saved on disk
6337:C 30 Jun 2025 17:58:35.023 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 17:58:35.121 * Background saving terminated with success
1:M 30 Jun 2025 18:03:36.087 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 18:03:36.096 * Background saving started by pid 6409
6409:C 30 Jun 2025 18:03:36.100 * DB saved on disk
6409:C 30 Jun 2025 18:03:36.100 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 18:03:36.199 * Background saving terminated with success
1:M 30 Jun 2025 18:08:37.035 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 18:08:37.043 * Background saving started by pid 6480
6480:C 30 Jun 2025 18:08:37.046 * DB saved on disk
6480:C 30 Jun 2025 18:08:37.046 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 18:08:37.146 * Background saving terminated with success
1:M 30 Jun 2025 18:13:38.065 * 10 changes in 300 seconds. Saving...
1:M 30 Jun 2025 18:13:38.066 * Background saving started by pid 6549
6549:C 30 Jun 2025 18:13:38.069 * DB saved on disk
6549:C 30 Jun 2025 18:13:38.070 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 18:13:38.168 * Background saving terminated with success
1:M 30 Jun 2025 18:28:39.022 * 1 changes in 900 seconds. Saving...
1:M 30 Jun 2025 18:28:39.027 * Background saving started by pid 6757
6757:C 30 Jun 2025 18:28:39.033 * DB saved on disk
6757:C 30 Jun 2025 18:28:39.033 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 30 Jun 2025 18:28:39.129 * Background saving terminated with success
1:M 01 Jul 2025 10:09:42.495 * 1 changes in 900 seconds. Saving...
1:M 01 Jul 2025 10:09:42.497 * Background saving started by pid 8298
8298:C 01 Jul 2025 10:09:42.502 * DB saved on disk
8298:C 01 Jul 2025 10:09:42.502 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 01 Jul 2025 10:09:42.599 * Background saving terminated with success
1:M 01 Jul 2025 10:14:43.076 * 10 changes in 300 seconds. Saving...
1:M 01 Jul 2025 10:14:43.080 * Background saving started by pid 8368
8368:C 01 Jul 2025 10:14:43.087 * DB saved on disk
8368:C 01 Jul 2025 10:14:43.088 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 01 Jul 2025 10:14:43.185 * Background saving terminated with success
1:M 01 Jul 2025 10:21:21.299 * 10 changes in 300 seconds. Saving...
1:M 01 Jul 2025 10:21:21.300 * Background saving started by pid 8459
8459:C 01 Jul 2025 10:21:21.305 * DB saved on disk
8459:C 01 Jul 2025 10:21:21.307 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 01 Jul 2025 10:21:21.403 * Background saving terminated with success
1:M 01 Jul 2025 10:47:59.406 * 1 changes in 900 seconds. Saving...
1:M 01 Jul 2025 10:47:59.407 * Background saving started by pid 8834
8834:C 01 Jul 2025 10:47:59.409 * DB saved on disk
8834:C 01 Jul 2025 10:47:59.410 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 01 Jul 2025 10:47:59.509 * Background saving terminated with success
1:signal-handler (1751338338) Received SIGTERM scheduling shutdown...
1:M 01 Jul 2025 10:52:18.936 * User requested shutdown...
1:M 01 Jul 2025 10:52:18.937 * Calling fsync() on the AOF file.
1:M 01 Jul 2025 10:52:18.938 * Saving the final RDB snapshot before exiting.
1:M 01 Jul 2025 10:52:18.943 * DB saved on disk
1:M 01 Jul 2025 10:52:18.943 # Redis is now ready to exit, bye bye...
1:C 02 Jul 2025 09:58:19.597 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 02 Jul 2025 09:58:19.598 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 02 Jul 2025 09:58:19.599 * Configuration loaded
1:M 02 Jul 2025 09:58:19.600 * monotonic clock: POSIX clock_gettime
1:M 02 Jul 2025 09:58:19.603 * Running mode=standalone, port=6379.
1:M 02 Jul 2025 09:58:19.604 * Server initialized
1:M 02 Jul 2025 09:58:19.607 * Reading RDB base file on AOF loading...
1:M 02 Jul 2025 09:58:19.608 * Loading RDB produced by version 7.2.9
1:M 02 Jul 2025 09:58:19.609 * RDB age 498781 seconds
1:M 02 Jul 2025 09:58:19.609 * RDB memory usage when created 0.38 Mb
1:M 02 Jul 2025 09:58:19.609 * RDB is base AOF
1:M 02 Jul 2025 09:58:19.610 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 02 Jul 2025 09:58:19.611 * DB loaded from base file appendonly.aof.1.base.rdb: 0.005 seconds
1:M 02 Jul 2025 09:58:19.629 * DB loaded from incr file appendonly.aof.1.incr.aof: 0.017 seconds
1:M 02 Jul 2025 09:58:19.629 * DB loaded from append only file: 0.024 seconds
1:M 02 Jul 2025 09:58:19.630 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 02 Jul 2025 09:58:19.631 * Ready to accept connections tcp
1:M 02 Jul 2025 09:59:20.026 * 10000 changes in 60 seconds. Saving...
1:M 02 Jul 2025 09:59:20.026 * Background saving started by pid 36
36:C 02 Jul 2025 09:59:20.034 * DB saved on disk
36:C 02 Jul 2025 09:59:20.034 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 09:59:20.128 * Background saving terminated with success
1:signal-handler (1751424507) Received SIGTERM scheduling shutdown...
1:M 02 Jul 2025 10:48:28.023 * User requested shutdown...
1:M 02 Jul 2025 10:48:28.024 * Calling fsync() on the AOF file.
1:M 02 Jul 2025 10:48:28.024 * Saving the final RDB snapshot before exiting.
1:M 02 Jul 2025 10:48:28.026 * DB saved on disk
1:M 02 Jul 2025 10:48:28.026 # Redis is now ready to exit, bye bye...
1:C 02 Jul 2025 14:18:36.901 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 02 Jul 2025 14:18:36.903 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 02 Jul 2025 14:18:36.912 * Configuration loaded
1:M 02 Jul 2025 14:18:36.915 * monotonic clock: POSIX clock_gettime
1:M 02 Jul 2025 14:18:36.921 * Running mode=standalone, port=6379.
1:M 02 Jul 2025 14:18:36.923 * Server initialized
1:M 02 Jul 2025 14:18:36.927 * Reading RDB base file on AOF loading...
1:M 02 Jul 2025 14:18:36.928 * Loading RDB produced by version 7.2.9
1:M 02 Jul 2025 14:18:36.932 * RDB age 514398 seconds
1:M 02 Jul 2025 14:18:36.933 * RDB memory usage when created 0.38 Mb
1:M 02 Jul 2025 14:18:36.933 * RDB is base AOF
1:M 02 Jul 2025 14:18:36.934 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 02 Jul 2025 14:18:36.937 * DB loaded from base file appendonly.aof.1.base.rdb: 0.011 seconds
1:M 02 Jul 2025 14:18:36.960 * DB loaded from incr file appendonly.aof.1.incr.aof: 0.022 seconds
1:M 02 Jul 2025 14:18:36.962 * DB loaded from append only file: 0.036 seconds
1:M 02 Jul 2025 14:18:36.967 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 02 Jul 2025 14:18:36.969 * Ready to accept connections tcp
1:signal-handler (1751437163) Received SIGTERM scheduling shutdown...
1:M 02 Jul 2025 14:19:23.307 * User requested shutdown...
1:M 02 Jul 2025 14:19:23.308 * Calling fsync() on the AOF file.
1:M 02 Jul 2025 14:19:23.309 * Saving the final RDB snapshot before exiting.
1:M 02 Jul 2025 14:19:23.314 * DB saved on disk
1:M 02 Jul 2025 14:19:23.317 # Redis is now ready to exit, bye bye...
1:C 02 Jul 2025 14:22:37.179 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 02 Jul 2025 14:22:37.180 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 02 Jul 2025 14:22:37.181 * Configuration loaded
1:M 02 Jul 2025 14:22:37.182 * monotonic clock: POSIX clock_gettime
1:M 02 Jul 2025 14:22:37.183 * Running mode=standalone, port=6379.
1:M 02 Jul 2025 14:22:37.184 * Server initialized
1:M 02 Jul 2025 14:22:37.187 * Reading RDB base file on AOF loading...
1:M 02 Jul 2025 14:22:37.187 * Loading RDB produced by version 7.2.9
1:M 02 Jul 2025 14:22:37.188 * RDB age 514639 seconds
1:M 02 Jul 2025 14:22:37.189 * RDB memory usage when created 0.38 Mb
1:M 02 Jul 2025 14:22:37.189 * RDB is base AOF
1:M 02 Jul 2025 14:22:37.190 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 02 Jul 2025 14:22:37.191 * DB loaded from base file appendonly.aof.1.base.rdb: 0.005 seconds
1:M 02 Jul 2025 14:22:37.207 * DB loaded from incr file appendonly.aof.1.incr.aof: 0.015 seconds
1:M 02 Jul 2025 14:22:37.208 * DB loaded from append only file: 0.022 seconds
1:M 02 Jul 2025 14:22:37.209 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 02 Jul 2025 14:22:37.210 * Ready to accept connections tcp
1:M 02 Jul 2025 14:23:38.025 * 10000 changes in 60 seconds. Saving...
1:M 02 Jul 2025 14:23:38.027 * Background saving started by pid 36
36:C 02 Jul 2025 14:23:38.029 * DB saved on disk
36:C 02 Jul 2025 14:23:38.030 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 14:23:38.128 * Background saving terminated with success
1:M 02 Jul 2025 14:29:06.604 * 10 changes in 300 seconds. Saving...
1:M 02 Jul 2025 14:29:06.606 * Background saving started by pid 107
107:C 02 Jul 2025 14:29:06.616 * DB saved on disk
107:C 02 Jul 2025 14:29:06.617 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 14:29:06.709 * Background saving terminated with success
1:signal-handler (1751437917) Received SIGTERM scheduling shutdown...
1:M 02 Jul 2025 14:31:57.848 * User requested shutdown...
1:M 02 Jul 2025 14:31:57.852 * Calling fsync() on the AOF file.
1:M 02 Jul 2025 14:31:57.853 * Saving the final RDB snapshot before exiting.
1:M 02 Jul 2025 14:31:57.857 * DB saved on disk
1:M 02 Jul 2025 14:31:57.857 # Redis is now ready to exit, bye bye...
1:C 02 Jul 2025 17:08:18.266 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 02 Jul 2025 17:08:18.267 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 02 Jul 2025 17:08:18.270 * Configuration loaded
1:M 02 Jul 2025 17:08:18.272 * monotonic clock: POSIX clock_gettime
1:M 02 Jul 2025 17:08:18.274 * Running mode=standalone, port=6379.
1:M 02 Jul 2025 17:08:18.288 * Server initialized
1:M 02 Jul 2025 17:08:18.290 * Reading RDB base file on AOF loading...
1:M 02 Jul 2025 17:08:18.292 * Loading RDB produced by version 7.2.9
1:M 02 Jul 2025 17:08:18.293 * RDB age 524580 seconds
1:M 02 Jul 2025 17:08:18.297 * RDB memory usage when created 0.38 Mb
1:M 02 Jul 2025 17:08:18.300 * RDB is base AOF
1:M 02 Jul 2025 17:08:18.303 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 02 Jul 2025 17:08:18.305 * DB loaded from base file appendonly.aof.1.base.rdb: 0.015 seconds
1:M 02 Jul 2025 17:08:18.330 * DB loaded from incr file appendonly.aof.1.incr.aof: 0.024 seconds
1:M 02 Jul 2025 17:08:18.334 * DB loaded from append only file: 0.043 seconds
1:M 02 Jul 2025 17:08:18.339 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 02 Jul 2025 17:08:18.341 * Ready to accept connections tcp
1:M 02 Jul 2025 17:09:19.019 * 10000 changes in 60 seconds. Saving...
1:M 02 Jul 2025 17:09:19.070 * Background saving started by pid 36
36:C 02 Jul 2025 17:09:19.091 * DB saved on disk
36:C 02 Jul 2025 17:09:19.096 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 17:09:19.188 * Background saving terminated with success
1:M 02 Jul 2025 17:14:20.066 * 10 changes in 300 seconds. Saving...
1:M 02 Jul 2025 17:14:20.074 * Background saving started by pid 108
108:C 02 Jul 2025 17:14:20.081 * DB saved on disk
108:C 02 Jul 2025 17:14:20.082 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 17:14:20.177 * Background saving terminated with success
1:M 02 Jul 2025 17:42:14.477 * 1 changes in 900 seconds. Saving...
1:M 02 Jul 2025 17:42:14.528 * Background saving started by pid 491
491:C 02 Jul 2025 17:42:14.626 * DB saved on disk
491:C 02 Jul 2025 17:42:14.627 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 17:42:14.671 * Background saving terminated with success
1:M 02 Jul 2025 17:47:15.078 * 10 changes in 300 seconds. Saving...
1:M 02 Jul 2025 17:47:15.082 * Background saving started by pid 562
562:C 02 Jul 2025 17:47:15.087 * DB saved on disk
562:C 02 Jul 2025 17:47:15.087 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 17:47:15.186 * Background saving terminated with success
1:M 02 Jul 2025 17:52:16.068 * 10 changes in 300 seconds. Saving...
1:M 02 Jul 2025 17:52:16.071 * Background saving started by pid 631
631:C 02 Jul 2025 17:52:16.074 * DB saved on disk
631:C 02 Jul 2025 17:52:16.075 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 17:52:16.173 * Background saving terminated with success
1:M 02 Jul 2025 17:57:17.021 * 10 changes in 300 seconds. Saving...
1:M 02 Jul 2025 17:57:17.023 * Background saving started by pid 702
702:C 02 Jul 2025 17:57:17.026 * DB saved on disk
702:C 02 Jul 2025 17:57:17.027 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 17:57:17.125 * Background saving terminated with success
1:M 02 Jul 2025 18:10:32.342 * 10 changes in 300 seconds. Saving...
1:M 02 Jul 2025 18:10:32.345 * Background saving started by pid 892
892:C 02 Jul 2025 18:10:32.350 * DB saved on disk
892:C 02 Jul 2025 18:10:32.350 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 18:10:32.448 * Background saving terminated with success
1:M 02 Jul 2025 18:15:33.041 * 10 changes in 300 seconds. Saving...
1:M 02 Jul 2025 18:15:33.043 * Background saving started by pid 963
963:C 02 Jul 2025 18:15:33.047 * DB saved on disk
963:C 02 Jul 2025 18:15:33.048 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 18:15:33.145 * Background saving terminated with success
1:M 02 Jul 2025 18:20:34.052 * 10 changes in 300 seconds. Saving...
1:M 02 Jul 2025 18:20:34.054 * Background saving started by pid 1035
1035:C 02 Jul 2025 18:20:34.057 * DB saved on disk
1035:C 02 Jul 2025 18:20:34.058 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 18:20:34.156 * Background saving terminated with success
1:M 02 Jul 2025 18:25:35.091 * 10 changes in 300 seconds. Saving...
1:M 02 Jul 2025 18:25:35.092 * Background saving started by pid 1105
1105:C 02 Jul 2025 18:25:35.096 * DB saved on disk
1105:C 02 Jul 2025 18:25:35.097 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 18:25:35.194 * Background saving terminated with success
1:M 02 Jul 2025 18:30:36.089 * 10 changes in 300 seconds. Saving...
1:M 02 Jul 2025 18:30:36.095 * Background saving started by pid 1176
1176:C 02 Jul 2025 18:30:36.100 * DB saved on disk
1176:C 02 Jul 2025 18:30:36.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 02 Jul 2025 18:30:36.197 * Background saving terminated with success
1:signal-handler (1751456273) Received SIGTERM scheduling shutdown...
1:M 02 Jul 2025 19:37:53.133 * User requested shutdown...
1:M 02 Jul 2025 19:37:53.134 * Calling fsync() on the AOF file.
1:M 02 Jul 2025 19:37:53.134 * Saving the final RDB snapshot before exiting.
1:M 02 Jul 2025 19:37:53.137 * DB saved on disk
1:M 02 Jul 2025 19:37:53.138 # Redis is now ready to exit, bye bye...
1:C 17 Jul 2025 15:32:10.699 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 17 Jul 2025 15:32:10.699 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 17 Jul 2025 15:32:10.700 * Configuration loaded
1:M 17 Jul 2025 15:32:10.700 * monotonic clock: POSIX clock_gettime
1:M 17 Jul 2025 15:32:10.700 * Running mode=standalone, port=6379.
1:M 17 Jul 2025 15:32:10.700 * Server initialized
1:M 17 Jul 2025 15:32:10.701 * Reading RDB base file on AOF loading...
1:M 17 Jul 2025 15:32:10.701 * Loading RDB produced by version 7.2.9
1:M 17 Jul 2025 15:32:10.701 * RDB age 1814812 seconds
1:M 17 Jul 2025 15:32:10.702 * RDB memory usage when created 0.38 Mb
1:M 17 Jul 2025 15:32:10.702 * RDB is base AOF
1:M 17 Jul 2025 15:32:10.702 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 17 Jul 2025 15:32:10.702 * DB loaded from base file appendonly.aof.1.base.rdb: 0.001 seconds
1:M 17 Jul 2025 15:32:10.709 * DB loaded from incr file appendonly.aof.1.incr.aof: 0.007 seconds
1:M 17 Jul 2025 15:32:10.709 * DB loaded from append only file: 0.009 seconds
1:M 17 Jul 2025 15:32:10.710 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 17 Jul 2025 15:32:10.710 * Ready to accept connections tcp
1:M 17 Jul 2025 15:33:11.085 * 10000 changes in 60 seconds. Saving...
1:M 17 Jul 2025 15:33:11.086 * Background saving started by pid 36
36:C 17 Jul 2025 15:33:11.087 * DB saved on disk
36:C 17 Jul 2025 15:33:11.088 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 17 Jul 2025 15:33:11.187 * Background saving terminated with success
1:M 17 Jul 2025 15:38:12.062 * 10 changes in 300 seconds. Saving...
1:M 17 Jul 2025 15:38:12.064 * Background saving started by pid 108
108:C 17 Jul 2025 15:38:12.070 * DB saved on disk
108:C 17 Jul 2025 15:38:12.071 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 17 Jul 2025 15:38:12.167 * Background saving terminated with success
1:M 17 Jul 2025 15:43:25.292 * 10 changes in 300 seconds. Saving...
1:M 17 Jul 2025 15:43:25.293 * Background saving started by pid 179
179:C 17 Jul 2025 15:43:25.297 * DB saved on disk
179:C 17 Jul 2025 15:43:25.297 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 17 Jul 2025 15:43:25.397 * Background saving terminated with success
1:M 17 Jul 2025 15:48:26.044 * 10 changes in 300 seconds. Saving...
1:M 17 Jul 2025 15:48:26.047 * Background saving started by pid 250
250:C 17 Jul 2025 15:48:26.053 * DB saved on disk
250:C 17 Jul 2025 15:48:26.056 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 17 Jul 2025 15:48:26.149 * Background saving terminated with success
1:M 17 Jul 2025 16:02:33.823 * 10 changes in 300 seconds. Saving...
1:M 17 Jul 2025 16:02:33.824 * Background saving started by pid 447
447:C 17 Jul 2025 16:02:33.828 * DB saved on disk
447:C 17 Jul 2025 16:02:33.829 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 17 Jul 2025 16:02:33.927 * Background saving terminated with success
1:M 17 Jul 2025 16:07:34.002 * 10 changes in 300 seconds. Saving...
1:M 17 Jul 2025 16:07:34.004 * Background saving started by pid 518
518:C 17 Jul 2025 16:07:34.007 * DB saved on disk
518:C 17 Jul 2025 16:07:34.008 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 17 Jul 2025 16:07:34.107 * Background saving terminated with success
1:M 17 Jul 2025 16:12:35.101 * 10 changes in 300 seconds. Saving...
1:M 17 Jul 2025 16:12:35.103 * Background saving started by pid 589
589:C 17 Jul 2025 16:12:35.107 * DB saved on disk
589:C 17 Jul 2025 16:12:35.108 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 17 Jul 2025 16:12:35.206 * Background saving terminated with success
1:M 17 Jul 2025 19:26:40.789 # Possible SECURITY ATTACK detected. It looks like somebody is sending POST or Host: commands to Redis. This is likely due to an attacker attempting to use Cross Protocol Scripting to compromise your Redis instance. Connection from ************:17929 aborted.
1:M 21 Jul 2025 15:32:20.461 * 1 changes in 900 seconds. Saving...
1:M 21 Jul 2025 15:32:20.463 * Background saving started by pid 16472
16472:C 21 Jul 2025 15:32:20.467 * DB saved on disk
16472:C 21 Jul 2025 15:32:20.468 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 21 Jul 2025 15:32:20.567 * Background saving terminated with success
1:M 21 Jul 2025 15:41:24.424 * 10 changes in 300 seconds. Saving...
1:M 21 Jul 2025 15:41:24.425 * Background saving started by pid 16599
16599:C 21 Jul 2025 15:41:24.427 * DB saved on disk
16599:C 21 Jul 2025 15:41:24.427 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 21 Jul 2025 15:41:24.528 * Background saving terminated with success
1:M 21 Jul 2025 15:45:01.935 # Possible SECURITY ATTACK detected. It looks like somebody is sending POST or Host: commands to Redis. This is likely due to an attacker attempting to use Cross Protocol Scripting to compromise your Redis instance. Connection from ************:65531 aborted.
1:M 21 Jul 2025 15:46:25.070 * 10 changes in 300 seconds. Saving...
1:M 21 Jul 2025 15:46:25.071 * Background saving started by pid 16668
16668:C 21 Jul 2025 15:46:25.074 * DB saved on disk
16668:C 21 Jul 2025 15:46:25.075 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 21 Jul 2025 15:46:25.177 * Background saving terminated with success
1:M 21 Jul 2025 15:52:29.967 * 10 changes in 300 seconds. Saving...
1:M 21 Jul 2025 15:52:29.969 * Background saving started by pid 16753
16753:C 21 Jul 2025 15:52:29.972 * DB saved on disk
16753:C 21 Jul 2025 15:52:29.973 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 21 Jul 2025 15:52:30.074 * Background saving terminated with success
1:M 21 Jul 2025 16:07:31.051 * 1 changes in 900 seconds. Saving...
1:M 21 Jul 2025 16:07:31.054 * Background saving started by pid 16964
16964:C 21 Jul 2025 16:07:31.059 * DB saved on disk
16964:C 21 Jul 2025 16:07:31.059 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 21 Jul 2025 16:07:31.158 * Background saving terminated with success
1:M 21 Jul 2025 16:46:25.537 * 1 changes in 900 seconds. Saving...
1:M 21 Jul 2025 16:46:25.539 * Background saving started by pid 17497
17497:C 21 Jul 2025 16:46:25.541 * DB saved on disk
17497:C 21 Jul 2025 16:46:25.542 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 21 Jul 2025 16:46:25.644 * Background saving terminated with success
1:M 21 Jul 2025 17:01:26.054 * 1 changes in 900 seconds. Saving...
1:M 21 Jul 2025 17:01:26.059 * Background saving started by pid 17708
17708:C 21 Jul 2025 17:01:26.070 * DB saved on disk
17708:C 21 Jul 2025 17:01:26.071 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 21 Jul 2025 17:01:26.165 * Background saving terminated with success
1:M 22 Jul 2025 10:45:12.221 * 1 changes in 900 seconds. Saving...
1:M 22 Jul 2025 10:45:12.224 * Background saving started by pid 21077
21077:C 22 Jul 2025 10:45:12.232 * DB saved on disk
21077:C 22 Jul 2025 10:45:12.233 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 22 Jul 2025 10:45:12.325 * Background saving terminated with success
1:M 22 Jul 2025 10:50:13.045 * 10 changes in 300 seconds. Saving...
1:M 22 Jul 2025 10:50:13.051 * Background saving started by pid 21148
21148:C 22 Jul 2025 10:50:13.059 * DB saved on disk
21148:C 22 Jul 2025 10:50:13.060 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 22 Jul 2025 10:50:13.159 * Background saving terminated with success
1:M 22 Jul 2025 11:40:01.615 * 1 changes in 900 seconds. Saving...
1:M 22 Jul 2025 11:40:01.616 * Background saving started by pid 21848
21848:C 22 Jul 2025 11:40:01.619 * DB saved on disk
21848:C 22 Jul 2025 11:40:01.620 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 22 Jul 2025 11:40:01.723 * Background saving terminated with success
1:signal-handler (1753235061) Received SIGTERM scheduling shutdown...
1:M 23 Jul 2025 09:44:21.374 * User requested shutdown...
1:M 23 Jul 2025 09:44:21.375 * Calling fsync() on the AOF file.
1:M 23 Jul 2025 09:44:21.375 * Saving the final RDB snapshot before exiting.
1:M 23 Jul 2025 09:44:21.381 * DB saved on disk
1:M 23 Jul 2025 09:44:21.382 # Redis is now ready to exit, bye bye...
1:C 24 Jul 2025 14:07:47.717 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 24 Jul 2025 14:07:47.717 * Redis version=7.2.9, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 24 Jul 2025 14:07:47.718 * Configuration loaded
1:M 24 Jul 2025 14:07:47.718 * monotonic clock: POSIX clock_gettime
1:M 24 Jul 2025 14:07:47.718 * Running mode=standalone, port=6379.
1:M 24 Jul 2025 14:07:47.719 * Server initialized
1:M 24 Jul 2025 14:07:47.720 * Reading RDB base file on AOF loading...
1:M 24 Jul 2025 14:07:47.720 * Loading RDB produced by version 7.2.9
1:M 24 Jul 2025 14:07:47.721 * RDB age 2414549 seconds
1:M 24 Jul 2025 14:07:47.721 * RDB memory usage when created 0.38 Mb
1:M 24 Jul 2025 14:07:47.721 * RDB is base AOF
1:M 24 Jul 2025 14:07:47.721 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 24 Jul 2025 14:07:47.721 * DB loaded from base file appendonly.aof.1.base.rdb: 0.002 seconds
1:M 24 Jul 2025 14:07:47.732 * DB loaded from incr file appendonly.aof.1.incr.aof: 0.010 seconds
1:M 24 Jul 2025 14:07:47.732 * DB loaded from append only file: 0.013 seconds
1:M 24 Jul 2025 14:07:47.732 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 24 Jul 2025 14:07:47.733 * Ready to accept connections tcp
1:M 24 Jul 2025 14:08:48.020 * 10000 changes in 60 seconds. Saving...
1:M 24 Jul 2025 14:08:48.021 * Background saving started by pid 35
35:C 24 Jul 2025 14:08:48.023 * DB saved on disk
35:C 24 Jul 2025 14:08:48.023 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
1:M 24 Jul 2025 14:08:48.123 * Background saving terminated with success
