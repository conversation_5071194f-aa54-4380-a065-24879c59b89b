requirepass 0lsF0uvsjYpZ96G
# Redis 7.2 优化配置 - 针对1GB内存限制

# 网络设置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# 内存设置 - 最大使用800MB，留200MB给系统
maxmemory 800mb
maxmemory-policy allkeys-lru

# 持久化设置
save 900 1
save 300 10
save 60 10000

# AOF持久化
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志设置
loglevel notice
logfile /var/log/redis/redis.log

# 客户端连接
maxclients 1000

# 性能优化
tcp-backlog 511
databases 16

# 安全设置
protected-mode no
# requirepass your_password_here

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 内存优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# 其他优化
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
