version: '3.8'
services:
  redis:
    image: redis:7.2
    container_name: ecp-redis
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ${ROOT_ENV_DIR}/ecp-redis/log:/var/log/redis
      - ${ROOT_ENV_DIR}/ecp-redis/data:/data
      - ${ROOT_ENV_DIR}/ecp-redis/conf:/usr/local/etc/redis
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "6379:6379"
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    restart: always

    # 内存限制 - 1GB
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
