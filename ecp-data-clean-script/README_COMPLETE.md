人工顺序执行：

-- 清理垃圾数据
drop database jlr_ecp_test;
drop table ecp_inventory.t_inventory_backup_20250414;
drop table ecp_order.t_order_info_backup_20250319;
drop table ecp_order.t_order_item_backup_20250319;
drop table ecp_order.t_order_refund_backup_20250319;
drop table ecp_order.t_order_refund_item_backup_20250319;
drop table ecp_order.t_order_info_bak;
drop table ecp_product.t_product_detail_backup_20250414;


truncate table ecp_subscription.t_vin_initial_temp;
truncate table ecp_system.t_infra_api_access_log;
truncate table ecp_xxl_job.xxl_job_log;
delete from ecp_subscription.t_appdcu_renew_records where created_time < '2025-01-01 00:00:00';
delete from ecp_subscription.t_dms_oss_original_data where created_time < '2025-02-26 00:00:00';
delete from ecp_subscription.t_incontrol_customer where created_time < '2025-01-01 00:00:00';
delete from ecp_subscription.t_remote_original_data where created_time < '2025-01-01 00:00:00';
delete from ecp_subscription.t_subscription_service where created_time < '2025-01-01 00:00:00';
delete from ecp_system.t_system_operate_log  where created_time < '2025-06-01 00:00:00';


-- 1. 创建临时表存储需要删除的车辆VIN
drop table if exists temp_vins_to_delete;
CREATE TEMPORARY TABLE temp_vins_to_delete AS
SELECT distinct car_vin FROM ecp_subscription.t_incontrol_vehicle
WHERE created_time > '2025-03-01 00:00:00';

-- 2. 为临时表添加索引
ALTER TABLE temp_vins_to_delete ADD PRIMARY KEY (car_vin);

-- 3. 创建临时表存储需要删除的订单编码
drop table if exists temp_orders_to_delete;
CREATE TEMPORARY TABLE temp_orders_to_delete AS
SELECT distinct order_code FROM ecp_order.t_vcs_order_info
WHERE car_vin NOT IN (SELECT car_vin FROM temp_vins_to_delete);

-- 4. 为临时表添加索引
ALTER TABLE temp_orders_to_delete ADD PRIMARY KEY (order_code);

DELETE FROM ecp_order.t_order_status_log
WHERE order_code IN (SELECT order_code FROM temp_orders_to_delete);

DELETE FROM ecp_order.t_order_info
WHERE order_code IN (SELECT order_code FROM temp_orders_to_delete);

-- 7. 分批删除VCS订单信息 (优化版)
DELIMITER //
DROP PROCEDURE IF EXISTS batch_delete_vcs_orders //
CREATE PROCEDURE batch_delete_vcs_orders()
BEGIN
DECLARE affected_rows INT;
DECLARE batch_size INT DEFAULT 5000; -- 减小批量大小
DECLARE sleep_interval INT DEFAULT 1; -- 每批之间暂停时间(秒)

    -- 创建临时表存储要删除的记录ID
    DROP TEMPORARY TABLE IF EXISTS temp_vcs_orders_batch;
    CREATE TEMPORARY TABLE temp_vcs_orders_batch (
                                                     id BIGINT PRIMARY KEY
    );

    REPEAT
        -- 清空临时表
        TRUNCATE TABLE temp_vcs_orders_batch;

        -- 获取一批要删除的记录ID
        INSERT INTO temp_vcs_orders_batch
        SELECT id FROM ecp_order.t_vcs_order_info
        WHERE car_vin NOT IN (SELECT car_vin FROM temp_vins_to_delete)
        LIMIT batch_size;

        -- 基于ID删除记录(更高效)
        DELETE FROM ecp_order.t_vcs_order_info
        WHERE id IN (SELECT id FROM temp_vcs_orders_batch);

        SET affected_rows = ROW_COUNT();

        -- 短暂休息，减轻数据库负担
        DO SLEEP(sleep_interval);

    UNTIL affected_rows = 0 END REPEAT;

    DROP TEMPORARY TABLE IF EXISTS temp_vcs_orders_batch;
END //
DELIMITER ;

CALL batch_delete_vcs_orders();

DELETE FROM ecp_subscription.t_incontrol_vehicle
WHERE car_vin not in (SELECT car_vin FROM temp_vins_to_delete);

-- 9. 清理临时表
DROP TEMPORARY TABLE IF EXISTS temp_vins_to_delete;
DROP TEMPORARY TABLE IF EXISTS temp_orders_to_delete;

-- 更新账户密码
update ecp_system.t_system_user set password = '$2a$04$zK.ssGDPKypHzi6d/uqpY.6URR21g4j34aW7EZe0QcrmUz9ldNn0K' where length(password) > 0;

-- 检查大表数据
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_consumer','t_consumer_channel' from ecp_consumer.t_consumer_channel HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_consumer','t_consumer_info' from ecp_consumer.t_consumer_info HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_consumer','t_customer_browse' from ecp_consumer.t_customer_browse HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_amap_activate_record' from ecp_subscription.t_amap_activate_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_amap_initial_data' from ecp_subscription.t_amap_initial_data HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_amap_query_batch_records' from ecp_subscription.t_amap_query_batch_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_amap_query_records' from ecp_subscription.t_amap_query_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_amap_renew_batch_records' from ecp_subscription.t_amap_renew_batch_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_amap_renew_records' from ecp_subscription.t_amap_renew_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_amap_temp_20240826' from ecp_subscription.t_amap_temp_20240826 HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_appd_initial_data' from ecp_subscription.t_appd_initial_data HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_appdcu_renew_batch_records' from ecp_subscription.t_appdcu_renew_batch_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_appdcu_renew_records' from ecp_subscription.t_appdcu_renew_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_consumer_incontrol' from ecp_subscription.t_consumer_incontrol HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_dms_oss_file_records' from ecp_subscription.t_dms_oss_file_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_dms_oss_original_data' from ecp_subscription.t_dms_oss_original_data HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_dms_oss_original_data_manual' from ecp_subscription.t_dms_oss_original_data_manual HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_dms_oss_original_data_records' from ecp_subscription.t_dms_oss_original_data_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_expire_search_batch_record' from ecp_subscription.t_expire_search_batch_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_expire_search_detail_record' from ecp_subscription.t_expire_search_detail_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_iccid_modify_batch_records' from ecp_subscription.t_iccid_modify_batch_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_iccid_modify_records' from ecp_subscription.t_iccid_modify_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_import_manual_modify_log' from ecp_subscription.t_import_manual_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_incontrol_customer' from ecp_subscription.t_incontrol_customer HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_incontrol_vehicle' from ecp_subscription.t_incontrol_vehicle HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_inter_busi_error_log' from ecp_subscription.t_inter_busi_error_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_manual_modify_log' from ecp_subscription.t_manual_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_pivi_package' from ecp_subscription.t_pivi_package HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_pivi_package_log' from ecp_subscription.t_pivi_package_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_remote_original_data' from ecp_subscription.t_remote_original_data HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_remote_package' from ecp_subscription.t_remote_package HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_remote_renew_batch_records' from ecp_subscription.t_remote_renew_batch_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_remote_renew_detail_records' from ecp_subscription.t_remote_renew_detail_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_series_brand_mapping_data' from ecp_subscription.t_series_brand_mapping_data HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_subscription_service' from ecp_subscription.t_subscription_service HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_subscription_service_log' from ecp_subscription.t_subscription_service_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_unicom_rnr' from ecp_subscription.t_unicom_rnr HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_unicom_rnr_batch_records' from ecp_subscription.t_unicom_rnr_batch_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_unicom_rnr_query_records' from ecp_subscription.t_unicom_rnr_query_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_unicom_todo_order' from ecp_subscription.t_unicom_todo_order HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vcs_fufilment_statistic' from ecp_subscription.t_vcs_fufilment_statistic HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vcs_order_fufilment' from ecp_subscription.t_vcs_order_fufilment HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vcs_order_fufilment_call' from ecp_subscription.t_vcs_order_fufilment_call HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vcs_order_fufilment_records' from ecp_subscription.t_vcs_order_fufilment_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vcs_order_rollback' from ecp_subscription.t_vcs_order_rollback HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vcs_order_rollback_records' from ecp_subscription.t_vcs_order_rollback_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vehicle_black_list' from ecp_subscription.t_vehicle_black_list HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vehicle_config_group' from ecp_subscription.t_vehicle_config_group HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vehicle_dms' from ecp_subscription.t_vehicle_dms HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vehicle_esim' from ecp_subscription.t_vehicle_esim HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vehicle_mobile' from ecp_subscription.t_vehicle_mobile HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vehicle_model_master_data' from ecp_subscription.t_vehicle_model_master_data HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vin_appd_temp' from ecp_subscription.t_vin_appd_temp HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vin_expiry_monthly_detail' from ecp_subscription.t_vin_expiry_monthly_detail HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vin_expiry_monthly_records' from ecp_subscription.t_vin_expiry_monthly_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vin_expiry_report_data' from ecp_subscription.t_vin_expiry_report_data HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vin_initial_query' from ecp_subscription.t_vin_initial_query HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vin_initial_temp' from ecp_subscription.t_vin_initial_temp HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vin_service_query_batch_records' from ecp_subscription.t_vin_service_query_batch_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_subscription','t_vin_service_query_records' from ecp_subscription.t_vin_service_query_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_inventory','t_inventory' from ecp_inventory.t_inventory HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_inventory','t_inventory_change_record' from ecp_inventory.t_inventory_change_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_inventory','t_inventory_sync_record' from ecp_inventory.t_inventory_sync_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_logistics','t_freight_rule_region' from ecp_logistics.t_freight_rule_region HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_logistics','t_freight_template' from ecp_logistics.t_freight_template HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_logistics','t_freight_template_rule' from ecp_logistics.t_freight_template_rule HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_logistics','t_order_delivery' from ecp_logistics.t_order_delivery HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_auto_task_condition' from ecp_notification.t_auto_task_condition HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_auto_task_trigger_map' from ecp_notification.t_auto_task_trigger_map HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_config_modify_log' from ecp_notification.t_config_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_crc_messages' from ecp_notification.t_crc_messages HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_message_template' from ecp_notification.t_message_template HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_minicode_config' from ecp_notification.t_minicode_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_minicode_generate_records' from ecp_notification.t_minicode_generate_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_mp_notice_expire_subscription' from ecp_notification.t_mp_notice_expire_subscription HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_mp_notice_order_subscription' from ecp_notification.t_mp_notice_order_subscription HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_mp_template_notice_record' from ecp_notification.t_mp_template_notice_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_msg_black_list' from ecp_notification.t_msg_black_list HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_notification_auto_task' from ecp_notification.t_notification_auto_task HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_notification_config' from ecp_notification.t_notification_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_notification_config_modify_log' from ecp_notification.t_notification_config_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_notification_history' from ecp_notification.t_notification_history HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_notification_history_detail' from ecp_notification.t_notification_history_detail HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_notification_task' from ecp_notification.t_notification_task HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_notification_template_modify_log' from ecp_notification.t_notification_template_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_order_notification_detail' from ecp_notification.t_order_notification_detail HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_short_link_click_total' from ecp_notification.t_short_link_click_total HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_short_link_click_user' from ecp_notification.t_short_link_click_user HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_short_link_create_record' from ecp_notification.t_short_link_create_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_sms_report_resp' from ecp_notification.t_sms_report_resp HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_task_modify_log' from ecp_notification.t_task_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_notification','t_template_modify_log' from ecp_notification.t_template_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_coupon_mock' from ecp_order.t_coupon_mock HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_customer_service_order' from ecp_order.t_customer_service_order HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_feedback_config' from ecp_order.t_feedback_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_feedback_dimensions' from ecp_order.t_feedback_dimensions HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_feedback_modify_log' from ecp_order.t_feedback_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_feedback_records' from ecp_order.t_feedback_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_feedback_snapshot' from ecp_order.t_feedback_snapshot HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_coupon_detail' from ecp_order.t_order_coupon_detail HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_discount_detail' from ecp_order.t_order_discount_detail HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_gift_address' from ecp_order.t_order_gift_address HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_independent' from ecp_order.t_order_independent HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_independent_item' from ecp_order.t_order_independent_item HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_independent_order_item' from ecp_order.t_order_independent_order_item HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_info' from ecp_order.t_order_info HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_item' from ecp_order.t_order_item HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_item_logistics' from ecp_order.t_order_item_logistics HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_modify_detail_log' from ecp_order.t_order_modify_detail_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_modify_log' from ecp_order.t_order_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_payment_records' from ecp_order.t_order_payment_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_refund' from ecp_order.t_order_refund HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_refund_attachment' from ecp_order.t_order_refund_attachment HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_refund_item' from ecp_order.t_order_refund_item HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_refund_payment_records' from ecp_order.t_order_refund_payment_records HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_statistic' from ecp_order.t_order_statistic HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_status_log' from ecp_order.t_order_status_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_status_mapping' from ecp_order.t_order_status_mapping HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_order_terms' from ecp_order.t_order_terms HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_shopping_car' from ecp_order.t_shopping_car HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_shopping_car_item' from ecp_order.t_shopping_car_item HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_vcs_order_info' from ecp_order.t_vcs_order_info HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_order','t_vcs_order_statistic' from ecp_order.t_vcs_order_statistic HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_app_info' from ecp_payment.t_app_info HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_invoice_apply_record' from ecp_payment.t_invoice_apply_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_invoice_apply_record_item' from ecp_payment.t_invoice_apply_record_item HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_invoice_change_log' from ecp_payment.t_invoice_change_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_invoice_changelog_record' from ecp_payment.t_invoice_changelog_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_invoice_especial_record' from ecp_payment.t_invoice_especial_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_invoice_paper_record' from ecp_payment.t_invoice_paper_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_invoice_request_order' from ecp_payment.t_invoice_request_order HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_invoice_revoke_record' from ecp_payment.t_invoice_revoke_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_invoice_revoke_request_order' from ecp_payment.t_invoice_revoke_request_order HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_invoice_title_info' from ecp_payment.t_invoice_title_info HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_pay_nofity_log' from ecp_payment.t_pay_nofity_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_pay_nofity_task' from ecp_payment.t_pay_nofity_task HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_payment_broker' from ecp_payment.t_payment_broker HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_payment_order' from ecp_payment.t_payment_order HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_payment_order_bak' from ecp_payment.t_payment_order_bak HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_payment_request_order' from ecp_payment.t_payment_request_order HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_refund_payment_order' from ecp_payment.t_refund_payment_order HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_payment','t_refund_request_order' from ecp_payment.t_refund_request_order HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_content_modify_log' from ecp_product.t_content_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_content_modify_log_bak' from ecp_product.t_content_modify_log_bak HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_coupon_modify_log' from ecp_product.t_coupon_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_kingdee_sku_tax' from ecp_product.t_kingdee_sku_tax HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_kingdee_sku_tax_temp' from ecp_product.t_kingdee_sku_tax_temp HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_merchant_account' from ecp_product.t_merchant_account HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_pivi_model_mapping' from ecp_product.t_pivi_model_mapping HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_attribute' from ecp_product.t_product_attribute HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_attribute_name' from ecp_product.t_product_attribute_name HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_attribute_value' from ecp_product.t_product_attribute_value HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_brand' from ecp_product.t_product_brand HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_bundles_sku' from ecp_product.t_product_bundles_sku HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_car_series' from ecp_product.t_product_car_series HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_category' from ecp_product.t_product_category HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_content' from ecp_product.t_product_content HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_coupon_usage_item' from ecp_product.t_product_coupon_usage_item HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_coupon_usage_rule' from ecp_product.t_product_coupon_usage_rule HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_detail' from ecp_product.t_product_detail HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_faq' from ecp_product.t_product_faq HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_faq_type' from ecp_product.t_product_faq_type HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_modify_log' from ecp_product.t_product_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_modify_log_bak' from ecp_product.t_product_modify_log_bak HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_policy' from ecp_product.t_product_policy HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_relation_info' from ecp_product.t_product_relation_info HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_schedule_on_shelf_detail' from ecp_product.t_product_schedule_on_shelf_detail HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_series' from ecp_product.t_product_series HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_sku' from ecp_product.t_product_sku HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_snapshot' from ecp_product.t_product_snapshot HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_spu_category' from ecp_product.t_product_spu_category HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_spu_info' from ecp_product.t_product_spu_info HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_tax' from ecp_product.t_product_tax HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_product_unit' from ecp_product.t_product_unit HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_remote_package' from ecp_product.t_remote_package HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_product','t_vehicle_model_master_data' from ecp_product.t_vehicle_model_master_data HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_report','t_report_download_task' from ecp_report.t_report_download_task HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','infra_file_content' from ecp_system.infra_file_content HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','member_level' from ecp_system.member_level HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','member_level_record' from ecp_system.member_level_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','member_point_config' from ecp_system.member_point_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','member_point_record' from ecp_system.member_point_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','member_sign_in_config' from ecp_system.member_sign_in_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','member_sign_in_record' from ecp_system.member_sign_in_record HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','member_tag' from ecp_system.member_tag HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','member_user' from ecp_system.member_user HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','pay_app' from ecp_system.pay_app HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','pay_channel' from ecp_system.pay_channel HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','pay_demo_order' from ecp_system.pay_demo_order HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','pay_notify_log' from ecp_system.pay_notify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','pay_notify_task' from ecp_system.pay_notify_task HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','pay_order' from ecp_system.pay_order HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','pay_order_extension' from ecp_system.pay_order_extension HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','pay_refund' from ecp_system.pay_refund HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','report_go_view_project' from ecp_system.report_go_view_project HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_dept' from ecp_system.system_dept HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_dict_data' from ecp_system.system_dict_data HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_dict_type' from ecp_system.system_dict_type HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_error_code' from ecp_system.system_error_code HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_login_log' from ecp_system.system_login_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_mail_account' from ecp_system.system_mail_account HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_mail_log' from ecp_system.system_mail_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_mail_template' from ecp_system.system_mail_template HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_menu' from ecp_system.system_menu HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_notice' from ecp_system.system_notice HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_notify_message' from ecp_system.system_notify_message HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_notify_template' from ecp_system.system_notify_template HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_oauth2_access_token' from ecp_system.system_oauth2_access_token HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_oauth2_approve' from ecp_system.system_oauth2_approve HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_oauth2_code' from ecp_system.system_oauth2_code HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_oauth2_refresh_token' from ecp_system.system_oauth2_refresh_token HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_post' from ecp_system.system_post HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_role' from ecp_system.system_role HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_role_menu' from ecp_system.system_role_menu HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_sms_code' from ecp_system.system_sms_code HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_sms_log' from ecp_system.system_sms_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_sms_template' from ecp_system.system_sms_template HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_social_user' from ecp_system.system_social_user HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_social_user_bind' from ecp_system.system_social_user_bind HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_tenant' from ecp_system.system_tenant HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_tenant_package' from ecp_system.system_tenant_package HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_user_post' from ecp_system.system_user_post HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_user_role' from ecp_system.system_user_role HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','system_users' from ecp_system.system_users HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_eshop_entrance_config' from ecp_system.t_eshop_entrance_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_eshop_entrance_config_modify_log' from ecp_system.t_eshop_entrance_config_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_eshop_entrance_poster_config' from ecp_system.t_eshop_entrance_poster_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_eshop_entrance_poster_modify_log' from ecp_system.t_eshop_entrance_poster_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_gift_address_config' from ecp_system.t_gift_address_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_gift_address_config_modify_log' from ecp_system.t_gift_address_config_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_home_config_modify_log' from ecp_system.t_home_config_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_home_config_modify_log_new' from ecp_system.t_home_config_modify_log_new HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_infra_api_access_log' from ecp_system.t_infra_api_access_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_infra_api_error_log' from ecp_system.t_infra_api_error_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_infra_file' from ecp_system.t_infra_file HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_infra_file_config' from ecp_system.t_infra_file_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_master_data_area' from ecp_system.t_master_data_area HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_master_data_city' from ecp_system.t_master_data_city HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_master_data_province' from ecp_system.t_master_data_province HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_mini_home_config' from ecp_system.t_mini_home_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_mini_home_config_draft' from ecp_system.t_mini_home_config_draft HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_order_gift_address' from ecp_system.t_order_gift_address HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_business' from ecp_system.t_system_business HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_dict_data' from ecp_system.t_system_dict_data HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_dict_type' from ecp_system.t_system_dict_type HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_login_log' from ecp_system.t_system_login_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_maintenance_config' from ecp_system.t_system_maintenance_config HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_maintenance_modify_log' from ecp_system.t_system_maintenance_modify_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_menu' from ecp_system.t_system_menu HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_menu_back' from ecp_system.t_system_menu_back HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_oauth2_access_token' from ecp_system.t_system_oauth2_access_token HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_oauth2_client' from ecp_system.t_system_oauth2_client HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_oauth2_refresh_token' from ecp_system.t_system_oauth2_refresh_token HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_operate_log' from ecp_system.t_system_operate_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_role' from ecp_system.t_system_role HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_role_menu' from ecp_system.t_system_role_menu HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_tenant' from ecp_system.t_system_tenant HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_tenant_package' from ecp_system.t_system_tenant_package HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_user' from ecp_system.t_system_user HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_user_pwd_log' from ecp_system.t_system_user_pwd_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_system','t_system_user_role' from ecp_system.t_system_user_role HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_fulfillment','t_guanyi_sync_log' from ecp_fulfillment.t_guanyi_sync_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_virtual','t_virtual_coupon_info' from ecp_virtual.t_virtual_coupon_info HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_virtual','t_virtual_coupon_sync_info' from ecp_virtual.t_virtual_coupon_sync_info HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_xxl_job','xxl_job_group' from ecp_xxl_job.xxl_job_group HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_xxl_job','xxl_job_info' from ecp_xxl_job.xxl_job_info HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_xxl_job','xxl_job_lock' from ecp_xxl_job.xxl_job_lock HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_xxl_job','xxl_job_log' from ecp_xxl_job.xxl_job_log HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_xxl_job','xxl_job_log_report' from ecp_xxl_job.xxl_job_log_report HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_xxl_job','xxl_job_logglue' from ecp_xxl_job.xxl_job_logglue HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_xxl_job','xxl_job_registry' from ecp_xxl_job.xxl_job_registry HAVING t_count > 100000 union all
select IF(COUNT(1) > 100000, COUNT(1), 0) as t_count, 'ecp_xxl_job','xxl_job_user' from ecp_xxl_job.xxl_job_user HAVING t_count > 100000 ;

-- 1. 创建基础环境和替换规则
source mysql_8_4_data_cleaning_complete.sql;

-- 2. 创建清洗函数
source mysql_8_4_cleaning_functions.sql;

-- 3. 创建批量处理存储过程
source mysql_8_4_batch_procedures.sql;


CALL CleanSchema('ecp_consumer');
CALL CleanSchema('ecp_fulfillment');
CALL CleanSchema('ecp_integration');
CALL CleanSchema('ecp_inventory');
CALL CleanSchema('ecp_logistics');
CALL CleanSchema('ecp_message');
CALL CleanSchema('ecp_payment');
CALL CleanSchema('ecp_product');
CALL CleanSchema('ecp_report');
CALL CleanSchema('ecp_virtual');
CALL CleanSchema('ecp_xxl_job');
CALL CleanSchema('ecp_notification');
CALL CleanSchema('ecp_subscription');
CALL CleanSchema('ecp_order');
CALL CleanSchema('ecp_system');


# 以下为AI生成
# MySQL 8.4.5 完整数据清洗解决方案

这是一个专为MySQL 8.4.5设计的完整数据清洗解决方案，支持灵活的替换规则配置和管理，完全基于SQL实现。

## 🚀 快速开始

### 执行顺序
```sql
-- 1. 创建基础环境和替换规则
source mysql_8_4_data_cleaning_complete.sql;

-- 2. 创建清洗函数
source mysql_8_4_cleaning_functions.sql;

-- 3. 创建批量处理存储过程
source mysql_8_4_batch_procedures.sql;

-- 4. 执行数据清洗
source execute_complete_cleaning.sql;

-- 5. 验证和确认结果
source verify_and_confirm_complete.sql;
```

### 一键执行清洗
```sql
-- 执行全部清洗
CALL BatchCleanAllTables();

-- 或按schema分别执行
CALL CleanSchema('ecp_consumer');
CALL CleanSchema('ecp_notification');
-- ... 其他schema
```

## 📁 文件说明

### 1. `mysql_8_4_data_cleaning_complete.sql`
**基础环境脚本**
- 创建清洗记录表 `data_cleaning_records`
- 创建替换规则表 `replacement_rules`
- 预配置完整的替换规则（支持您的所有需求）
- 创建清洗进度跟踪表

### 2. `mysql_8_4_cleaning_functions.sql`
**核心清洗函数**
- `apply_all_replacements()` - 动态应用所有启用的替换规则
- `needs_replacement()` - 检查文本是否需要替换
- `get_applied_rules()` - 获取应用的规则列表
- `CleanSingleColumn()` - 清洗单个列的存储过程

### 3. `mysql_8_4_batch_procedures.sql`
**批量处理存储过程**
- `BatchCleanAllTables()` - 批量清洗所有表
- `CleanSchema()` - 清洗指定schema
- `CleanTable()` - 清洗指定表
- `GetCleaningStatistics()` - 获取清洗统计信息

### 4. `execute_complete_cleaning.sql`
**执行清洗脚本**
- 提供多种执行方式
- 包含测试和监控功能
- 显示详细的进度信息

### 5. `verify_and_confirm_complete.sql`
**验证和确认脚本**
- 清洗结果统计和验证
- 自动批量确认功能
- 生成最终报告

### 6. `rule_management.sql`
**规则管理脚本**
- 规则的增删改查
- 规则测试功能
- 规则使用统计
- 规则导入导出

## 🎯 替换规则详情

### 品牌名称替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| 捷豹 | 品牌A | 中文品牌名 |
| 路虎 | 品牌B | 中文品牌名 |
| 卫士 | 品牌B子品牌1 | 子品牌 |
| 揽胜 | 品牌B子品牌2 | 子品牌 |
| 发现 | 品牌B子品牌3 | 子品牌 |
| 发现运动 | 品牌B子品牌4 | 子品牌 |

### JLR相关替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| JLR | ATB | 大写 |
| jlr | atb | 小写 |
| Jlr | Atb | 首字母大写 |

### Jaguar相关替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| Jaguar | TradeA | 标准格式 |
| jaguar | tradea | 小写 |
| JAGUAR | TRADEA | 大写 |

### Landrover相关替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| Landrover | TradeB | 标准格式 |
| landrover | tradeb | 小写 |
| LANDROVER | TRADEB | 大写 |
| LAND_ROVER | TRADE_B | 下划线格式 |
| Land rover | trade b | 空格格式 |
| LandRover | TradeB | 驼峰格式 |

### 子品牌英文替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| Defender | TradeBSub1 | 卫士英文 |
| Range Rover | TradeBSub2 | 揽胜英文 |
| Discovery | TradeBSub3 | 发现英文 |
| Discovery Sport | TradeBSub4 | 发现运动英文 |

### 系统缩写替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| tsdp | rsds | 小写 |
| TSDP | RSDS | 大写 |
| appd | ssop | 小写 |
| APPD | SSOP | 大写 |

### 服务商替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| 联通 | 某网络运营商 | 中文 |
| China Unicom | SSDP | 英文 |
| 高德 | 某地图服务商 | 中文 |
| AMAP | SMAP | 英文 |

### CRC相关替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| CRC | CTP | 大写 |
| crc | ctp | 小写 |
| Crc | Ctp | 首字母大写 |

### ForgeRock相关替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| FORGEROCK | AUTHSYS | 全大写 |
| ForgeRock | AuthSys | 标准格式 |
| forgeRock | authSys | 驼峰格式 |
| forgerock | authsys | 全小写 |

### 支付相关替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| 3rd party payment broker（eg.CUSC）cusc | paytp | 支付描述 |
| CUSC | PAYTP | 支付系统 |

### 其他系统替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| CDT | PMG | 大写 |
| cdt | pmg | 小写 |
| sota | UIDM | 系统名 |
| CCCM | CARSYSA | 车载系统A |
| PIVI | CARSYSB | 车载系统B |
| pivi | carsysb | 小写 |
| Pivi | CarsysB | 首字母大写 |

### 用户类型替换
| 原始内容 | 替换内容 | 说明 |
|---------|---------|------|
| JLR 用户 | Internal 用户 | 中文用户 |
| JLR User | Internal User | 英文用户 |
| JLR ECP User | Internal ECP User | ECP用户 |

## 🔧 核心功能

### 1. 动态替换规则系统
- 所有替换规则存储在 `replacement_rules` 表中
- 支持规则的启用/禁用
- 支持规则分类和排序
- 修改规则后立即生效，无需重启

### 2. 智能文本处理
```sql
-- 应用所有替换规则
SELECT apply_all_replacements('JLR捷豹路虎用户使用APPD系统');
-- 结果: ATB品牌A品牌B用户使用SSOP系统

-- 检查是否需要替换
SELECT needs_replacement('包含JLR的文本');
-- 结果: 1 (true)

-- 获取应用的规则
SELECT get_applied_rules('原文', '替换后文本');
-- 结果: 应用的规则名称列表
```

### 3. 完整的记录跟踪
- 每次替换都记录在 `data_cleaning_records` 表中
- 包含原始内容、替换内容、业务主键
- 支持人工确认机制（待确认/已确认/已拒绝）
- 记录应用的替换规则

### 4. 灵活的执行方式
```sql
-- 全部清洗
CALL BatchCleanAllTables();

-- 按schema清洗
CALL CleanSchema('ecp_consumer');

-- 按表清洗
CALL CleanTable('ecp_consumer', 't_consumer_info');

-- 单列清洗
CALL CleanSingleColumn('ecp_consumer', 't_consumer_info', 'nick_name');
```

## 📊 涉及的Schema

- `ecp_consumer` - 消费者相关
- `ecp_fulfillment` - 履约相关  
- `ecp_integration` - 集成相关
- `ecp_inventory` - 库存相关
- `ecp_logistics` - 物流相关
- `ecp_message` - 消息相关
- `ecp_notification` - 通知相关
- `ecp_order` - 订单相关
- `ecp_payment` - 支付相关
- `ecp_product` - 产品相关
- `ecp_report` - 报表相关
- `ecp_subscription` - 订阅相关
- `ecp_system` - 系统相关
- `ecp_virtual` - 虚拟相关
- `ecp_xxl_job` - 任务调度相关
- `jlr_ecp_test` - 测试相关

## 📈 监控和管理

### 实时监控
```sql
-- 查看清洗进度
SELECT status, COUNT(*) as count 
FROM ecp_data_cleaning.cleaning_progress 
GROUP BY status;

-- 查看清洗统计
CALL GetCleaningStatistics();
```

### 规则管理
```sql
-- 查看所有规则
SELECT * FROM ecp_data_cleaning.replacement_rules 
ORDER BY rule_category, sort_order;

-- 添加新规则
INSERT INTO replacement_rules (rule_name, search_pattern, replace_value, rule_category, sort_order) 
VALUES ('new_rule', '原始文本', '替换文本', 'CUSTOM', 300);

-- 禁用规则
UPDATE replacement_rules SET is_active = 0 WHERE rule_name = 'rule_name';

-- 测试规则
CALL TestReplacementRule('测试文本', 'rule_name');
CALL TestAllRules('测试文本');
```

### 结果验证
```sql
-- 查看清洗记录
SELECT * FROM ecp_data_cleaning.data_cleaning_records 
ORDER BY created_time DESC LIMIT 100;

-- 人工确认
UPDATE ecp_data_cleaning.data_cleaning_records 
SET confirm_status = 1 
WHERE confirm_status = 0;
```

## ⚠️ 重要注意事项

### 1. 数据备份
```sql
-- 执行前务必备份
mysqldump -u root -p --all-databases > backup_before_cleaning.sql
```

### 2. 权限要求
```sql
-- 确保用户有足够权限
GRANT ALL PRIVILEGES ON *.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 会话配置
```sql
-- 调整会话参数
SET SESSION innodb_lock_wait_timeout = 600;
SET SESSION max_execution_time = 0;
```

## 🛠️ 故障排除

### 常见问题

1. **规则不生效**
   - 检查规则是否启用：`SELECT * FROM replacement_rules WHERE is_active = 0;`
   - 重新加载规则：`CALL ReloadReplacementRules();`

2. **清洗失败**
   - 查看失败任务：`SELECT * FROM cleaning_progress WHERE status = 'FAILED';`
   - 重置失败任务：`UPDATE cleaning_progress SET status = 'PENDING' WHERE status = 'FAILED';`

3. **性能问题**
   - 调整批处理大小
   - 分schema执行
   - 检查索引

## 📋 执行检查清单

- [ ] 备份数据库
- [ ] 执行基础环境脚本
- [ ] 创建清洗函数和存储过程
- [ ] 测试替换函数
- [ ] 执行数据清洗
- [ ] 监控清洗进度
- [ ] 验证清洗结果
- [ ] 人工确认记录
- [ ] 备份清洗记录
- [ ] 清理临时数据

## 🎯 特色功能

### 1. 规则热更新
- 修改 `replacement_rules` 表即可更新规则
- 无需重启或重新部署
- 支持规则的启用/禁用

### 2. 完整的审计跟踪
- 每次替换都有详细记录
- 支持回溯和审计
- 人工确认机制

### 3. 灵活的执行策略
- 支持全量、分批、单表、单列执行
- 可根据业务需要调整执行策略
- 支持断点续传

### 4. 智能错误处理
- 自动错误恢复
- 详细的错误日志
- 支持重试机制

这个完整的解决方案提供了企业级的数据清洗能力，完全满足您的需求，并且具有很好的可扩展性和维护性。
