-- 完整的数据清洗验证和确认脚本

USE ecp_data_cleaning;

-- 1. 清洗结果统计报告
SELECT '=== 完整数据清洗结果统计报告 ===' as report_title;
SELECT NOW() as report_time;

-- 总体统计
SELECT 
    '总体统计' as section,
    COUNT(*) as total_replacements,
    COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) as affected_tables,
    COUNT(DISTINCT CONCAT(schema_name, '.', table_name, '.', column_name)) as affected_columns,
    COUNT(DISTINCT biz_pk) as affected_records,
    MIN(created_time) as first_replacement,
    MAX(created_time) as last_replacement
FROM data_cleaning_records;

-- 按Schema统计
SELECT 
    '按Schema统计' as section,
    schema_name,
    COUNT(*) as replacements,
    COUNT(DISTINCT table_name) as tables,
    COUNT(DISTINCT CONCAT(table_name, '.', column_name)) as columns,
    COUNT(DISTINCT biz_pk) as records
FROM data_cleaning_records
GROUP BY schema_name
ORDER BY replacements DESC;

-- 按表统计（Top 30）
SELECT 
    '按表统计(Top 30)' as section,
    CONCAT(schema_name, '.', table_name) as full_table_name,
    COUNT(DISTINCT column_name) as cleaned_columns,
    COUNT(*) as total_replacements,
    COUNT(DISTINCT biz_pk) as affected_records
FROM data_cleaning_records
GROUP BY schema_name, table_name
ORDER BY total_replacements DESC
LIMIT 30;

-- 按替换规则统计
SELECT 
    '按替换规则统计' as section,
    SUBSTRING_INDEX(replace_rule, ',', 1) as main_rule,
    COUNT(*) as usage_count,
    COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) as affected_tables
FROM data_cleaning_records
WHERE replace_rule IS NOT NULL AND replace_rule != ''
GROUP BY SUBSTRING_INDEX(replace_rule, ',', 1)
ORDER BY usage_count DESC
LIMIT 20;

-- 3. 人工确认状态
SELECT '=== 人工确认状态 ===' as confirmation_title;

SELECT 
    CASE confirm_status
        WHEN 0 THEN '待确认'
        WHEN 1 THEN '已确认'
        WHEN 2 THEN '已拒绝'
        ELSE '未知状态'
    END as status_name,
    COUNT(*) as record_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM data_cleaning_records), 2) as percentage
FROM data_cleaning_records
GROUP BY confirm_status
ORDER BY confirm_status;

-- 5. 清洗记录示例
SELECT '=== 清洗记录示例 ===' as sample_title;

SELECT 
    CONCAT(schema_name, '.', table_name, '.', column_name) as location,
    biz_pk,
    LEFT(src_content, 80) as original_content,
    LEFT(replace_content, 80) as new_content,
    replace_rule,
    CASE confirm_status
        WHEN 0 THEN '待确认'
        WHEN 1 THEN '已确认'
        WHEN 2 THEN '已拒绝'
    END as status,
    created_time
FROM data_cleaning_records
ORDER BY created_time DESC
LIMIT 50;

-- 6. 按规则分类的清洗效果
SELECT '=== 按规则分类的清洗效果 ===' as category_title;

SELECT 
    '品牌名称替换' as category,
    COUNT(*) as replacement_count,
    COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) as affected_tables
FROM data_cleaning_records
WHERE replace_rule LIKE '%brand_%' OR 
      replace_rule LIKE '%jaguar%' OR 
      replace_rule LIKE '%landrover%' OR
      replace_rule LIKE '%defender%' OR
      replace_rule LIKE '%range_rover%' OR
      replace_rule LIKE '%discovery%'

UNION ALL

SELECT 
    '系统缩写替换' as category,
    COUNT(*) as replacement_count,
    COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) as affected_tables
FROM data_cleaning_records
WHERE replace_rule LIKE '%jlr_%' OR 
      replace_rule LIKE '%tsdp%' OR 
      replace_rule LIKE '%appd%' OR
      replace_rule LIKE '%crc_%' OR
      replace_rule LIKE '%cdt_%' OR
      replace_rule LIKE '%sota%' OR
      replace_rule LIKE '%cccm%' OR
      replace_rule LIKE '%pivi%'

UNION ALL

SELECT 
    '服务商替换' as category,
    COUNT(*) as replacement_count,
    COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) as affected_tables
FROM data_cleaning_records
WHERE replace_rule LIKE '%unicom%' OR 
      replace_rule LIKE '%amap%' OR 
      replace_rule LIKE '%forgerock%' OR
      replace_rule LIKE '%cusc%'

UNION ALL

SELECT 
    '用户类型替换' as category,
    COUNT(*) as replacement_count,
    COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) as affected_tables
FROM data_cleaning_records
WHERE replace_rule LIKE '%user%';

-- 8. 确认后统计
SELECT '=== 确认后统计 ===' as post_confirm_title;

SELECT 
    CASE confirm_status
        WHEN 0 THEN '待确认'
        WHEN 1 THEN '已确认'
        WHEN 2 THEN '已拒绝'
    END as status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM data_cleaning_records), 2) as percentage
FROM data_cleaning_records
GROUP BY confirm_status
ORDER BY confirm_status;

-- 9. 生成最终报告
SELECT '=== 数据清洗最终报告 ===' as final_report_title;

-- 清洗完成时间和基本统计
SELECT 
    '清洗完成时间' as metric,
    NOW() as value
UNION ALL
SELECT 
    '总处理记录数' as metric,
    CAST(COUNT(*) AS CHAR) as value
FROM data_cleaning_records
UNION ALL
SELECT 
    '影响的表数量' as metric,
    CAST(COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) AS CHAR) as value
FROM data_cleaning_records
UNION ALL
SELECT 
    '影响的列数量' as metric,
    CAST(COUNT(DISTINCT CONCAT(schema_name, '.', table_name, '.', column_name)) AS CHAR) as value
FROM data_cleaning_records
UNION ALL
SELECT 
    '已确认记录数' as metric,
    CAST(COUNT(*) AS CHAR) as value
FROM data_cleaning_records
WHERE confirm_status = 1
UNION ALL
SELECT 
    '待确认记录数' as metric,
    CAST(COUNT(*) AS CHAR) as value
FROM data_cleaning_records
WHERE confirm_status = 0
UNION ALL
SELECT 
    '已拒绝记录数' as metric,
    CAST(COUNT(*) AS CHAR) as value
FROM data_cleaning_records
WHERE confirm_status = 2;

-- 10. 创建清洗记录备份
SELECT '=== 创建清洗记录备份 ===' as backup_title;

-- 创建清洗记录备份表
CREATE TABLE IF NOT EXISTS data_cleaning_records_backup AS 
SELECT * FROM data_cleaning_records;

SELECT CONCAT('已创建备份表 data_cleaning_records_backup，包含 ', 
              (SELECT COUNT(*) FROM data_cleaning_records_backup), ' 条记录') as backup_info;
