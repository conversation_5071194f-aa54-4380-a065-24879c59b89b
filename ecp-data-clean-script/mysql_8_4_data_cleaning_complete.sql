-- MySQL 8.4.5 完整数据清洗解决方案
-- 支持灵活的替换规则配置和管理

-- 创建清洗数据库
CREATE DATABASE IF NOT EXISTS ecp_data_cleaning;
USE ecp_data_cleaning;

-- 创建清洗记录表
CREATE TABLE IF NOT EXISTS data_cleaning_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    schema_name VARCHAR(100) NOT NULL COMMENT 'Schema名称',
    table_name VARCHAR(100) NOT NULL COMMENT '表名',
    column_name VARCHAR(100) NOT NULL COMMENT '列名',
    biz_pk VARCHAR(500) NOT NULL COMMENT '业务数据主键',
    src_content TEXT COMMENT '原始内容',
    replace_content TEXT COMMENT '替换后的内容',
    confirm_status TINYINT DEFAULT 0 COMMENT '人工确认状态: 0-待确认, 1-已确认, 2-拒绝',
    replace_rule VARCHAR(500) COMMENT '应用的替换规则',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    operator VARCHAR(100) DEFAULT 'SYSTEM' COMMENT '操作人',
    remark TEXT COMMENT '备注',
    INDEX idx_schema_table (schema_name, table_name),
    INDEX idx_confirm_status (confirm_status),
    INDEX idx_created_time (created_time)
) COMMENT '数据清洗记录表' ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建替换规则表
CREATE TABLE IF NOT EXISTS replacement_rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    search_pattern VARCHAR(500) NOT NULL COMMENT '搜索模式',
    replace_value VARCHAR(500) NOT NULL COMMENT '替换值',
    rule_category VARCHAR(50) DEFAULT 'GENERAL' COMMENT '规则分类',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    case_sensitive TINYINT DEFAULT 1 COMMENT '是否区分大小写',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_rule_name (rule_name),
    INDEX idx_category_order (rule_category, sort_order),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '替换规则配置表';

-- 清空并插入最新的替换规则
TRUNCATE TABLE replacement_rules;
INSERT INTO replacement_rules (rule_name, search_pattern, replace_value, rule_category, sort_order, case_sensitive) VALUES
-- 品牌名称替换（中文）
('brand_jaguar_cn', '捷豹', '品牌A', 'BRAND', 1, 1),
('brand_landrover_cn', '路虎', '品牌B', 'BRAND', 2, 1),
('brand_defender_cn', '卫士', '品牌B子品牌1', 'BRAND', 3, 1),
('brand_range_rover_cn', '揽胜', '品牌B子品牌2', 'BRAND', 4, 1),
('brand_discovery_cn', '发现', '品牌B子品牌3', 'BRAND', 5, 1),
('brand_discovery_sport_cn', '发现运动', '品牌B子品牌4', 'BRAND', 6, 1),

-- JLR相关替换
('jlr_upper', 'JLR', 'ATB', 'SYSTEM', 10, 1),
('jlr_lower', 'jlr', 'atb', 'SYSTEM', 11, 1),
('jlr_title', 'Jlr', 'Atb', 'SYSTEM', 12, 1),

-- Jaguar相关替换
('jaguar_title', 'Jaguar', 'TradeA', 'BRAND', 20, 1),
('jaguar_lower', 'jaguar', 'tradea', 'BRAND', 21, 1),
('jaguar_upper', 'JAGUAR', 'TRADEA', 'BRAND', 22, 1),

-- Landrover相关替换
('landrover_title', 'Landrover', 'TradeB', 'BRAND', 30, 1),
('landrover_lower', 'landrover', 'tradeb', 'BRAND', 31, 1),
('landrover_upper', 'LANDROVER', 'TRADEB', 'BRAND', 32, 1),
('land_rover_underscore', 'LAND_ROVER', 'TRADE_B', 'BRAND', 33, 1),
('land_rover_space', 'Land rover', 'trade b', 'BRAND', 34, 1),
('landrover_camel', 'LandRover', 'TradeB', 'BRAND', 35, 1),

-- 子品牌英文替换
('defender_en', 'Defender', 'TradeBSub1', 'BRAND', 40, 1),
('range_rover_en', 'Range Rover', 'TradeBSub2', 'BRAND', 41, 1),
('discovery_en', 'Discovery', 'TradeBSub3', 'BRAND', 42, 1),
('discovery_sport_en', 'Discovery Sport', 'TradeBSub4', 'BRAND', 43, 1),

-- 系统缩写替换
('tsdp_lower', 'tsdp', 'rsds', 'SYSTEM', 50, 1),
('tsdp_upper', 'TSDP', 'RSDS', 'SYSTEM', 51, 1),
('appd_lower', 'appd', 'ssop', 'SYSTEM', 52, 1),
('appd_upper', 'APPD', 'SSOP', 'SYSTEM', 53, 1),

-- 服务商替换
('unicom_cn', '联通', '某网络运营商', 'VENDOR', 60, 1),
('china_unicom', 'China Unicom', 'SSDP', 'VENDOR', 61, 1),
('amap_cn', '高德', '某地图服务商', 'VENDOR', 62, 1),
('amap_upper', 'AMAP', 'SMAP', 'VENDOR', 63, 1),

-- CRC相关替换
('crc_upper', 'CRC', 'CTP', 'SYSTEM', 70, 1),
('crc_lower', 'crc', 'ctp', 'SYSTEM', 71, 1),
('crc_title', 'Crc', 'Ctp', 'SYSTEM', 72, 1),

-- ForgeRock相关替换
('forgerock_upper', 'FORGEROCK', 'AUTHSYS', 'VENDOR', 80, 1),
('forgerock_title', 'ForgeRock', 'AuthSys', 'VENDOR', 81, 1),
('forgerock_camel', 'forgeRock', 'authSys', 'VENDOR', 82, 1),
('forgerock_lower', 'forgerock', 'authsys', 'VENDOR', 83, 1),

-- 支付相关替换
('payment_broker', '3rd party payment broker（eg.CUSC）cusc', 'paytp', 'VENDOR', 90, 1),
('cusc_upper', 'CUSC', 'PAYTP', 'VENDOR', 91, 1),

-- 其他系统缩写
('cdt_upper', 'CDT', 'PMG', 'SYSTEM', 100, 1),
('cdt_lower', 'cdt', 'pmg', 'SYSTEM', 101, 1),
('sota_lower', 'sota', 'UIDM', 'SYSTEM', 102, 1),
('cccm_upper', 'CCCM', 'CARSYSA', 'SYSTEM', 103, 1),
('pivi_upper', 'PIVI', 'CARSYSB', 'SYSTEM', 104, 1),
('pivi_lower', 'pivi', 'carsysb', 'SYSTEM', 105, 1),
('pivi_title', 'Pivi', 'CarsysB', 'SYSTEM', 106, 1),

-- 用户类型替换
('jlr_user_cn', 'JLR 用户', 'Internal 用户', 'USER_TYPE', 110, 1),
('jlr_user_en', 'JLR User', 'Internal User', 'USER_TYPE', 111, 1),
('jlr_ecp_user', 'JLR ECP User', 'Internal ECP User', 'USER_TYPE', 112, 1);

-- 创建临时表存储需要处理的表和列
DROP TEMPORARY TABLE IF EXISTS temp_tables_columns;
CREATE TEMPORARY TABLE temp_tables_columns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    schema_name VARCHAR(100),
    table_name VARCHAR(100),
    column_name VARCHAR(100),
    data_type VARCHAR(50),
    processed TINYINT DEFAULT 0
);

-- 插入需要清洗的表和列信息
INSERT INTO temp_tables_columns (schema_name, table_name, column_name, data_type)
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA IN (
    'ecp_consumer', 'ecp_fulfillment', 'ecp_integration', 'ecp_inventory',
    'ecp_logistics', 'ecp_message', 'ecp_notification', 'ecp_order',
    'ecp_payment', 'ecp_product', 'ecp_report', 'ecp_subscription',
    'ecp_system', 'ecp_virtual', 'ecp_xxl_job', 'jlr_ecp_test'
)
AND DATA_TYPE IN ('varchar', 'char', 'text', 'mediumtext', 'longtext', 'json')
AND COLUMN_NAME NOT IN ('password', 'created_time', 'updated_time', 'created_by', 'updated_by', 'deleted_time')
AND TABLE_NAME NOT LIKE '%_log%'
AND TABLE_NAME NOT LIKE '%_bak%'
AND TABLE_NAME NOT LIKE '%backup%'
ORDER BY TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME;

-- 创建清洗进度表
CREATE TABLE IF NOT EXISTS cleaning_progress (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    schema_name VARCHAR(100),
    table_name VARCHAR(100),
    column_name VARCHAR(100),
    start_time DATETIME,
    end_time DATETIME,
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING',
    records_updated INT DEFAULT 0,
    error_message TEXT,
    INDEX idx_status (status),
    INDEX idx_schema_table (schema_name, table_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 清空进度表
TRUNCATE TABLE cleaning_progress;

-- 插入清洗任务
INSERT INTO cleaning_progress (schema_name, table_name, column_name, status)
SELECT schema_name, table_name, column_name, 'PENDING'
FROM temp_tables_columns;

-- 显示统计信息
SELECT 
    '=== 数据清洗计划 ===' as title,
    COUNT(*) as total_columns,
    COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) as total_tables,
    COUNT(DISTINCT schema_name) as total_schemas
FROM temp_tables_columns;

SELECT 
    '按Schema统计' as section,
    schema_name,
    COUNT(*) as column_count,
    COUNT(DISTINCT table_name) as table_count
FROM temp_tables_columns
GROUP BY schema_name
ORDER BY schema_name;

-- 显示替换规则统计
SELECT 
    '=== 替换规则统计 ===' as rule_title,
    rule_category,
    COUNT(*) as rule_count
FROM replacement_rules
WHERE is_active = 1
GROUP BY rule_category
ORDER BY rule_category;

SELECT '=== 数据清洗环境准备完成 ===' as status;
