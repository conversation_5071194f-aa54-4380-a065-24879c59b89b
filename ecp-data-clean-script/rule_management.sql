-- 替换规则管理脚本
-- 用于管理和更新替换规则

USE ecp_data_cleaning;

-- 1. 查看当前所有替换规则
SELECT '=== 当前所有替换规则 ===' as title;

SELECT 
    id,
    rule_name,
    search_pattern,
    replace_value,
    rule_category,
    sort_order,
    CASE is_active WHEN 1 THEN '启用' ELSE '禁用' END as status,
    CASE case_sensitive WHEN 1 THEN '区分大小写' ELSE '不区分大小写' END as case_mode,
    created_time,
    updated_time
FROM replacement_rules
ORDER BY rule_category, sort_order;

-- 2. 按分类查看规则
SELECT '=== 按分类查看规则 ===' as category_view;

SELECT 
    rule_category,
    COUNT(*) as total_rules,
    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_rules,
    SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_rules
FROM replacement_rules
GROUP BY rule_category
ORDER BY rule_category;

-- 3. 添加新规则的示例
SELECT '=== 添加新规则示例 ===' as add_example;

-- 示例：添加新的替换规则
/*
INSERT INTO replacement_rules (rule_name, search_pattern, replace_value, rule_category, sort_order, case_sensitive) VALUES
('new_rule_example', '原始文本', '替换文本', 'CUSTOM', 200, 1);
*/

-- 4. 更新现有规则的示例
SELECT '=== 更新现有规则示例 ===' as update_example;

-- 示例：更新替换规则
/*
UPDATE replacement_rules 
SET replace_value = '新的替换值',
    updated_time = NOW()
WHERE rule_name = 'rule_name_to_update';
*/

-- 5. 禁用/启用规则的示例
SELECT '=== 禁用/启用规则示例 ===' as toggle_example;

-- 示例：禁用特定规则
/*
UPDATE replacement_rules 
SET is_active = 0,
    updated_time = NOW()
WHERE rule_name = 'rule_name_to_disable';
*/

-- 示例：启用特定规则
/*
UPDATE replacement_rules 
SET is_active = 1,
    updated_time = NOW()
WHERE rule_name = 'rule_name_to_enable';
*/

-- 6. 批量操作示例
SELECT '=== 批量操作示例 ===' as batch_example;

-- 示例：禁用某个分类的所有规则
/*
UPDATE replacement_rules 
SET is_active = 0,
    updated_time = NOW()
WHERE rule_category = 'CATEGORY_NAME';
*/

-- 示例：启用某个分类的所有规则
/*
UPDATE replacement_rules 
SET is_active = 1,
    updated_time = NOW()
WHERE rule_category = 'CATEGORY_NAME';
*/

-- 7. 删除规则的示例
SELECT '=== 删除规则示例 ===' as delete_example;

-- 示例：删除特定规则（谨慎操作）
/*
DELETE FROM replacement_rules 
WHERE rule_name = 'rule_name_to_delete';
*/

-- 8. 规则测试功能
SELECT '=== 规则测试功能 ===' as test_function;

-- 创建测试替换效果的存储过程
DELIMITER $$

DROP PROCEDURE IF EXISTS TestReplacementRule$$
CREATE PROCEDURE TestReplacementRule(
    IN p_test_text TEXT,
    IN p_rule_name VARCHAR(100)
)
BEGIN
    DECLARE v_search_pattern VARCHAR(500);
    DECLARE v_replace_value VARCHAR(500);
    DECLARE v_result TEXT;
    
    -- 获取规则信息
    SELECT search_pattern, replace_value 
    INTO v_search_pattern, v_replace_value
    FROM replacement_rules
    WHERE rule_name = p_rule_name AND is_active = 1;
    
    IF v_search_pattern IS NOT NULL THEN
        SET v_result = REPLACE(p_test_text, v_search_pattern, v_replace_value);
        
        SELECT 
            p_rule_name as rule_name,
            v_search_pattern as search_pattern,
            v_replace_value as replace_value,
            p_test_text as original_text,
            v_result as result_text,
            CASE WHEN p_test_text != v_result THEN '已替换' ELSE '无变化' END as status;
    ELSE
        SELECT 
            p_rule_name as rule_name,
            '规则不存在或已禁用' as error_message;
    END IF;
END$$

-- 测试所有规则对指定文本的效果
DROP PROCEDURE IF EXISTS TestAllRules$$
CREATE PROCEDURE TestAllRules(IN p_test_text TEXT)
BEGIN
    SELECT 
        '测试文本' as test_type,
        p_test_text as original_text,
        apply_all_replacements(p_test_text) as final_result,
        needs_replacement(p_test_text) as needs_replacement;
        
    SELECT 
        '应用的规则' as applied_rules,
        get_applied_rules(p_test_text, apply_all_replacements(p_test_text)) as rule_list;
END$$

DELIMITER ;

-- 9. 规则验证功能
SELECT '=== 规则验证功能 ===' as validation_function;

-- 检查重复的搜索模式
SELECT 
    '重复搜索模式检查' as check_type,
    search_pattern,
    COUNT(*) as duplicate_count
FROM replacement_rules
WHERE is_active = 1
GROUP BY search_pattern
HAVING COUNT(*) > 1;

-- 检查空的搜索模式或替换值
SELECT 
    '空值检查' as check_type,
    rule_name,
    CASE 
        WHEN search_pattern = '' OR search_pattern IS NULL THEN '搜索模式为空'
        WHEN replace_value = '' OR replace_value IS NULL THEN '替换值为空'
        ELSE '正常'
    END as issue
FROM replacement_rules
WHERE (search_pattern = '' OR search_pattern IS NULL OR 
       replace_value = '' OR replace_value IS NULL)
  AND is_active = 1;

-- 10. 规则使用统计
SELECT '=== 规则使用统计 ===' as usage_stats;

-- 统计每个规则的使用次数
SELECT 
    r.rule_name,
    r.rule_category,
    r.search_pattern,
    r.replace_value,
    IFNULL(usage.usage_count, 0) as usage_count
FROM replacement_rules r
LEFT JOIN (
    SELECT 
        SUBSTRING_INDEX(replace_rule, ',', 1) as rule_name,
        COUNT(*) as usage_count
    FROM data_cleaning_records
    WHERE replace_rule IS NOT NULL AND replace_rule != ''
    GROUP BY SUBSTRING_INDEX(replace_rule, ',', 1)
) usage ON r.rule_name = usage.rule_name
WHERE r.is_active = 1
ORDER BY usage_count DESC, r.rule_category, r.sort_order;

-- 11. 规则管理操作模板
SELECT '=== 规则管理操作模板 ===' as template_title;

-- 常用操作模板
SELECT '-- 添加新规则' as operation
UNION ALL
SELECT 'INSERT INTO replacement_rules (rule_name, search_pattern, replace_value, rule_category, sort_order) VALUES (''规则名'', ''搜索内容'', ''替换内容'', ''分类'', 排序号);'
UNION ALL
SELECT ''
UNION ALL
SELECT '-- 更新规则'
UNION ALL
SELECT 'UPDATE replacement_rules SET replace_value = ''新替换值'' WHERE rule_name = ''规则名'';'
UNION ALL
SELECT ''
UNION ALL
SELECT '-- 禁用规则'
UNION ALL
SELECT 'UPDATE replacement_rules SET is_active = 0 WHERE rule_name = ''规则名'';'
UNION ALL
SELECT ''
UNION ALL
SELECT '-- 启用规则'
UNION ALL
SELECT 'UPDATE replacement_rules SET is_active = 1 WHERE rule_name = ''规则名'';'
UNION ALL
SELECT ''
UNION ALL
SELECT '-- 测试规则'
UNION ALL
SELECT 'CALL TestReplacementRule(''测试文本'', ''规则名'');'
UNION ALL
SELECT ''
UNION ALL
SELECT '-- 测试所有规则'
UNION ALL
SELECT 'CALL TestAllRules(''测试文本'');';

-- 12. 规则导出功能
SELECT '=== 规则导出 ===' as export_title;

-- 生成规则导出SQL
SELECT 
    CONCAT(
        'INSERT INTO replacement_rules (rule_name, search_pattern, replace_value, rule_category, sort_order, is_active, case_sensitive) VALUES (',
        '''', rule_name, ''', ',
        '''', search_pattern, ''', ',
        '''', replace_value, ''', ',
        '''', rule_category, ''', ',
        sort_order, ', ',
        is_active, ', ',
        case_sensitive, ');'
    ) as export_sql
FROM replacement_rules
WHERE is_active = 1
ORDER BY rule_category, sort_order;

SELECT '=== 规则管理脚本完成 ===' as completion;
