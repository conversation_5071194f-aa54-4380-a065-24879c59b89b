-- MySQL 8.4.5 批量清洗存储过程
USE ecp_data_cleaning;

DELIMITER $$

-- 批量清洗所有表的存储过程
DROP PROCEDURE IF EXISTS BatchCleanAllTables$$
CREATE PROCEDURE BatchCleanAllTables()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_schema VARCHAR(100);
    DECLARE v_table VARCHAR(100);
    DECLARE v_column VARCHAR(100);
    DECLARE v_counter INT DEFAULT 0;
    DECLARE v_total INT DEFAULT 0;
    DECLARE v_start_time DATETIME;
    DECLARE v_error_count INT DEFAULT 0;
    
    DECLARE table_cursor CURSOR FOR
        SELECT schema_name, table_name, column_name
        FROM temp_tables_columns
        WHERE processed = 0
        ORDER BY schema_name, table_name, column_name;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        SET v_error_count = v_error_count + 1;
        GET DIAGNOSTICS CONDITION 1
            @error_msg = MESSAGE_TEXT;
        
        UPDATE cleaning_progress 
        SET status = 'FAILED', 
            end_time = NOW(),
            error_message = @error_msg
        WHERE schema_name = v_schema 
          AND table_name = v_table 
          AND column_name = v_column;
    END;
    
    SET v_start_time = NOW();
    
    -- 获取总数
    -- SELECT COUNT(*) INTO v_total FROM temp_tables_columns WHERE processed = 0;
    
    -- SELECT CONCAT('开始批量清洗，共 ', v_total, ' 个列需要处理') as start_info;
    
    OPEN table_cursor;
    read_loop: LOOP
        FETCH table_cursor INTO v_schema, v_table, v_column;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        SET v_counter = v_counter + 1;
        
        -- 更新进度
        UPDATE cleaning_progress 
        SET status = 'PROCESSING', start_time = NOW() 
        WHERE schema_name = v_schema 
          AND table_name = v_table 
          AND column_name = v_column;
        
        -- 执行清洗
        CALL CleanSingleColumn(v_schema, v_table, v_column);
        
        -- 更新完成状态
        UPDATE cleaning_progress 
        SET status = 'COMPLETED', end_time = NOW() 
        WHERE schema_name = v_schema 
          AND table_name = v_table 
          AND column_name = v_column
          AND status = 'PROCESSING';
        
        -- 标记为已处理
        UPDATE temp_tables_columns 
        SET processed = 1 
        WHERE schema_name = v_schema 
          AND table_name = v_table 
          AND column_name = v_column;
        
        -- 每处理50个显示进度
--         IF v_counter % 50 = 0 THEN
--             SELECT CONCAT('已处理 ', v_counter, '/', v_total, ' 个列 (',
--                          ROUND(v_counter * 100.0 / v_total, 2), '%)') as progress;
--         END IF;
        
    END LOOP;
    CLOSE table_cursor;
    
--     SELECT CONCAT('批量清洗完成！共处理 ', v_counter, ' 个列，错误 ', v_error_count, ' 个，',
--                   '耗时 ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') as completion_info;
    
END$$

-- 清洗指定schema的存储过程
DROP PROCEDURE IF EXISTS CleanSchema$$
CREATE PROCEDURE CleanSchema(IN p_schema_name VARCHAR(100))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_table VARCHAR(100);
    DECLARE v_column VARCHAR(100);
    DECLARE v_counter INT DEFAULT 0;
    
    DECLARE schema_cursor CURSOR FOR
        SELECT table_name, column_name
        FROM temp_tables_columns
        WHERE schema_name = p_schema_name AND processed = 0
        ORDER BY table_name, column_name;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
--     SELECT CONCAT('开始清洗 schema: ', p_schema_name) as start_info;
    
    OPEN schema_cursor;
    read_loop: LOOP
        FETCH schema_cursor INTO v_table, v_column;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        SET v_counter = v_counter + 1;
        
--         SELECT CONCAT('正在处理: ', p_schema_name, '.', v_table, '.', v_column) as current_task;
        
        -- 更新进度
        UPDATE cleaning_progress 
        SET status = 'PROCESSING', start_time = NOW() 
        WHERE schema_name = p_schema_name 
          AND table_name = v_table 
          AND column_name = v_column;
        
        -- 执行清洗
        CALL CleanSingleColumn(p_schema_name, v_table, v_column);
        
        -- 更新完成状态
        UPDATE cleaning_progress 
        SET status = 'COMPLETED', end_time = NOW() 
        WHERE schema_name = p_schema_name 
          AND table_name = v_table 
          AND column_name = v_column
          AND status = 'PROCESSING';
        
        -- 标记为已处理
        UPDATE temp_tables_columns 
        SET processed = 1 
        WHERE schema_name = p_schema_name 
          AND table_name = v_table 
          AND column_name = v_column;
        
    END LOOP;
    CLOSE schema_cursor;
    
--     SELECT CONCAT('完成清洗 schema: ', p_schema_name, '，共处理 ', v_counter, ' 个列') as completion_info;
    
END$$

-- 清洗指定表的存储过程
DROP PROCEDURE IF EXISTS CleanTable$$
CREATE PROCEDURE CleanTable(
    IN p_schema_name VARCHAR(100),
    IN p_table_name VARCHAR(100)
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_column VARCHAR(100);
    DECLARE v_counter INT DEFAULT 0;
    
    DECLARE table_cursor CURSOR FOR
        SELECT column_name
        FROM temp_tables_columns
        WHERE schema_name = p_schema_name 
          AND table_name = p_table_name 
          AND processed = 0
        ORDER BY column_name;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
--     SELECT CONCAT('开始清洗表: ', p_schema_name, '.', p_table_name) as start_info;
    
    OPEN table_cursor;
    read_loop: LOOP
        FETCH table_cursor INTO v_column;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        SET v_counter = v_counter + 1;
        
--         SELECT CONCAT('正在处理列: ', v_column) as current_column;
        
        -- 更新进度
        UPDATE cleaning_progress 
        SET status = 'PROCESSING', start_time = NOW() 
        WHERE schema_name = p_schema_name 
          AND table_name = p_table_name 
          AND column_name = v_column;
        
        -- 执行清洗
        CALL CleanSingleColumn(p_schema_name, p_table_name, v_column);
        
        -- 更新完成状态
        UPDATE cleaning_progress 
        SET status = 'COMPLETED', end_time = NOW() 
        WHERE schema_name = p_schema_name 
          AND table_name = p_table_name 
          AND column_name = v_column
          AND status = 'PROCESSING';
        
        -- 标记为已处理
        UPDATE temp_tables_columns 
        SET processed = 1 
        WHERE schema_name = p_schema_name 
          AND table_name = p_table_name 
          AND column_name = v_column;
        
    END LOOP;
    CLOSE table_cursor;
    
--     SELECT CONCAT('完成清洗表: ', p_schema_name, '.', p_table_name, '，共处理 ', v_counter, ' 个列') as completion_info;
    
END$$

-- 重新加载替换规则的存储过程
DROP PROCEDURE IF EXISTS ReloadReplacementRules$$
CREATE PROCEDURE ReloadReplacementRules()
BEGIN
    DECLARE v_rule_count INT DEFAULT 0;
    
    -- 重新创建替换函数以使用最新规则
    -- 注意：这里只是重新统计，实际的函数会自动使用最新的规则
    SELECT COUNT(*) INTO v_rule_count 
    FROM replacement_rules 
    WHERE is_active = 1;
    
--     SELECT CONCAT('已重新加载 ', v_rule_count, ' 条替换规则') as reload_info;
    
    -- 显示当前启用的规则
    SELECT 
        rule_category,
        COUNT(*) as rule_count
    FROM replacement_rules
    WHERE is_active = 1
    GROUP BY rule_category
    ORDER BY rule_category;
    
END$$

-- 获取清洗统计信息的存储过程
DROP PROCEDURE IF EXISTS GetCleaningStatistics$$
CREATE PROCEDURE GetCleaningStatistics()
BEGIN
    -- 总体统计
    SELECT 
        '总体统计' as section,
        COUNT(*) as total_tasks,
        SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks,
        SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_tasks,
        SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) as pending_tasks,
        SUM(records_updated) as total_records_updated
    FROM cleaning_progress;
    
    -- 按Schema统计
    SELECT 
        '按Schema统计' as section,
        schema_name,
        COUNT(*) as total_tasks,
        SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks,
        SUM(records_updated) as records_updated
    FROM cleaning_progress
    GROUP BY schema_name
    ORDER BY schema_name;
    
    -- 清洗记录统计
    SELECT 
        '清洗记录统计' as section,
        COUNT(*) as total_replacements,
        COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) as affected_tables,
        COUNT(DISTINCT biz_pk) as affected_records
    FROM data_cleaning_records;
    
    -- 按规则分类统计
    SELECT 
        '按规则分类统计' as section,
        SUBSTRING_INDEX(replace_rule, ',', 1) as main_rule,
        COUNT(*) as usage_count
    FROM data_cleaning_records
    WHERE replace_rule IS NOT NULL AND replace_rule != ''
    GROUP BY SUBSTRING_INDEX(replace_rule, ',', 1)
    ORDER BY usage_count DESC
    LIMIT 20;
    
END$$

DELIMITER ;

SELECT '=== MySQL 8.4.5 批量清洗存储过程已创建 ===' as status;
