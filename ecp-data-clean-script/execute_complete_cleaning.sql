-- 执行完整数据清洗的主脚本
-- 使用方法：
-- 1. 先执行 mysql_8_4_data_cleaning_complete.sql
-- 2. 再执行 mysql_8_4_cleaning_functions.sql  
-- 3. 再执行 mysql_8_4_batch_procedures.sql
-- 4. 最后执行此脚本

USE ecp_data_cleaning;

-- 设置会话参数
SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';
SET SESSION autocommit = 1;
SET SESSION innodb_lock_wait_timeout = 600;
SET SESSION max_execution_time = 0;

-- 显示开始信息
SELECT '=== 开始执行完整数据清洗任务 ===' as title;
SELECT NOW() as start_time;

-- 显示替换规则信息
SELECT '=== 当前替换规则 ===' as rules_title;
SELECT 
    rule_category,
    rule_name,
    search_pattern,
    replace_value,
    sort_order
FROM replacement_rules
WHERE is_active = 1
ORDER BY rule_category, sort_order;

-- 显示替换规则统计
SELECT 
    '替换规则统计' as section,
    rule_category,
    COUNT(*) as rule_count
FROM replacement_rules
WHERE is_active = 1
GROUP BY rule_category
ORDER BY rule_category;

-- 显示将要处理的表和列统计
SELECT 
    '待处理统计' as section,
    schema_name,
    COUNT(*) as column_count,
    COUNT(DISTINCT table_name) as table_count
FROM temp_tables_columns
WHERE processed = 0
GROUP BY schema_name
ORDER BY schema_name;

-- 显示总体统计
SELECT 
    '总体待处理' as section,
    COUNT(*) as total_columns,
    COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) as total_tables,
    COUNT(DISTINCT schema_name) as total_schemas
FROM temp_tables_columns
WHERE processed = 0;

-- 测试替换函数
SELECT '=== 测试替换函数 ===' as test_title;

SELECT 
    '测试文本1' as test_type,
    'JLR捷豹路虎APPD联通' as original_text,
    apply_all_replacements('JLR捷豹路虎APPD联通') as replaced_text,
    needs_replacement('JLR捷豹路虎APPD联通') as needs_replace;

SELECT 
    '测试文本2' as test_type,
    'Jaguar Landrover PIVI CCCM ForgeRock' as original_text,
    apply_all_replacements('Jaguar Landrover PIVI CCCM ForgeRock') as replaced_text,
    needs_replacement('Jaguar Landrover PIVI CCCM ForgeRock') as needs_replace;

SELECT 
    '测试文本3' as test_type,
    'JLR User使用TSDP系统访问Range Rover配置' as original_text,
    apply_all_replacements('JLR User使用TSDP系统访问Range Rover配置') as replaced_text,
    needs_replacement('JLR User使用TSDP系统访问Range Rover配置') as needs_replace;

-- 执行清洗选项说明
SELECT '=== 清洗执行选项 ===' as options_title;
SELECT '选项1: 执行全部清洗 - CALL BatchCleanAllTables();' as option1;
SELECT '选项2: 按schema清洗 - CALL CleanSchema(''schema_name'');' as option2;
SELECT '选项3: 按表清洗 - CALL CleanTable(''schema'', ''table'');' as option3;
SELECT '选项4: 单个列清洗 - CALL CleanSingleColumn(''schema'', ''table'', ''column'');' as option4;

-- 显示当前进度
SELECT '=== 当前清洗进度 ===' as current_progress_title;

SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM cleaning_progress), 2) as percentage
FROM cleaning_progress
GROUP BY status
ORDER BY 
    CASE status 
        WHEN 'COMPLETED' THEN 1
        WHEN 'PROCESSING' THEN 2
        WHEN 'FAILED' THEN 3
        WHEN 'PENDING' THEN 4
        ELSE 5
    END;

-- 执行建议
SELECT '=== 执行建议 ===' as suggestions_title;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM cleaning_progress WHERE status = 'PENDING') > 0 
        THEN CONCAT('建议执行: CALL BatchCleanAllTables(); 来处理剩余的 ', 
                   (SELECT COUNT(*) FROM cleaning_progress WHERE status = 'PENDING'), ' 个待处理任务')
        ELSE '所有清洗任务已完成'
    END as suggestion;

-- 分schema执行示例（可以取消注释分别执行）
/*
-- 按优先级分schema执行清洗

-- 1. 清洗 ecp_consumer schema（消费者相关）
SELECT '=== 开始清洗 ecp_consumer schema ===' as progress;
CALL CleanSchema('ecp_consumer');

-- 2. 清洗 ecp_notification schema（通知相关）
SELECT '=== 开始清洗 ecp_notification schema ===' as progress;
CALL CleanSchema('ecp_notification');

-- 3. 清洗 ecp_order schema（订单相关）
SELECT '=== 开始清洗 ecp_order schema ===' as progress;
CALL CleanSchema('ecp_order');

-- 4. 清洗 ecp_product schema（产品相关）
SELECT '=== 开始清洗 ecp_product schema ===' as progress;
CALL CleanSchema('ecp_product');

-- 5. 清洗 ecp_subscription schema（订阅相关）
SELECT '=== 开始清洗 ecp_subscription schema ===' as progress;
CALL CleanSchema('ecp_subscription');

-- 6. 清洗 ecp_payment schema（支付相关）
SELECT '=== 开始清洗 ecp_payment schema ===' as progress;
CALL CleanSchema('ecp_payment');

-- 7. 清洗 ecp_system schema（系统相关）
SELECT '=== 开始清洗 ecp_system schema ===' as progress;
CALL CleanSchema('ecp_system');

-- 8. 清洗其他schema
SELECT '=== 开始清洗 ecp_fulfillment schema ===' as progress;
CALL CleanSchema('ecp_fulfillment');

SELECT '=== 开始清洗 ecp_integration schema ===' as progress;
CALL CleanSchema('ecp_integration');

SELECT '=== 开始清洗 ecp_inventory schema ===' as progress;
CALL CleanSchema('ecp_inventory');

SELECT '=== 开始清洗 ecp_logistics schema ===' as progress;
CALL CleanSchema('ecp_logistics');

SELECT '=== 开始清洗 ecp_message schema ===' as progress;
CALL CleanSchema('ecp_message');

SELECT '=== 开始清洗 ecp_report schema ===' as progress;
CALL CleanSchema('ecp_report');

SELECT '=== 开始清洗 ecp_virtual schema ===' as progress;
CALL CleanSchema('ecp_virtual');

SELECT '=== 开始清洗 ecp_xxl_job schema ===' as progress;
CALL CleanSchema('ecp_xxl_job');

SELECT '=== 开始清洗 jlr_ecp_test schema ===' as progress;
CALL CleanSchema('jlr_ecp_test');
*/

-- 单表执行示例（可以取消注释执行）
/*
-- 清洗重要的表

-- 清洗消费者渠道表
SELECT '=== 清洗 ecp_consumer.t_consumer_channel ===' as table_progress;
CALL CleanTable('ecp_consumer', 't_consumer_channel');

-- 清洗消费者信息表
SELECT '=== 清洗 ecp_consumer.t_consumer_info ===' as table_progress;
CALL CleanTable('ecp_consumer', 't_consumer_info');

-- 清洗通知配置表
SELECT '=== 清洗 ecp_notification.t_notification_config ===' as table_progress;
CALL CleanTable('ecp_notification', 't_notification_config');

-- 清洗订单通知详情表
SELECT '=== 清洗 ecp_notification.t_order_notification_detail ===' as table_progress;
CALL CleanTable('ecp_notification', 't_order_notification_detail');
*/

-- 显示执行完成后的统计
SELECT '=== 当前状态总结 ===' as summary_title;

SELECT 
    CONCAT(
        '总任务: ', (SELECT COUNT(*) FROM cleaning_progress), ', ',
        '已完成: ', (SELECT COUNT(*) FROM cleaning_progress WHERE status = 'COMPLETED'), ', ',
        '处理中: ', (SELECT COUNT(*) FROM cleaning_progress WHERE status = 'PROCESSING'), ', ',
        '失败: ', (SELECT COUNT(*) FROM cleaning_progress WHERE status = 'FAILED'), ', ',
        '待处理: ', (SELECT COUNT(*) FROM cleaning_progress WHERE status = 'PENDING')
    ) as status_summary;

SELECT 
    CONCAT(
        '清洗记录: ', IFNULL((SELECT COUNT(*) FROM data_cleaning_records), 0), ' 条, ',
        '影响表: ', IFNULL((SELECT COUNT(DISTINCT CONCAT(schema_name, '.', table_name)) FROM data_cleaning_records), 0), ' 个'
    ) as cleaning_summary;

-- 显示最近的清洗记录示例
SELECT 
    '最近清洗记录示例' as section,
    CONCAT(schema_name, '.', table_name, '.', column_name) as location,
    LEFT(src_content, 50) as original_content,
    LEFT(replace_content, 50) as new_content,
    replace_rule,
    created_time
FROM data_cleaning_records
ORDER BY created_time DESC
LIMIT 20;

-- 显示失败的任务
SELECT 
    '失败的任务' as section,
    CONCAT(schema_name, '.', table_name, '.', column_name) as failed_task,
    error_message,
    start_time
FROM cleaning_progress
WHERE status = 'FAILED'
ORDER BY start_time DESC;

-- 最终提示
SELECT '=== 执行提示 ===' as final_tips;
SELECT '1. 如需执行全部清洗，请运行: CALL BatchCleanAllTables();' as tip1;
SELECT '2. 如需分schema执行，请取消注释相应的CALL CleanSchema语句' as tip2;
SELECT '3. 如需分表执行，请取消注释相应的CALL CleanTable语句' as tip3;
SELECT '4. 执行完成后，请运行验证脚本检查结果' as tip4;
SELECT '5. 如需修改替换规则，请更新replacement_rules表' as tip5;
