-- MySQL 8.4.5 数据清洗函数和存储过程
USE ecp_data_cleaning;

DELIMITER $$

-- 动态应用所有替换规则的函数
DROP FUNCTION IF EXISTS apply_all_replacements$$
CREATE FUNCTION apply_all_replacements(input_text TEXT) 
RETURNS TEXT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result_text TEXT;
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_search_pattern VARCHAR(500);
    DECLARE v_replace_value VARCHAR(500);
    
    DECLARE rule_cursor CURSOR FOR
        SELECT search_pattern, replace_value
        FROM replacement_rules
        WHERE is_active = 1
        ORDER BY rule_category, sort_order;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    SET result_text = IFNULL(input_text, '');
    
    IF result_text = '' THEN
        RETURN result_text;
    END IF;
    
    -- 遍历所有启用的替换规则
    OPEN rule_cursor;
    rule_loop: LOOP
        FETCH rule_cursor INTO v_search_pattern, v_replace_value;
        IF done THEN
            LEAVE rule_loop;
        END IF;
        
        -- 应用替换规则
        SET result_text = REPLACE(result_text, v_search_pattern, v_replace_value);
    END LOOP;
    CLOSE rule_cursor;
    
    RETURN result_text;
END$$

-- 检查文本是否需要替换的函数
DROP FUNCTION IF EXISTS needs_replacement$$
CREATE FUNCTION needs_replacement(input_text TEXT) 
RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_search_pattern VARCHAR(500);
    DECLARE pattern_found BOOLEAN DEFAULT FALSE;
    
    DECLARE pattern_cursor CURSOR FOR
        SELECT search_pattern
        FROM replacement_rules
        WHERE is_active = 1;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    IF input_text IS NULL OR input_text = '' THEN
        RETURN FALSE;
    END IF;
    
    -- 检查是否包含任何需要替换的模式
    OPEN pattern_cursor;
    check_loop: LOOP
        FETCH pattern_cursor INTO v_search_pattern;
        IF done THEN
            LEAVE check_loop;
        END IF;
        
        IF input_text LIKE CONCAT('%', v_search_pattern, '%') THEN
            SET pattern_found = TRUE;
            LEAVE check_loop;
        END IF;
    END LOOP;
    CLOSE pattern_cursor;
    
    RETURN pattern_found;
END$$

-- 获取应用的替换规则列表
DROP FUNCTION IF EXISTS get_applied_rules$$
CREATE FUNCTION get_applied_rules(original_text TEXT, new_text TEXT) 
RETURNS VARCHAR(1000)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE rules_applied VARCHAR(1000) DEFAULT '';
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_rule_name VARCHAR(100);
    DECLARE v_search_pattern VARCHAR(500);
    
    DECLARE rule_cursor CURSOR FOR
        SELECT rule_name, search_pattern
        FROM replacement_rules
        WHERE is_active = 1
        ORDER BY rule_category, sort_order;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    IF original_text = new_text OR original_text IS NULL OR new_text IS NULL THEN
        RETURN '';
    END IF;
    
    -- 检查哪些规则被应用了
    OPEN rule_cursor;
    rule_loop: LOOP
        FETCH rule_cursor INTO v_rule_name, v_search_pattern;
        IF done THEN
            LEAVE rule_loop;
        END IF;
        
        IF original_text LIKE CONCAT('%', v_search_pattern, '%') THEN
            IF rules_applied = '' THEN
                SET rules_applied = v_rule_name;
            ELSE
                SET rules_applied = CONCAT(rules_applied, ',', v_rule_name);
            END IF;
        END IF;
    END LOOP;
    CLOSE rule_cursor;
    
    RETURN rules_applied;
END$$

-- 清洗单个列的存储过程
DROP PROCEDURE IF EXISTS CleanSingleColumn$$
CREATE PROCEDURE CleanSingleColumn(
    IN p_schema_name VARCHAR(100),
    IN p_table_name VARCHAR(100),
    IN p_column_name VARCHAR(100)
)
BEGIN
    DECLARE v_pk_column VARCHAR(100) DEFAULT 'id';
    DECLARE v_count INT DEFAULT 0;
    DECLARE v_updated INT DEFAULT 0;
    DECLARE v_error_msg TEXT DEFAULT '';
    DECLARE v_batch_size INT DEFAULT 1000;
    DECLARE v_offset INT DEFAULT 0;
    DECLARE v_has_more BOOLEAN DEFAULT TRUE;
    
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        GET DIAGNOSTICS CONDITION 1
            v_error_msg = MESSAGE_TEXT;
        
        UPDATE cleaning_progress 
        SET status = 'FAILED', 
            end_time = NOW(),
            error_message = v_error_msg
        WHERE schema_name = p_schema_name 
          AND table_name = p_table_name 
          AND column_name = p_column_name;
          
--         SELECT CONCAT('错误处理 ', p_schema_name, '.', p_table_name, '.', p_column_name, ': ', v_error_msg) as error_info;
    END;
    
    -- 获取主键列名
    SELECT COLUMN_NAME INTO v_pk_column
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = p_schema_name
      AND TABLE_NAME = p_table_name
      AND CONSTRAINT_NAME = 'PRIMARY'
    LIMIT 1;
    
    -- 如果没有主键，尝试使用id列
    IF v_pk_column IS NULL THEN
        SELECT COLUMN_NAME INTO v_pk_column
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = p_schema_name
          AND TABLE_NAME = p_table_name
          AND COLUMN_NAME = 'id'
        LIMIT 1;
    END IF;
    
    -- 如果还是没有，使用第一个列
    IF v_pk_column IS NULL THEN
        SELECT COLUMN_NAME INTO v_pk_column
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = p_schema_name
          AND TABLE_NAME = p_table_name
        ORDER BY ORDINAL_POSITION
        LIMIT 1;
    END IF;
    
    -- 创建临时表存储需要更新的数据
    DROP TEMPORARY TABLE IF EXISTS temp_update_data;
    CREATE TEMPORARY TABLE temp_update_data (
        pk_value VARCHAR(500),
        original_value TEXT,
        new_value TEXT,
        applied_rules VARCHAR(1000),
        INDEX idx_pk (pk_value)
    );
    
    -- 分批处理数据
    batch_loop: WHILE v_has_more DO
        -- 清空临时表
        TRUNCATE TABLE temp_update_data;
        
        -- 构建查询SQL并插入到临时表
        SET @sql = CONCAT(
            'INSERT INTO temp_update_data (pk_value, original_value, new_value, applied_rules) ',
            'SELECT CAST(`', v_pk_column, '` AS CHAR), `', p_column_name, '`, ',
            'apply_all_replacements(`', p_column_name, '`), ',
            'get_applied_rules(`', p_column_name, '`, apply_all_replacements(`', p_column_name, '`)) ',
            'FROM `', p_schema_name, '`.`', p_table_name, '` ',
            'WHERE `', p_column_name, '` IS NOT NULL ',
            'AND needs_replacement(`', p_column_name, '`) ',
            'LIMIT ', v_batch_size, ' OFFSET ', v_offset
        );
        
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 检查是否有数据
        SELECT COUNT(*) INTO v_count FROM temp_update_data;
        
        IF v_count = 0 THEN
            SET v_has_more = FALSE;
        ELSE
            -- 开始事务处理当前批次
            START TRANSACTION;
            
            -- 更新原表数据
            SET @update_sql = CONCAT(
                'UPDATE `', p_schema_name, '`.`', p_table_name, '` t ',
                'INNER JOIN temp_update_data tmp ON CAST(t.`', v_pk_column, '` AS CHAR) = tmp.pk_value ',
                'SET t.`', p_column_name, '` = tmp.new_value ',
                'WHERE tmp.original_value != tmp.new_value'
            );
            
            PREPARE stmt FROM @update_sql;
            EXECUTE stmt;
            SET v_updated = v_updated + ROW_COUNT();
            DEALLOCATE PREPARE stmt;
            
            -- 记录清洗操作
            INSERT INTO data_cleaning_records (
                schema_name, table_name, column_name, biz_pk,
                src_content, replace_content, replace_rule
            )
            SELECT 
                p_schema_name, 
                p_table_name, 
                p_column_name, 
                pk_value,
                original_value, 
                new_value, 
                applied_rules
            FROM temp_update_data
            WHERE original_value != new_value;
            
            COMMIT;
            
            -- 更新偏移量
            SET v_offset = v_offset + v_batch_size;
            
            -- 如果处理的记录数少于批次大小，说明已经处理完
            IF v_count < v_batch_size THEN
                SET v_has_more = FALSE;
            END IF;
        END IF;
        
    END WHILE;
    
    -- 更新进度记录
    UPDATE cleaning_progress 
    SET records_updated = v_updated
    WHERE schema_name = p_schema_name 
      AND table_name = p_table_name 
      AND column_name = p_column_name;
    
    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS temp_update_data;
    
--     SELECT CONCAT('完成清洗 ', p_schema_name, '.', p_table_name, '.', p_column_name,
--                   '，更新了 ', v_updated, ' 条记录') as result;
    
END$$

DELIMITER ;

SELECT '=== MySQL 8.4.5 清洗函数和存储过程已创建 ===' as status;
