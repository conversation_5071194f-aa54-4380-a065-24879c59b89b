version: '3.8'

services:
  nacos:
    image: nacos/nacos-server:v2.5.0
    container_name: ecp-nacos
    ports:
      - "8848:8848"
      - "9848:9848"
    environment:
      - TZ=Asia/Shanghai
      - PREFER_HOST_MODE=hostname
      - MODE=standalone

      # JVM参数优化 - 解决内存问题
      - JVM_XMS=512m          # 初始堆内存
      - JVM_XMX=1g            # 最大堆内存
      - JVM_XMN=256m          # 新生代内存
      - JVM_MS=128m           # 元空间初始大小

      - JVM_MMS=320m          # 元空间最大大小
      # GC优化

      - NACOS_OPTS=-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap
      # Nacos配置优化
      - NACOS_SERVER_PORT=8848
      - NACOS_APPLICATION_PORT=8848

      # 连接池配置
      - TOMCAT_MAX_THREADS=200
      - TOMCAT_MIN_SPARE_THREADS=50
      - TOMCAT_MAX_CONNECTIONS=10000

      # 日志配置
      - NACOS_DEBUG=false
      - TOMCAT_ACCESSLOG_ENABLED=true

      # 安全配置
      - NACOS_AUTH_ENABLE=false
      - NACOS_AUTH_TOKEN_EXPIRE_SECONDS=18000

      # 禁用自定义日志配置
      - LOGGING_CONFIG=

    volumes:
      - ${ROOT_ENV_DIR}/ecp-nacos/data:/home/<USER>/data
      - ${ROOT_ENV_DIR}/ecp-nacos/logs:/home/<USER>/logs
      # 添加时区文件
      - /etc/localtime:/etc/localtime:ro

    # 资源限制 - 防止内存溢出
    deploy:
      resources:
        limits:
          memory: 1024M
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

    # 健康检查 - 自动检测服务状态
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8848/nacos/v1/console/health/readiness || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    # 重启策略 - 服务死掉自动重启
    restart: unless-stopped

